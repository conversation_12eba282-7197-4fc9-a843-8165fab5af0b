const path = require("path");
const assetsPublicPath = "/";
const PORT = process.env.PORT || 8001;
const sourcePrefix = "bifrost-resource/";
const publicPath = "/bifrost-resource/";
module.exports = {
	projectCode: "bifrost",
	publicPath,
	common: {
		htmlTemplatePath: path.resolve(__dirname, "../src/index.ejs"),
		sourcePrefix: sourcePrefix
	},
	dev: {
		hot: true,
		assetsSubDirectory: sourcePrefix + "/static",
		assetsPublicPath: "/",
		// assetsPublicPath: publicPath,
		proxyTable: {
			"/bridgeApi": {
				// target: "http://***********:8092",
				target: "http://***********:8088",
				// target: "http://************:8088",
				// target: "http://************:8090",
				// target: "http://*************:8090",
				changeOrigin: true,
				pathRewrite: {
					// "^/bridgeApi": "/bridgeApi"
				}
			}
		},
		host: "0.0.0.0",
		port: PORT,
		autoOpenBrowser: true,
		devtool: "eval-source-map",
		publicPath: publicPath
	},
	build: {
		assetsRoot: path.resolve(__dirname, "../dist"),
		assetsSubDirectory: sourcePrefix + "/static",
		assetsPublicPath,
		devtool: "source-map"
	}
};
