FROM registry.tongdun.me/library/centos7-common-nginx:1.0
ENV APPNAME=${APPNAME}

ADD nginx.conf /etc/nginx/
ADD ok.htm /home/<USER>/
COPY /dist/ /home/<USER>/
RUN mkdir /home/<USER>/nginx
RUN mkdir /home/<USER>/nginx/conf
RUN mkdir /home/<USER>/$APPNAME
ADD validate.sh /home/<USER>/$APPNAME
RUN mkdir /home/<USER>/output
RUN mkdir /home/<USER>/output/$APPNAME
RUN mkdir /home/<USER>/output/$APPNAME/logs

CMD ["/bin/bash", "-c", "nginx"]
