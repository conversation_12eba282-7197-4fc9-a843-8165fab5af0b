user  root;
worker_processes  4;

error_log  /home/<USER>/output/bifrost-react/logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

pid        /home/<USER>/output/bifrost-react/logs/nginx.pid;


events {
    worker_connections  1024;
}
daemon          off;


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /home/<USER>/output/bifrost-react/logs/access.log;
    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    client_max_body_size 1900M;
    client_body_buffer_size 128k;
    #gzip  on;
    gzip  on;
    gzip_min_length 5k;
    gzip_buffers 4 16k;
    gzip_http_version 1.0;
    gzip_comp_level 3;
    gzip_types text/plain application/javascript application/css  text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";


    server {
        # 静态资源根路径变量
        set $root /home/<USER>
        # 统一登录静态资源路径
        listen 8088;
        server_name bifrost-react.com;
        # 静态资源根路径，（后面相关前端静态资源都放单该路径下）
        root /home/<USER>

        #重定向到默认首页
        location / {
            index  index.html index.htm;
        }

        location ^~ /user {
            index  index.html index.htm;
            try_files $uri/ /bifrost-resource/index.html;
        }

        location ^~ /bridge/index.html {
            index  index.html index.htm;
            try_files $uri/ /bifrost-resource/index.html;
        }

    }
}
