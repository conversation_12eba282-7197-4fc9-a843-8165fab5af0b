{"presets": [["@babel/preset-env", {"corejs": {"version": 3}, "useBuiltIns": "usage", "targets": {"ie": 9, "chrome": 52, "safari": 10, "opera": 32, "firefox": 30}}], "@babel/preset-react"], "env": {"development": {"plugins": []}, "production": {"plugins": [["transform-remove-console", {"exclude": ["error", "warn", "info"]}]]}}, "plugins": [["@babel/plugin-proposal-decorators", {"legacy": true}], "@babel/plugin-syntax-dynamic-import", "@babel/plugin-syntax-import-meta", ["@babel/plugin-proposal-class-properties", {"loose": true}], "@babel/plugin-transform-runtime", ["import", {"libraryName": "antd", "libraryDirectory": "es", "style": true}, "antd"], ["import", {"libraryName": "tntd", "libraryDirectory": "lib", "camel2DashComponentName": false}, "tntd"], "lodash"]}