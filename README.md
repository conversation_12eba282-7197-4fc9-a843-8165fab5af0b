#### 2.4.0-unified 分支之后
- 分支统一切换到tntd-layout<删除老的layout代码>；
- 默认为暗黑风格<代码可查看Layout>；
- 对样式做了一些优化。

<br/>

```javascript
"build:default":"cross-env SYS_ENV=production DEFAULT_THEME=true BABEL_ENV=production webpack --progress --config build/webpack.prod.conf.js",
```
如果想要切换回老的经典风格，增加了`DEFAULT_THEME=true`。npm run build:default。


<br/>

针对部分特殊场景，例如超时未操作的时间限制。有些系统可能是半小时，但有些可能要求1小时。另拉分支的方式，存在版本迭代的合并问题。所以这里将此类问题，通过process.env的方式传参数。默认半小时。各业务系统有特殊的需求哦，则通过参数配置，具体可参考 `build:tntd:model`，指定`OPERATE_TIME=3600000`

<br/>

那么部署之后，我如何查看我使用的哪个命令打包？

`/dist/bifrost-resource`下有一个`branch_info.txt`文件，记录了一些基础信息，可以通过git信息、
执行脚本的内容，来告知第2、3、4....个同学，你要用哪个分支，哪个脚本打包。
