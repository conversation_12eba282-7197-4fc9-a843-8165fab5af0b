import { useEffect, Fragment } from "react";
import { connect } from "dva";
import { withRouter, matchPath } from "dva/router";
import { Config<PERSON>rovider, Spin } from "antd";
import zhCN from "antd/es/locale/zh_CN";
import { Layout, DevelopmentLogin, AuthContext } from "tntd";
import DocumentTitle from "react-document-title";
import { get } from "lodash";
import Cookies from "universal-cookie";

import { NoOperate, MultiUser } from "@tntd/user-status-modal";
import { findMenuInfoByPath } from "@/utils/utils";
import { publicPath } from "../build/config";

const { HeaderActionItem } = Layout;

const Shell = ({ globalStore = {}, dispatch, history, children, actions }) => {

	const { userInfoMode: userInfo = {}, currentApp, appList, customTree = {}, showApp, multiUserModal, menuTreeReady, userReady } = globalStore;
	const { menuTree = [], name, enName, logo, extendMap = {} } = customTree || {};
	const needAuth = !(["/user/login", "/user/startup"].indexOf(window.location.pathname) > -1);

	useEffect(() => {
		// 如果没有csrf则默认跳转到登录页面
		if (!sessionStorage.getItem("_csrf_")) {
			if (needAuth) {
				const { origin, pathname, search } = window.location || {};
				if (search) {
					const callbackUrl = origin + pathname + encodeURIComponent(search);
					window.location = "/user/login?callbackUrl=" + callbackUrl;
				} else {
					window.location = "/user/login";
				}
			}
		}
		if (needAuth) {
			dispatch({
				type: "global/getUserInfo",
				actions
			});
			dispatch({
				type: "global/getUserMenuTree",
				actions
			});
		}

		// 在lightbox中,需要使用事件方式监听数据变化
		const { CURRENT_APP_CHANGE, LANGUAGE_CHANGE, ON_LOGOUT } = actions ? actions.EVENTS_ENUM : {};
		if (actions) {
			actions.on(CURRENT_APP_CHANGE, onAppChange);
			actions.on(LANGUAGE_CHANGE, onLanguageChange);
			actions.on(ON_LOGOUT, onLogout);
		}
		return () => {
			if (actions) {
				actions.off(CURRENT_APP_CHANGE, onAppChange);
				actions.off(LANGUAGE_CHANGE, onLanguageChange);
				actions.off(ON_LOGOUT, onLogout);
			}
		};

	}, []);

	// 获取菜单key
	const getSelecteMenuKey = () => {
		const { subMenu } = findMenuInfoByPath(menuTree, location.pathname, location);
		return get(subMenu, "code");
	};

	const getPageTitle = () => {
		let { location } = history;
		let { pathname } = location;
		let title;

		menuTree &&
            menuTree.length > 0 &&
            menuTree.map((item) => {
            	item.children &&
                    item.children.map((subItem) => {
                    	if (pathname.includes(subItem.path)) {
                    		title = subItem["menuName"];
                    	}
                    });
            });

		return title ? title : "统一登录";
	};

	// 应用切换
	const onAppChange = (app) => {
		dispatch({
			type: "global/setAttrValue",
			payload: {
				currentApp: app
			}
		});
	};
	// 语言切换
	const onLanguageChange = (language) => {
		let { personalMode } = globalStore;
		personalMode.lang = language;
		dispatch({
			type: "global/setAttrValue",
			payload: {
				personalMode
			}
		});
		localStorage.setItem("lang", language);
		const cookies = new Cookies();
		cookies.set("lang", language, { path: "/" });
	};
	// 登出
	const onLogout = () => {
		dispatch({
			type: "login/logout"
		});
	};

	// 格式化地址
	const formatMenuPath = path => {
		if (path.indexOf("{esAuth}") > -1) {
			const csrf = encodeURIComponent(sessionStorage.getItem("_csrf_"));
			const ac = userInfo?.token;

			return path.replace("{esAuth}", `ac=${ac}&cf=${csrf}`);
		}

		return path;
	};

	// 缺省空白页面
	const isEmptyLayout = ["/user/login", "/user/startup"].find(path => matchPath(location.pathname, { path }));
	let theme = {};
	if (process.env.DEFAULT_THEME) {
		theme = { compatible: true };
	}
	return (
		<ConfigProvider locale={zhCN} language="cn">
			<DocumentTitle title={getPageTitle()}>
				<Layout
					{...theme}
					className="tntd-layout"
					key={get(currentApp, "key")}
					appKey="bridge"
					language="cn"
					name={name}
					enName={enName}
					logo={
						<img
							className="logo"
							style={{ opacity: logo && logo.indexOf("white") ? 0.85 : 1 }}
							src={`${publicPath}logo/${logo}`}
							onError={(e) => {
								e.target.onerror = null;
								e.target.src = `${publicPath}logo/logo11-white.svg`;
							}}
						/>
					}
					isEmptyLayout={window.isInLightBox || isEmptyLayout}
					userInfo={userInfo}
					menus={menuTree}
					appList={showApp && appList}
					selectedMenuKey={getSelecteMenuKey()}
					formatMenuPath={formatMenuPath}
					changeRouter={path => history.push(path)}
					extendMap={extendMap}
					// 应用切换
					onAppChange={onAppChange}
					// 语言切换
					onLanguageChange={onLanguageChange}
					// 退出系统
					onLogout={onLogout}
					// 开发模式增加登录
					extralHeaderActions={[
						process.env.SYS_ENV === "development" && (
							<HeaderActionItem
								key="help"
								onClick={() => { }}
							>
								<DevelopmentLogin
									signIn={
										(params) => dispatch({
											type: "login/mockLogin",
											payload: params
										})
									}
								/>
							</HeaderActionItem>
						)
					]}
				>
					<AuthContext.Consumer>
						{
							auth => {
								window.auth = auth;
								return (
									(
										(menuTreeReady && userReady) || !needAuth
									)
										? children
										: <Spin className="globalSpin" />
								);
							}
						}
					</AuthContext.Consumer>
					{
						needAuth &&
                        <Fragment>
                        	{/* 长时间登陆无任何操作，弹框提示并跳转登陆页 */}
                        	<NoOperate
                        		lang="cn"
                        		noOperateTime={process.env.OPERATE_TIME || 1800000}
                        		modalShowEvent={() => {
                        			// 弹窗回调
                        			dispatch({
                        				type: "login/signOut"
                        			});
                        		}}
                        		modalCloseEvent={() => {
                        			// 点击弹窗确定回调
                        			dispatch({
                        				type: "login/goLogin"
                        			});
                        		}}
                        	/>

                        	{/* 多终端登录 */}
                        	<MultiUser
                        		lang="cn"
                        		showModal={multiUserModal}
                        		modalShowEvent={() => {
                        			// 弹窗回调
                        			dispatch({
                        				type: "login/signOut"
                        			});
                        		}}
                        		modalCloseEvent={() => {
                        			// 点击弹窗确定回调
                        			dispatch({
                        				type: "login/goLogin"
                        			});
                        			dispatch({
                        				type: "global/setAttrValue",
                        				payload: {
                        					multiUserModal: false
                        				}
                        			});
                        		}}
                        	/>
                        </Fragment>
					}

				</Layout>
			</DocumentTitle>
		</ConfigProvider>
	);

};

export default withRouter(
	connect(state => ({
		globalStore: state.global
	}))(Shell)
);
