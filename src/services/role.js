import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";

const getOrgList = async(param) => {
	return request(getUrl("/org", param), {
		method: "GET",
		headers: getHeader()
	});
};

const getRoleByOrg = async(param) => {
	return request(getUrl("/role/list", param), {
		method: "GET",
		headers: getHeader()
	});
};
const addRole = async(params) => {
	return request("/role", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};
const modifyRole = async(params) => {
	return request("/role", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};
const deleteRole = async(uuid) => {
	return request("/role?uuid=" + uuid, {
		method: "DELETE",
		headers: getHeader()
	});
};

const getRolePermission = async(param) => {
	return request(getUrl("/role/menu", param), {
		method: "GET",
		headers: getHeader()
	});
};
const saveRolePermission = async(param) => {
	return request("/role/menu", {
		method: "POST",
		headers: getHeader(),
		body: { ...param }
	});
};

export default {
	getOrgList,
	getRoleByOrg,
	addRole,
	modifyRole,
	deleteRole,
	getRolePermission,
	saveRolePermission
};
