import { getHeader, getUrl, deleteEmptyObjItem } from "../common";
import request from "@/utils/request";

const getApplicationList = async(params) => {
	return request(getUrl("/app/list", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getApplicationDetail = async(params) => {
	return request(getUrl("/app", params), {
		method: "GET",
		headers: getHeader()
	});
};

const addApplication = async(params) => {
	params = deleteEmptyObjItem(params);
	return request("/app", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const modifyApplication = async(params) => {
	return request("/app", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const deleteApplication = async(params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/app", params), {
		method: "DELETE",
		headers: getHeader()
	});
};

export default {
	getApplicationList,
	getApplicationDetail,
	addApplication,
	modifyApplication,
	deleteApplication
};
