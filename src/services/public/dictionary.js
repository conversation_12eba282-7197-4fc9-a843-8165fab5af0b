import { getHeader, getUrl, deleteEmptyObjItem } from "../common";
import request from "../../utils/request";

const getDictionaryList = async(param) => {
	return request(getUrl("/dictionary/list", param), {
		method: "GET",
		headers: getHeader()
	});
};

const addDictionary = async(params) => {
	return request("/dictionary/create", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const modifyDictionary = async(params) => {
	return request("/dictionary/update", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
	// return request("/dictionary", {
	// 	method: "PUT",
	// 	headers: getHeader(),
	// 	body: { ...params }
	// });
};

const deleteDictionary = async(param) => {
	return request("/dictionary/delete", {
		method: "POST",
		headers: getHeader(),
		body: {
			...param
		}
	});
};

// 获取字典，不校验权限
const getDictionaryType = async(dictionaryType) => {
	return request(`/dictionary/value/${dictionaryType}`, {
		method: "GET",
		headers: getHeader()
	});
};

// 展示表单子弹，不校验权限
const getDictionaryForm = async() => {
	return request("/dictionary/auditForm", {
		method: "GET",
		headers: getHeader()
	});
};
export default {
	getDictionaryList,
	addDictionary,
	modifyDictionary,
	deleteDictionary,
	getDictionaryType,
	getDictionaryForm
};
