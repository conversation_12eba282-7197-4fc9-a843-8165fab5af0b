import { getHeader, getUrl, deleteEmptyObjItem } from "../common";
import request from "../../utils/request";

const getConfigList = async(param) => {
	return request(getUrl("/system/config/list", param), {
		method: "GET",
		headers: getHeader()
	});
};

const getConfigDetail = async(param) => {
	return request(getUrl("/system/config/detail", param), {
		method: "GET",
		headers: getHeader()
	});
};

const addConfig = async(params) => {
	return request("/system/config/create", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const modifyConfig = async(params) => {
	// /system/config PUT
	return request("/system/config/update", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const deleteConfig = async(param) => {
	return request("/system/config/delete", {
		method: "DELETE",
		headers: getHeader(),
		body: {
			...param
		}
	});
};

export default {
	getConfigList,
	getConfigDetail,
	addConfig,
	modifyConfig,
	deleteConfig
};
