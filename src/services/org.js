import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";

const getOrgList = async(param) => {
	return request(getUrl("/org", param), {
		method: "GET",
		headers: getHeader()
	});
};

const addOrg = async(params) => {
	return request("/org", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const modifyOrg = async(params) => {
	return request("/org", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const deleteOrg = async(param) => {
	return request(getUrl("/org", param), {
		method: "DELETE",
		headers: getHeader()
	});
};

const getOrgPermission = async(param) => {
	return request(getUrl("/org/menu", param), {
		method: "GET",
		headers: getHeader()
	});
};
const saveOrgPermission = async(param) => {
	return request("/org/menu", {
		method: "POST",
		headers: getHeader(),
		body: { ...param }
	});
};

export default {
	getOrgList,
	addOrg,
	modifyOrg,
	deleteOrg,
	getOrgPermission,
	saveOrgPermission
};
