import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";

const auth = async (params) => {
	return request("/user/getAuthCode", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const signIn = async (params) => {
	return request("/user/login", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const signOut = async (params) => {
	return request("/user/logout", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

/**
 * 用户获取自己的信息
 * @returns {Promise<Object>}
 */
const getUserInfo = async () => {
	return request("/userCenter/getUserInfo", {
		method: "GET",
		headers: getHeader()
	});
};

const checkToken = async (params) => {
	return request(getUrl("/user/checkToken", params), {
		method: "POST",
		headers: getHeader()
	});
};

const getUserList = async (params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/user/list", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getUserMenuTree = async (params) => {
	return request(getUrl("/user/menuTree", params), {
		method: "GET",
		headers: getHeader()
	});
};

const addUser = async (params) => {
	return request("/user", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const modifyUser = async (params) => {
	return request("/user", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

/**
 * 修改用户自己的信息
 * @param params
 * @returns {Promise<Object>}
 */
const modifyUserInfo = async (params) => {
	return request("/userCenter/updateUser", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const deleteUser = async (uuid) => {
	return request("/user?uuid=" + uuid, {
		method: "DELETE",
		headers: getHeader()
	});
};

const changeUserStatus = async (params) => {
	return request("/user/status", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const resetUserPassword = async (params) => {
	return request("/user/resetPwd", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

/**
 * 用户修改自己的密码
 * @param params
 * @returns {Promise<Object>}
 */
const changePwd = async (params) => {
	return request("/userCenter/changePwd", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const activeLicense = async (params) => {
	return request("/license/active", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const getLicenseInfo = async (params) => {
	return request(getUrl("/license", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getLoginHistory = async (params) => {
	return request(getUrl("/userCenter/userHistory", params), {
		method: "GET",
		headers: getHeader()
	});
};

const userLogin = async (params) => {
	return request(getUrl("/user/login"), {
		method: "POST",
		body: { ...params }
	});
};

// 获取有效用户信息
const geteEffectUserList = async (params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/user/effective/list", params), {
		method: "GET",
		headers: getHeader()
	});
};
const getTokenByClientId = async (params) => {
	return request(getUrl("/user/getToken"), {
		method: "POST",
		body: { ...params }
	});
};

const getAuthCode = async (params) => {
	return request(getUrl("/user/authCode", params), {
		method: "GET",
		headers: getHeader()
	});
};

export default {
	auth,
	signIn,
	signOut,
	getUserInfo,
	getUserList,
	addUser,
	modifyUser,
	changePwd,
	modifyUserInfo,
	deleteUser,
	changeUserStatus,
	getUserMenuTree,
	resetUserPassword,
	activeLicense,
	getLicenseInfo,
	checkToken,
	getLoginHistory,
	userLogin,
	geteEffectUserList,
	getTokenByClientId,
	getAuthCode
};
