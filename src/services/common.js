import queryString from "query-string";
import Cookies from "universal-cookie";
import md5 from "md5";

const getUrl = (url, query) => {
	return url + "?" + queryString.stringify(query);
};

const getCsrfInfo = () => {
	const cookies = new Cookies();
	let token = cookies.get("_td_token_");
	let randomArray = [];

	for (let i = 0; i < 4; i++) {
		let randomNum = Math.floor(Math.random() * 10);
		randomArray.push(randomNum);
	}
	let randomStr = "";
	if (token) {
		randomArray.forEach((number, index) => {
			randomStr = randomStr + token[number];
		});
	}
	let md5Str = md5(randomStr);
	let preText = "";
	let tailText = "";
	randomArray.forEach((number, index) => {
		if (index === 0 || index === 1) {
			preText = preText + number;
		}
		if (index === 2 || index === 3) {
			tailText = tailText + number;
		}
	});
	return preText + md5Str + tailText;
};

const getHeader = () => {
	let headers = {};
	headers["X-Cf-Random"] = sessionStorage.getItem("_csrf_");
	return headers;
};
const deleteEmptyObjItem = (obj) => {
	for (let i in obj) {
		let value = obj[i];
		if (value === null || value === undefined || value === "" || !value && value !== 0) {
			delete obj[i];
		}
	}
	return obj;
};
export {
	getUrl,
	getHeader,
	deleteEmptyObjItem
};
