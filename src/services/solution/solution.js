import { getHeader, getUrl, deleteEmptyObjItem } from "../common";
import request from "../../utils/request";

const getSolution = async(param) => {
	return request(getUrl("/solution", param), {
		method: "GET",
		headers: getHeader()
	});
};

const addSolutionGroup = async(params) => {
	return request("/solution/group", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};
const updateSolutionMenu = async(params) => {
	return request("/solution/menu", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const modifySolutionGroup = async(params) => {
	return request("/solution/group", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const deleteSolutionGroup = async(uuid) => {
	return request("/solution/group?uuid=" + uuid, {
		method: "DELETE",
		headers: getHeader()
	});
};

const modifySolution = async(params) => {
	return request("/solution", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

export default {
	getSolution,
	addSolutionGroup,
	updateSolutionMenu,
	modifySolutionGroup,
	deleteSolutionGroup,
	modifySolution
};
