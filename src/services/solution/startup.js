import { getHeader, getUrl, deleteEmptyObjItem } from "../common";
import request from "../../utils/request";

const getLicenseInfo = async() => {
	let params = {
		appCode: "all"
	};
	return request(getUrl("/license", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getSolution = async(param) => {
	return request(getUrl("/solution", param), {
		method: "GET",
		headers: getHeader()
	});
};

const activeLicense = async(params) => {
	return request("/license/active", {
		method: "POST",
		headers: getHeader(),
		body: params
	});
};

export default {
	getLicenseInfo,
	getSolution,
	activeLicense
};
