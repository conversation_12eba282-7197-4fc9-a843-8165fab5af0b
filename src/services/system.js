import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";

const getSystemRegister = async(param) => {
	return request(getUrl("/regedit/menu", param), {
		method: "GET",
		headers: getHeader()
	});
};

const addSystemRegisterMenu = async(params) => {
	return request("/regedit/menu", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};
const modifySystemRegisterMenu = async(params) => {
	return request("/regedit/menu", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};
const deleteSystemRegisterMenu = async(params) => {
	return request(getUrl("/regedit/menu", params), {
		method: "DELETE",
		headers: getHeader()
	});
};

const addSystemRegisterFunction = async(params) => {
	return request("/regedit/function", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};
const modifySystemRegisterFunction = async(params) => {
	return request("/regedit/function", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};
const deleteSystemRegisterFunction = async(params) => {
	return request(getUrl("/regedit/function", params), {
		method: "DELETE",
		headers: getHeader()
	});
};

const getOrgPermission = async(param) => {
	return request(getUrl("/system/register", param), {
		method: "GET",
		headers: getHeader()
	});
};

const getOrgList = async(param) => {
	return request(getUrl("/system/register", param), {
		method: "GET",
		headers: getHeader()
	});
};

export default {
	getSystemRegister,
	addSystemRegisterMenu,
	deleteSystemRegisterMenu,
	addSystemRegisterFunction,
	modifySystemRegisterFunction,
	deleteSystemRegisterFunction,
	modifySystemRegisterMenu,
	getOrgPermission,
	getOrgList
};
