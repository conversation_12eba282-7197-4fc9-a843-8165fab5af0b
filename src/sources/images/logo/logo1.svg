<?xml version="1.0" encoding="UTF-8"?>
<svg width="134px" height="134px" viewBox="0 0 134 134" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 49 (51002) - http://www.bohemiancoding.com/sketch -->
    <title>Group 2</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <polygon id="path-1" points="55.8015726 68.797588 -5.45696821e-12 103.382321 55.8015726 137.595176 111.603145 103.382321"></polygon>
        <filter x="-4.5%" y="-5.8%" width="109.0%" height="114.5%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.669403699   0 0 0 0 0.669403699   0 0 0 0 0.669403699  0 0 0 0.360252491 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <polygon id="path-3" points="55.8015726 34.398794 -5.45696821e-12 68.9835274 55.8015726 103.196382 111.603145 68.9835274"></polygon>
        <filter x="-4.5%" y="-5.8%" width="109.0%" height="114.5%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.669403699   0 0 0 0 0.669403699   0 0 0 0 0.669403699  0 0 0 0.360252491 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <polygon id="path-5" points="55.8015726 3.18323146e-12 -3.63797881e-12 34.5847334 55.8015726 68.797588 111.603145 34.5847334"></polygon>
        <filter x="-4.5%" y="-5.8%" width="109.0%" height="114.5%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.669403699   0 0 0 0 0.669403699   0 0 0 0 0.669403699  0 0 0 0.360252491 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="Page-2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Group-2" transform="translate(-22.000000, -23.000000)">
            <g id="Group" transform="translate(88.471947, 89.359691) rotate(-45.000000) translate(-88.471947, -89.359691) translate(32.471947, 20.359691)">
                <g id="Path-2" opacity="0.404185268">
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                </g>
                <g id="Path-2-Copy" opacity="0.603459821">
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-3"></use>
                </g>
                <g id="Path-2-Copy-2" opacity="0.799107143">
                    <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-5"></use>
                </g>
            </g>
        </g>
    </g>
</svg>