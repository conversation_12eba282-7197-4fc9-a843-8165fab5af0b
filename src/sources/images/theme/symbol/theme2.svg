<?xml version="1.0" encoding="UTF-8"?>
<svg width="68px" height="68px" viewBox="0 0 68 68" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 52.4 (67378) - http://www.bohemiancoding.com/sketch -->
    <title>左右</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="68" height="68"></rect>
        <linearGradient x1="12.9101562%" y1="0%" x2="84.0527344%" y2="100%" id="linearGradient-3">
            <stop stop-color="#8ABEFF" offset="0%"></stop>
            <stop stop-color="#62A7FF" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-4" points="4.4408921e-16 0 20 0 20 68 8.8817842e-16 68"></polygon>
    </defs>
    <g id="大盘" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="V2-Copy-2" transform="translate(-1239.000000, -412.000000)">
            <g id="左右" transform="translate(1239.000000, 412.000000)">
                <mask id="mask-2" fill="white">
                    <use xlink:href="#path-1"></use>
                </mask>
                <use id="Mask" fill="#E3EBFF" xlink:href="#path-1"></use>
                <g id="矩形" mask="url(#mask-2)">
                    <g id="大气科技镭射渐变背景-copy">
                        <mask id="mask-5" fill="white">
                            <use xlink:href="#path-4"></use>
                        </mask>
                        <use id="Mask" fill="url(#linearGradient-3)" opacity="0.819707961" xlink:href="#path-4"></use>
                    </g>
                </g>
                <rect id="矩形-copy-5" fill="#2E81F7" opacity="0.169926525" mask="url(#mask-2)" x="20" y="0" width="48" height="17"></rect>
            </g>
        </g>
    </g>
</svg>