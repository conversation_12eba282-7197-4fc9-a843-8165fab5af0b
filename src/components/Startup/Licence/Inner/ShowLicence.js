import { PureComponent } from "react";
import { connect } from "dva";
import { Row, Col, Form, Button, Input } from "antd";
import moment from "moment";

import startUpLang from "@/constants/lang/startup";
const TextArea = Input.TextArea;

class ShowLicence extends PureComponent {
	constructor(props) {
		super(props);
	}

	render() {
		let { startupStore, dispatch } = this.props;
		let { licensePage } = startupStore;
		let { licenseInfo } = licensePage;

		return (
			<Form>
				<Row gutter={10} className="auto-height">
					<Col span={6} className="basic-info-title">
						{startUpLang.common("nowLicence")}：{" "}
					</Col>
					<Col span={18}>
						<TextArea
							type="text"
							rows={4}
							disabled={true}
							value={licenseInfo.license}
						/>
					</Col>
				</Row>
				<Row gutter={10} className="auto-height">
					<Col span={6} className="basic-info-title">
						{startUpLang.common("restTime")}：{" "}
					</Col>
					<Col span={18}>
						<Input
							type="text"
							size="large"
							disabled={true}
							value={licenseInfo.leftDay}
						/>
					</Col>
				</Row>
				<Row gutter={10} className="auto-height">
					<Col span={6} className="basic-info-title">
						{startUpLang.common("overdueTime")}：{" "}
					</Col>
					<Col span={18}>
						<Input
							type="text"
							size="large"
							disabled={true}
							value={
								licenseInfo.expiration ? moment(licenseInfo.expiration).format("YYYY-MM-DD HH:mm:ss") : ""
							}
						/>
					</Col>
				</Row>
				<Row gutter={10} className="auto-height">
					<Col span={6} className="basic-info-title">
						{startUpLang.common("maxAppName")}：{" "}
					</Col>
					<Col span={18}>
						<Input
							type="text"
							size="large"
							disabled={true}
							value={licenseInfo.appNum}
						/>
					</Col>
				</Row>
				<Row gutter={10}>
					<Col span={6} className="basic-info-title" />
					<Col span={18}>
						<Button
							size="large"
							type="primary"
							onClick={() => {
								dispatch({
									type: "startup/setAttrValue",
									payload: {
										licensePage: {
											modifyLicence: true
										}
									}
								});
							}}>
							{startUpLang.common("modifyLicence")}
						</Button>
					</Col>
				</Row>
			</Form>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	startupStore: state.startup
}))(ShowLicence);
