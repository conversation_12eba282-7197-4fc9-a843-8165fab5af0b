import { PureComponent } from "react";
import { connect } from "dva";
import { Row, Col, Form, Button, Input, message, Alert } from "antd";
import { startupAPI } from "@/services";
import startUpLang from "@/constants/lang/startup";

const TextArea = Input.TextArea;

class EditLicence extends PureComponent {
    state = {
    	needLicence: false,
    	needPublicKey: false
    };

    constructor(props) {
    	super(props);
    	this.activeLicense = this.activeLicense.bind(this);
    	this.cancelEditLicence = this.cancelEditLicence.bind(this);
    	this.changeFieldValue = this.changeFieldValue.bind(this);
    }

    changeFieldValue(field, e) {
    	let { startupStore, dispatch } = this.props;
    	let { licensePage } = startupStore;
    	let { modifyLicenceData } = licensePage;

    	modifyLicenceData[field] = e.target.value;

    	// 清空alert信息
    	if (field === "license") {
    		this.setState({
    			needLicence: false
    		});
    	}
    	if (field === "publicKey") {
    		this.setState({
    			needPublicKey: false
    		});
    	}

    	dispatch({
    		type: "startup/setAttrValue",
    		payload: {
    			licensePage: {
    				modifyLicenceData: modifyLicenceData
    			}
    		}
    	});
    }

    activeLicense() {
    	let { startupStore, dispatch } = this.props;
    	let { licensePage } = startupStore;
    	let { modifyLicenceData } = licensePage;

    	if (!modifyLicenceData.license) {
    		this.setState({
    			needLicence: true
    		});
    		return;
    	}

    	if (!modifyLicenceData.publicKey) {
    		this.setState({
    			needPublicKey: true
    		});
    		return;
    	}

    	startupAPI
    		.activeLicense(modifyLicenceData)
    		.then(res => {
    			if (res.success) {
    				message.success(startUpLang.common("success"));
    				// 获取licence
    				dispatch({
    					type: "startup/getLicense"
    				});

    				// 清空model数据
    				dispatch({
    					type: "startup/setAttrValue",
    					payload: {
    						licensePage: {
    							modifyLicenceData: {
    								license: null,
    								publicKey: null
    							}
    						}
    					}
    				});

    				// 返回
    				dispatch({
    					type: "startup/setAttrValue",
    					payload: {
    						licensePage: {
    							modifyLicence: false
    						}
    					}
    				});
    			} else {
    				message.error(res.message);
    			}
    		})
    		.catch(err => {
    			console.error(err);
    		});
    }

    cancelEditLicence() {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "startup/setAttrValue",
    		payload: {
    			licensePage: {
    				modifyLicence: false,
    				modifyLicenceData: {
    					license: null,
    					publicKey: null
    				}
    			}
    		}
    	});
    }

    render() {
    	let { needLicence, needPublicKey } = this.state;
    	let { startupStore } = this.props;
    	let { licensePage } = startupStore;
    	let { hasLicense, licenseReady, modifyLicenceData } = licensePage;

    	return (
    		<Form>
    			<Row gutter={10} style={{height: "auto"}}>
    				<Col span={6} className="basic-info-title">
                        License：
    				</Col>
    				<Col span={18}>
    					{
    						needLicence &&
                            <Alert
                            	message={startUpLang.common("enterLicence")}
                            	type="warning"
                            	showIcon
                            	style={{ marginBottom: "10px" }}
                            />
    					}
    					<TextArea
    						type="text"
    						rows={4}
    						placeholder={startUpLang.common("enterLicense")}
    						value={modifyLicenceData.license || undefined}
    						onChange={this.changeFieldValue.bind(this, "license")}
    					/>
    				</Col>
    			</Row>
    			<Row gutter={10} style={{height: "auto"}}>
    				<Col span={6} className="basic-info-title">
                        PublicKey：
    				</Col>
    				<Col span={18}>
    					{needPublicKey && (
    						<Alert
    							message={startUpLang.common("enterPublicKey")}
    							type="warning"
    							showIcon
    							style={{ marginBottom: "10px" }}
    						/>
    					)}
    					<TextArea
    						type="text"
    						rows={4}
    						placeholder={startUpLang.common("enterPublicKey")}
    						value={modifyLicenceData.publicKey || undefined}
    						onChange={this.changeFieldValue.bind(this, "publicKey")}
    					/>
    				</Col>
    			</Row>
    			<Row gutter={10}>
    				<Col span={6} className="basic-info-title" />
    				<Col span={18}>
    					{
    						licenseReady &&
                            hasLicense &&
                            <Button
                            	size="large"
                            	className="mr10"
                            	onClick={this.cancelEditLicence.bind(this)}>
                            	{startUpLang.common("cancel")}
                            </Button>
    					}
    					<Button
    						size="large"
    						type="primary"
    						onClick={this.activeLicense.bind(this)}>
    						{licenseReady && hasLicense
    							? startUpLang.common("modifyLicence")
    							: startUpLang.common("ok")}
    					</Button>
    				</Col>
    			</Row>
    		</Form>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	startupStore: state.startup
}))(EditLicence);
