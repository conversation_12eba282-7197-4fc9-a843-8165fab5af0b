import { PureComponent } from "react";
import { connect } from "dva";
import ShowLicence from "./Inner/ShowLicence";
import EditLicence from "./Inner/EditLicence";

class Licence extends PureComponent {
	constructor(props) {
		super(props);
	}

	componentWillMount() {
		let { dispatch } = this.props;

		dispatch({
			type: "startup/setAttrValue",
			payload: {
				licensePage: {
					modifyLicence: false
				}
			}
		});
	}

	render() {
		let { startupStore } = this.props;
		let { licensePage } = startupStore;
		let { hasLicense, licenseReady, modifyLicence } = licensePage;

		return (
			<div className="startup-form-wrap">
				<div className="startup-form">
					<div className="basic-form">
						{
							licenseReady &&
                            hasLicense &&
                            !modifyLicence &&
                            <ShowLicence />
						}
						{
							// 修改license
							licenseReady &&
                            !hasLicense &&
                            <EditLicence />
						}
						{
							// 修改license
							licenseReady &&
                            hasLicense &&
                            modifyLicence &&
                            <EditLicence />
						}
					</div>
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	startupStore: state.startup
}))(Licence);
