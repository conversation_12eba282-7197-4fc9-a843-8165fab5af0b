import { PureComponent } from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { Row, Col, Icon } from "antd";
import "./Startup.less";
import Licence from "./Licence";
import Solution from "./Solution";
import { searchToObject } from "@/utils/utils";
import startUpLang from "@/constants/lang/startup";

class Startup extends PureComponent {
    state = {};

    constructor(props) {
        super(props);
        this.switchTab = this.switchTab.bind(this);
    }

    componentWillMount() {
        let { dispatch } = this.props;

        // 获取licence
        dispatch({
            type: "startup/getLicense"
        });

        // 获取解决方案
        dispatch({
            type: "startup/getSolution"
        });
    }

    switchTab(item, index) {
        let { dispatch, location } = this.props;
        let { pathname } = location;
        let search = "?currentTab=" + item.code;
        dispatch(routerRedux.push(pathname + search));
    }

    render() {
        let {
            globalStore: {
                personalMode: { lang }
            },
            startupStore,
            location,
            history,
            dispatch,
            isInit
        } = this.props;
        let { search } = location;
        let searchObj =
            search && searchToObject(search) ? searchToObject(search) : null;
        let currentTab =
            searchObj && searchObj.currentTab
                ? searchObj.currentTab
                : "license";

        let { solutionPage } = startupStore;
        let { solutionReady, hasSolution } = solutionPage;

        const menuMap = [
            {
                code: "license",
                title: "License管理",
                enTitle: "License Admin"
            },
            {
                code: "solution",
                title: "解决方案管理",
                enTitle: "Solution Manage"
            }
        ];

        return (
            <div className={isInit ? "startup-layout" : ""}>
                <div
                    className={
                        isInit ? "startup-wrap" : "startup-wrap not-center"
                    }>
                    {isInit && (
                        <div className="startup-header">
                            <div className="go-back">
                                <a
                                    onClick={() => {
                                        dispatch(
                                            routerRedux.push("/user/login")
                                        );
                                    }}>
                                    <Icon type="left" />
                                    <span>{startUpLang.common("return")}</span>
                                </a>
                            </div>
                            <div className="title">
                                <h2>{startUpLang.common("systermConfig")}</h2>
                                <p>{startUpLang.common("systermConfigDes")}</p>
                            </div>
                        </div>
                    )}
                    <div className="startup-body">
                        <Row gutter={40}>
                            <Col span={6}>
                                <div className="startup-menu">
                                    {menuMap.map((item, index) => {
                                        return (
                                            <div
                                                className={
                                                    currentTab === item.code
                                                        ? "startup-menu-item active"
                                                        : "startup-menu-item"
                                                }
                                                onClick={this.switchTab.bind(
                                                    this,
                                                    item,
                                                    index
                                                )}
                                                key={index}>
                                                <a>
                                                    {lang === "cn"
                                                        ? item.title
                                                        : item.enTitle}
                                                </a>
                                            </div>
                                        );
                                    })}
                                </div>
                            </Col>
                            <Col span={18}>
                                {currentTab === "license" && <Licence />}
                                {currentTab === "solution" && <Solution />}
                            </Col>
                        </Row>
                    </div>
                </div>
            </div>
        );
    }
}

export default connect(state => ({
    globalStore: state.global,
    startupStore: state.startup
}))(Startup);
