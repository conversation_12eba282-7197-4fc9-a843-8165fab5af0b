import { PureComponent } from "react";
import { connect } from "dva";
import {
	Row,
	Col,
	Form,
	Input,
	Button,
	message,
	Alert
} from "antd";
import ViewSolutionModal from "./Modal/ViewSolutionModal";
import ImportSolutionModal from "./Modal/ImportSolutionModal";
import startUpLang from "@/constants/lang/startup";

class Solution extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.viewSolutionDetail = this.viewSolutionDetail.bind(this);
    	this.importSolutionHandle = this.importSolutionHandle.bind(this);
    }

    viewSolutionDetail() {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "startup/setAttrValue",
    		payload: {
    			dialogShow: {
    				viewSolution: true
    			}
    		}
    	});
    }

    importSolutionHandle() {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "startup/setAttrValue",
    		payload: {
    			dialogShow: {
    				importSolution: true
    			}
    		}
    	});
    }

    render() {
    	let { startupStore } = this.props;
    	let { solutionPage, licensePage } = startupStore;
    	let { hasLicense, licenseReady } = licensePage;
    	let { hasSolution, solutionReady, menuTreeInfo } = solutionPage;

    	return (
    		<div className="startup-form-wrap">
    			<div className="startup-form">
    				<div className="basic-form">
    					<Form>
    						{solutionReady && !hasSolution && (
    							<div>
    								<Row gutter={10} className="auto-height">
    									<Col
    										span={6}
    										className="basic-info-title"
    									/>
    									<Col span={18}>
    										<Alert
    											message={startUpLang.common("tip")}
    											description={startUpLang.common("noSolution")}
    											type="warning"
    											showIcon
    										/>
    									</Col>
    								</Row>
    							</div>
    						)}
    						{solutionReady && hasSolution && (
    							<div>
    								<Row gutter={10} className="auto-height">
    									<Col
    										span={6}
    										className="basic-info-title"
    									>
    										{startUpLang.common("chineseName")}：
    									</Col>
    									<Col span={18}>
    										<Input
    											type="text"
    											size="large"
    											placeholder={startUpLang.common("chineseNamePlaceholder")}
    											value={menuTreeInfo.name || undefined}
    											disabled={true}
    										/>
    									</Col>
    								</Row>
    								<Row gutter={10} className="auto-height">
    									<Col
    										span={6}
    										className="basic-info-title"
    									>
    										{startUpLang.common("englishName")}{" "}：
    									</Col>
    									<Col span={18}>
    										<Input
    											type="text"
    											size="large"
    											placeholder={startUpLang.common(
    												"englishNamePlaceholder"
    											)}
    											value={
    												menuTreeInfo.enName ||
                                                    undefined
    											}
    											disabled={true}
    										/>
    									</Col>
    								</Row>
    								<Row gutter={10} className="auto-height">
    									<Col
    										span={6}
    										className="basic-info-title"
    									>
                                            Logo：
    									</Col>
    									<Col span={18}>
    										<Input
    											type="text"
    											size="large"
    											value={menuTreeInfo.logo || undefined}
    											disabled={true}
    										/>
    									</Col>
    								</Row>
    								<Row gutter={10} className="auto-height">
    									<Col
    										span={6}
    										className="basic-info-title">
    										{startUpLang.common("solutionDetail")}：
    									</Col>
    									<Col span={18}>
    										<a
    											onClick={this.viewSolutionDetail.bind(this)}
    										>
    											{startUpLang.common("clickDetail")}
    										</a>
    									</Col>
    								</Row>
    							</div>
    						)}
    						<Row gutter={10}>
    							<Col span={6} className="basic-info-title" />
    							<Col span={18}>
    								{
    									licenseReady &&
                                        !hasLicense &&
                                        <Button
                                        	size="large"
                                        	type="primary"
                                        	onClick={() => {
                                        		message.warning(startUpLang.common("licenceWarning"));
                                        	}}
                                        >
                                        	{startUpLang.common("importSolution")}
                                        </Button>
    								}
    								{
    									licenseReady &&
                                        hasLicense &&
                                        <Button
                                        	size="large"
                                        	type="primary"
                                        	onClick={this.importSolutionHandle.bind(
                                        		this
                                        	)}
                                        >
                                        	{startUpLang.common("importSolution")}
                                        </Button>
    								}
    							</Col>
    						</Row>
    					</Form>
    				</div>
    			</div>
    			<ViewSolutionModal />
    			<ImportSolutionModal />
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	startupStore: state.startup
}))(Solution);
