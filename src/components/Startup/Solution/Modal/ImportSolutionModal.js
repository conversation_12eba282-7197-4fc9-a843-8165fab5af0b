import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, message, Upload, Button, Icon } from "antd";
import startUpLang from "@/constants/lang/startup";
import { getHeader } from "@/services/common";
import { uploadIntercept } from "@/utils/request";

class ImportSolutionModal extends PureComponent {
    state = {
    	fileList: []
    };

    constructor(props) {
    	super(props);
    	this.closeModal = this.closeModal.bind(this);
    	this.submitModal = this.submitModal.bind(this);
    }

    closeModal() {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "startup/setAttrValue",
    		payload: {
    			dialogShow: {
    				importSolution: false
    			}
    		}
    	});
    }

    submitModal() {
    	let { dispatch } = this.props;
    }

    render() {
    	let { fileList } = this.state;
    	let { startupStore, dispatch } = this.props;
    	let { dialogShow } = startupStore;

    	return (
    		<Modal
    			title={startUpLang.common("importSolution")}
    			visible={dialogShow.importSolution}
    			maskClosable={false}
    			onCancel={this.closeModal.bind(this)}
    			onOk={this.submitModal.bind(this)}
    			footer={
    				<Button
    					onClick={this.closeModal.bind(this)}
    				>
                        取消
    				</Button>
    			}
    		>
    			<Upload
    				name="solution"
    				action="/bridgeApi/solution/import"
    				fileList={this.state.fileList}
    				headers={getHeader()}
    				onChange={info => {
    					console.log(info);
    					let fileInfo = info.file;
    					if (fileInfo.status !== "uploading") {
    						console.log(fileInfo, info.fileList);
    					}
    					if (fileInfo.status === "done") {
    						if (fileInfo.response.success) {
    							message.success(startUpLang.common("success"));

    							// 获取解决方案
    							dispatch({
    								type: "startup/getSolution"
    							});

    							// 关闭modal
    							setTimeout(() => {
    								this.closeModal();
    							}, 500);
    						} else {
    							message.warning(fileInfo.response.message);
    						}
    					} else if (fileInfo.status === "error") {
    						console.log(fileInfo);
    						const { error = {}, response = {} } = fileInfo || {};
    						uploadIntercept(error.status, response);
    						message.error(startUpLang.common("uploadFailed"));
    					}
    					// 重新设置state
    					if (info.fileList && info.fileList.length > 0) {
    						fileList = [fileInfo];
    					} else {
    						fileList = [];
    					}
    					this.setState({
    						fileList: fileList
    					});
    				}}>
    				<Button>
    					<Icon type="upload" />
    					{startUpLang.common("uploadButtonText")}
    				</Button>
    			</Upload>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	startupStore: state.startup
}))(ImportSolutionModal);
