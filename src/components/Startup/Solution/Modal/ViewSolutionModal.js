import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Tree } from "antd";
import startUpLang from "@/constants/lang/startup";

const DirectoryTree = Tree.DirectoryTree;
const { TreeNode } = Tree;

class ViewSolutionModal extends PureComponent {
	constructor(props) {
		super(props);
	}

    onSelect = () => {
    	// console.log("Trigger Select");
    };

    onExpand = () => {
    	// console.log("Trigger Expand");
    };

    render() {
    	let { startupStore, dispatch } = this.props;
    	let { solutionPage, dialogShow } = startupStore;
    	let { menuTreeInfo } = solutionPage;
    	let { menuTree } = menuTreeInfo;
    	const { globalStore: { personalMode: { lang } } } = this.props;

    	return (
    		<Modal
    			title={startUpLang.common("solutionDetail")}
    			visible={dialogShow.viewSolution}
    			width={750}
    			onCancel={() => {
    				dispatch({
    					type: "startup/setAttrValue",
    					payload: {
    						dialogShow: {
    							viewSolution: false
    						}
    					}
    				});
    			}}
    			footer={null}
    			maskClosable={true}>
    			<DirectoryTree
    				// multiple
    				defaultExpandAll
    				onSelect={this.onSelect}
    				onExpand={this.onExpand}>
    				{menuTree &&
                        menuTree.map((item, index) => {
                        	return (
                        		<TreeNode
                        			title={
                        				lang === "cn" ? item.name : item.enName
                        			}
                        			key={index}>
                        			{item.children &&
                                        item.children.map(
                                        	(subItem, subIndex) => {
                                        		return (
                                        			<TreeNode
                                        				title={
                                        					lang === "cn"
                                        						? subItem.name
                                        						: subItem.enName
                                        				}
                                        				key={
                                        					index +
                                                            "-" +
                                                            subIndex
                                        				}
                                        				isLeaf
                                        			/>
                                        		);
                                        	}
                                        )}
                        		</TreeNode>
                        	);
                        })}
    			</DirectoryTree>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	startupStore: state.startup
}))(ViewSolutionModal);
