:global {
    .startup-layout {
        width: 100%;
        height: 100vh !important;
        background: -webkit-gradient(linear, left top, left bottom, from(#fcfeff), to(#e5f7ff));
        background: linear-gradient(#fcfeff, #e5f7ff);
        position: fixed;
        top: 0;
        left: 0;
        overflow: auto;
    }

    .startup-wrap {
        & {
            position: relative;
            top: 40px;
            width: 750px;
            margin-left: auto;
            margin-right: auto;
        }

        &.not-center {
            margin-left: 80px;
            margin-right: auto;
        }

        .startup-header {
            & {
                margin-bottom: 30px;
                border-bottom: 1px solid #e6e6e6;
            }

            .go-back {
                & {
                    font-size: 18px;
                    color: #000c17;
                    margin-bottom: 20px;
                }

                i {}

                span {}
            }

            .title {
                h2 {
                    color: #333;
                    font-weight: 500;
                    font-size: 40px;
                    margin-bottom: 10px;
                }

                p {
                    color: #8f939b;
                    font-size: 16px;
                }
            }
        }

        .startup-body {
            & {
                margin-bottom: 30px;
            }
        }
    }

    // 菜单
    .startup-menu {
        & {}

        .startup-menu-item {
            a {
                height: 45px;
                line-height: 45px;
                width: 100%;
                border: 1px solid #6ec1ff;
                border-radius: 50px;
                text-align: center;
                -webkit-transition: all .2s;
                transition: all .2s;
                margin-bottom: 10px;
                cursor: pointer;
                display: block;
                text-decoration: none;
            }

            a:hover,
            &.active a {
                border-color: #428ff0;
                background: -webkit-gradient(linear, left top, right top, from(#0050bf), to(#338cff));
                background: linear-gradient(90deg, #0050bf, #338cff);
                color: #fff;
            }
        }
    }

    .startup-form-wrap {
        & {}

        .startup-form {
            .basic-info-title {
                line-height: 40px;
            }

            a {
                line-height: 40px;
            }
        }
    }
}
