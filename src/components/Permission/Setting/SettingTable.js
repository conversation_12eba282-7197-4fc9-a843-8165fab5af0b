import { PureComponent } from "react";
import { connect } from "dva";
import { Table, Checkbox, Popover, Switch } from "antd";
import { permissionLang } from "@/constants/lang";

class SettingTable extends PureComponent {

	constructor(props) {
		super(props);
		this.changeGroupStatus = this.changeGroupStatus.bind(this);
		this.changeFunctionStatus = this.changeFunctionStatus.bind(this);
		this.changeCheckAll = this.changeCheckAll.bind(this);
		this.changeTableCheckAll = this.changeTableCheckAll.bind(this);
	}

	changeGroupStatus(groupIndex, menuIndex, funcIndex, status) {
		let { permissionStore, dispatch } = this.props;
		let { permissionConfig } = permissionStore;
		permissionConfig[groupIndex]["children"][menuIndex]["hasPermission"] = status;
		let functionList = permissionConfig[groupIndex]["children"][menuIndex]["functionList"] ? permissionConfig[groupIndex]["children"][menuIndex]["functionList"] : [];
		if (!status) {
			functionList && functionList.map((item, index) => {
				item["hasPermission"] = false;
			});
		}

		dispatch({
			type: "permission/setAttrValue",
			payload: {
				permissionConfig: permissionConfig
			}
		});
	}

	changeFunctionStatus(groupIndex, menuIndex, funcIndex, e) {
		let status = e.target.checked;
		console.log(status);
		let { permissionStore, groupConfigJson, dispatch } = this.props;
		let { permissionConfig } = permissionStore;

		permissionConfig[groupIndex]["children"][menuIndex]["functionList"][funcIndex]["hasPermission"] = status;

		dispatch({
			type: "permission/setAttrValue",
			payload: {
				permissionConfig: permissionConfig
			}
		});
	}

	changeCheckAll(groupIndex, menuIndex, selfStatus, e) {
		let status = e.target.checked;
		let { permissionStore, groupConfigJson, dispatch } = this.props;
		let { permissionConfig } = permissionStore;
		let functionList = permissionConfig[groupIndex]["children"][menuIndex]["functionList"] ? permissionConfig[groupIndex]["children"][menuIndex]["functionList"] : [];
		if (selfStatus) {
			permissionConfig[groupIndex]["children"][menuIndex]["hasPermission"] = false;
			functionList && functionList.map((item, index) => {
				item["hasPermission"] = false;
			});
		} else {
			permissionConfig[groupIndex]["children"][menuIndex]["hasPermission"] = true;
			functionList && functionList.map((item, index) => {
				item["hasPermission"] = true;
			});
		}
		dispatch({
			type: "permission/setAttrValue",
			payload: {
				permissionConfig: permissionConfig
			}
		});
	}

	changeTableCheckAll(checkAll) {
		let { permissionStore, groupConfigJson, groupIndex, dispatch } = this.props;
		let { permissionConfig } = permissionStore;
		let length = permissionConfig[groupIndex]["children"].length;
		if (!checkAll) {
			for (let i = 0; i < length; i++) {
				let functionList = permissionConfig[groupIndex]["children"][i]["functionList"] ? permissionConfig[groupIndex]["children"][i]["functionList"] : [];
				permissionConfig[groupIndex]["children"][i]["hasPermission"] = true;
				functionList && functionList.map((item, index) => {
					item["hasPermission"] = true;
				});
			}
		} else {
			for (let i = 0; i < length; i++) {
				let functionList = permissionConfig[groupIndex]["children"][i]["functionList"] ? permissionConfig[groupIndex]["children"][i]["functionList"] : [];
				permissionConfig[groupIndex]["children"][i]["hasPermission"] = false;
				functionList && functionList.map((item, index) => {
					item["hasPermission"] = false;
				});
			}
		}

		dispatch({
			type: "permission/setAttrValue",
			payload: {
				permissionConfig: permissionConfig
			}
		});
	}

	render() {
		let { permissionStore, groupConfigJson, groupIndex, dispatch, globalStore } = this.props;
		const { personalMode } = globalStore;
		const { lang } = personalMode;
		let checkAll = true;
		let checkPart = false;
		groupConfigJson.map((item, index) => {
			if (item.hasPermission) {
				checkPart = true;
				item.functionList && item.functionList.map((fun, index) => {
					if (!fun.hasPermission) {
						checkAll = false;
					}
				});
			} else {
				checkAll = false;
			}
		});
		if (checkAll) {
			checkPart = false;
		}
		const columns = [
			{
				title: (
					<div>
						<Checkbox
							checked={checkAll}
							indeterminate={checkPart}
							onClick={this.changeTableCheckAll.bind(this, checkAll)}
						/>
					</div>
				),
				width: 64,
				render: (text, record, index) => {
					// console.log(record);
					let checked = true;
					let indeterminate = false;
					if (record.hasPermission) {
						record.functionList && record.functionList.map((item, index) => {
							if (!item.hasPermission) {
								checked = false;
								indeterminate = true;
							}
						});
					} else {
						checked = false;
						indeterminate = false;
					}
					return (
						<div>
							<Checkbox
								indeterminate={indeterminate}
								onChange={this.changeCheckAll.bind(this, groupIndex, index, checked)}
								checked={checked}
							>
							</Checkbox>
						</div>
					);
				}
			},
			{
				title: permissionLang.tableSet("menu"), // 菜单
				width: 200,
				dataIndex: "menuName",
				render: (text, record, index) => {
					let content = (
						<span>{record.code}</span>
					);
					return (
						<div>
							<Switch
								// className="ant-switch-s1"
								onChange={this.changeGroupStatus.bind(this, groupIndex, index, "")}
								checked={record.hasPermission}
							/>

							{
								process.env.SYS_ENV === "development" &&
                                <Popover title={permissionLang.tableSet("menuCode")} content={content}>
                                	<span className="ml10">{lang === "cn" ? record.menuName : record.enName}</span>
                                </Popover>
							}
							{
								process.env.SYS_ENV !== "development" &&
                                <span className="ml10">{lang === "cn" ? record.menuName : record.enName}</span>
							}
						</div>
					);
				}
			},
			{
				title: permissionLang.tableSet("menuFeatures"), // 菜单功能
				dataIndex: "functionList",
				render: (text, record, index) => {
					return (
						<div className="show-content">
							{
								text.map((item, funcIndex) => {
									let content = (
										<span>{item.code}</span>
									);
									return (
										<div key={funcIndex}>
											{
												<Checkbox
													onChange={this.changeFunctionStatus.bind(this, groupIndex, index, funcIndex)}
													disabled={!record.hasPermission}
													checked={item.hasPermission}
												>
													{
														process.env.SYS_ENV === "development" &&
                                                        <Popover
                                                        	title={permissionLang.tableSet("menuCode")}
                                                        	content={content}
                                                        >
                                                        	{lang === "cn" ? item.name : item.enName}
                                                        </Popover>
													}
													{
														process.env.SYS_ENV !== "development" &&
														// <div>
                                                        	// {
                                                                lang === "cn" ? item.name : item.enName
														// }
														// </div>
													}
												</Checkbox>
											}
										</div>
									);
								})
							}
						</div>
					);

				}
			}
		];

		return (
			<Table
				columns={columns}
				dataSource={groupConfigJson}
				pagination={false}
				rowKey="menuUuid"
				size="middle"
			/>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	permissionStore: state.permission
}))(SettingTable);
