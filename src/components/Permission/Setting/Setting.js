import { PureComponent } from "react";
import { connect } from "dva";
import { Button, message, Icon } from "antd";
import { orgAPI, roleAPI } from "@/services";
import SettingTable from "./SettingTable";
import { checkFunctionHasPermission } from "@/utils/permission";
import { permissionLang } from "@/constants/lang";
import EmptyPage from "@/components/EmptyPage";

const ButtonGroup = Button.Group;

class Setting extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.editExpandedRowKeys = this.editExpandedRowKeys.bind(this);
    	this.savePermissionModify = this.savePermissionModify.bind(this);
    }

    editExpandedRowKeys(item) {
    	let { permissionStore, dispatch } = this.props;
    	let { expandedRowKeys } = permissionStore;
    	if (expandedRowKeys.indexOf(item.groupUuid) > -1) {
    		let index = expandedRowKeys.indexOf(item.groupUuid);
    		expandedRowKeys.splice(index, 1);
    	} else {
    		expandedRowKeys.push(item.groupUuid);
    	}

    	dispatch({
    		type: "permission/setAttrValue",
    		payload: {
    			expandedRowKeys: expandedRowKeys
    		}
    	});
    }

    functionJ = (funcItem) => {
    	console.log(funcItem);
    };

    savePermissionModify() {
    	let { permissionStore, dispatch, pageName } = this.props;
    	let { permissionConfig, permissionConfigTemp, selectOrg, selectRole } = permissionStore;
    	let list = {
    		add: [],
    		delete: []
    	};
    	permissionConfig.map((groupItem, groupIndex) => {
    		groupItem.children && groupItem.children.map((menuItem, menuIndex) => {
    			let tempMenuItem = permissionConfigTemp[groupIndex]["children"][menuIndex];
    			if (tempMenuItem["hasPermission"] !== menuItem["hasPermission"]) {
    				if (tempMenuItem["hasPermission"]) {
    					console.log(menuItem);
    					list.delete.push({
    						"menuUuid": menuItem.menuUuid,
    						"functionUuid": null,
    						"optType": "menu",
    						"customTreeUuid": groupItem.groupUuid
    					});
    				} else {
    					list.add.push({
    						"menuUuid": menuItem.menuUuid,
    						"functionUuid": null,
    						"optType": "menu",
    						"customTreeUuid": groupItem.groupUuid
    					});
    				}
    			}
    			menuItem.functionList &&
                    menuItem.functionList.map((funcItem, funcIndex) => {
                    	let tempFunctionItem = permissionConfigTemp[groupIndex]["children"][menuIndex]["functionList"][funcIndex];
                    	if (tempFunctionItem["hasPermission"] !== funcItem["hasPermission"]) {
                    		if (tempFunctionItem["hasPermission"]) {
                    			console.log(funcItem);
                    			list.delete.push({
                    				"menuUuid": menuItem.menuUuid,
                    				"functionUuid": funcItem.funcUuid,
                    				"optType": "function",
                    				"customTreeUuid": groupItem.groupUuid
                    			});
                    		} else {
                    			list.add.push({
                    				"menuUuid": menuItem.menuUuid,
                    				"functionUuid": funcItem.funcUuid,
                    				"optType": "function",
                    				"customTreeUuid": groupItem.groupUuid
                    			});
                    		}
                    	}
                    });
    		});
    	});
    	let params = {
    		orgUuid: selectOrg.uuid,
    		roleUuid: selectRole.uuid,
    		params: JSON.stringify(list)
    	};
    	if (pageName === "org") {
    		orgAPI.saveOrgPermission(params).then((res) => {
    			if (res.success) {
    				dispatch({
    					type: "permission/getOrgPermission",
    					payload: {
    						orgUuid: selectOrg.uuid
    					}
    				});
    				message.success(permissionLang.tipInfo("tip1")); // 更新机构菜单成功
    			} else {
    				message.error(res.message);
    			}
    		}).catch(err => {
    			console.log(err);
    		});
    	} else if (pageName === "role") {
    		roleAPI.saveRolePermission(params).then((res) => {
    			if (res.success) {
    				dispatch({
    					type: "permission/getRolePermission",
    					payload: {
    						roleUuid: selectRole.uuid
    					}
    				});
    				message.success(permissionLang.tipInfo("tip2")); // 更新角色菜单成功
    			} else {
    				message.error(res.message);
    			}
    		}).catch(err => {
    			console.log(err);
    		});
    	}
    }

    render() {
    	const { permissionStore, pageName, globalStore } = this.props;
    	const { personalMode } = globalStore;
    	const { lang } = personalMode;
    	const { permissionConfig, selectOrg, selectRole, expandedRowKeys } = permissionStore;

    	let hasSelectLeftNav = false;
    	if (pageName === "org" && selectOrg.name) {
    		hasSelectLeftNav = true;
    	}
    	if (pageName === "role" && selectRole.name) {
    		hasSelectLeftNav = true;
    	}
    	let saveDisabled = false;
    	if (pageName === "org") {
    		saveDisabled = hasSelectLeftNav ? !checkFunctionHasPermission("QX0101", "modifyPermission") : true;
    	} else if (pageName === "role") {
    		saveDisabled = hasSelectLeftNav ? !checkFunctionHasPermission("QX0102", "modifyPermission") : true;
    	}

    	return (
    		<div className="fix-height">
    			<div className="register-box-header">
    				<h3>
    					{selectOrg.name && <span>{selectOrg.name}___</span>}
    					{/* 权限设置 */}
    					{permissionLang.common("permissionSet")}
    				</h3>
    				{
    					!saveDisabled &&
                        <ButtonGroup className="btns">
                        	<Button size="small"
                        		disabled={saveDisabled}
                        		type="primary"
                        		onClick={this.savePermissionModify.bind(this)}
                        	>
                        		{/* 保存修改 */}
                        		{permissionLang.common("saveChanges")}
                        	</Button>
                        </ButtonGroup>
    				}
    			</div>
    			<div className="register-box-body white-bg">
    				{
    					hasSelectLeftNav &&
                        permissionConfig &&
                        !!permissionConfig.length &&
                        <ul className="permission-config-list">
                        	{
                        		permissionConfig.map((item, index) => {
                        			let itemObj = (
                        				<li
                        					className={expandedRowKeys.indexOf(item.groupUuid) > -1 ? "permission-config-item active" : "permission-config-item"}
                        					key={index}
                        				>
                        					<div className="config-item-header"
                        						onClick={this.editExpandedRowKeys.bind(this, item)}>
                        						<div className="info">
                        							<h3>
                        								{lang === "cn" ? item.groupName : item.enName}
                        							</h3>
                        						</div>
                        						<div className="right">
                        							<Icon
                        								type={expandedRowKeys.indexOf(item.groupUuid) > -1 ? "down" : "right"}
                        							/>
                        						</div>
                        					</div>
                        					<div className="config-item-body">
                        						{
                        							item.children &&
                                                    <SettingTable
                                                    	index={item.groupUuid}
                                                    	groupIndex={index}
                                                    	groupConfigJson={item.children}
                                                    />
                        						}
                        						{
                        							!item.children &&
                                                    <p>
                                                    	{/* 当前组内暂无菜单列表 */}
                                                    	{permissionLang.common("noMenuList")}
                                                    </p>
                        						}
                        					</div>
                        				</li>
                        			);
                        			return itemObj;
                        		})
                        	}
                        </ul>
    				}
    				{/* 暂无菜单和功能权限 */}
    				{
    					permissionConfig.length === 0 &&
                        hasSelectLeftNav &&
                        <EmptyPage
                        	text={permissionLang.common("noMenuFun")}
                        />
    				}
    				{/* 请选择机构 */}
    				{
    					!hasSelectLeftNav &&
                        <EmptyPage
                        	text={permissionLang.common("selectIn")}
                        />
    				}
    			</div>
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	permissionStore: state.permission
}))(Setting);
