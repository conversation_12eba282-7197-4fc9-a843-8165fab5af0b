import { PureComponent, Fragment } from "react";
import { Dropdown, Menu, Icon } from "antd";
import "./index.less";

class RightOperaWrap extends PureComponent {
	constructor(props) {
		super(props);
	}
	render() {
		const { children = [] } = this.props;
		const newChildren = [...children].filter((child) => { return child; });
		let [firstChildPart, lastChildPart] = [null, null];
		if (newChildren && newChildren.length > 0) {
			firstChildPart = newChildren.slice(0, 1);
			lastChildPart = newChildren.slice(1);
		}
		const menu = (
			<Menu className="org-pop-opera-btn">
				{
					lastChildPart.map(child=>{
						return (
							<Menu.Item>
								{child}
							</Menu.Item>
						);
					})
				}

			</Menu>
		);
		return (
			<div className="oper-list right-opera-wrap" onClick={(e)=>{e.stopPropagation();}}>
				{firstChildPart}
				{
					lastChildPart &&
                    lastChildPart.length > 0 &&
                    <Dropdown
                    	overlay={menu}
                    	arrow
                    >
                    	<Icon type="more" />
                    </Dropdown>
				}
			</div>
		);
	}
}

export default RightOperaWrap;
