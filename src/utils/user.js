import { message } from "antd";
import JSEncrypt from "jsencrypt";
const pubKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDNpMKIVmt0u5lx62tRD1O/15EyNLN0lNi3++ytnvLalkQNSrrqU2w3uD5NwdVE/v4OrDznTpBdTl6N1ryXAILU5GDu0bLATC46RKxDlH52LIvaRBU7BZkEGqllEqRJFmwtvtNCVeZD6ekJWc67MLUh4LNa1yMQ9V6Zsf64uY2lgwIDAQAB";

// 弹窗未完全关闭禁止再次提交
export function messageError(payload) {
	return new Promise((resolve) => {
		message.error(payload, () => {
			resolve(false);
		});
	});
}

export const rsaPwd = (pwd) => {
	const encrypt = new JSEncrypt();
	encrypt.setPublicKey(pubKey);
	return encrypt.encrypt(pwd); // 加密
};

export const checkRegPwd = (pwd) => {
	return (/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d+)(?=.*[~!#@$%^*&?])[A-Za-z\d~!#@$%^*&?]{8,18}$/.test(pwd));
};
