import fetch from "dva/fetch";
import { routerRedux } from "dva/router";
import { getAppStore } from "../app";
import queryString from "query-string";
import Config from "../common/config";

const codeMessage = {
	200: "服务器成功返回请求的数据。",
	201: "新增或修改数据成功。",
	202: "一个请求已经进入后台排队（异步任务）。",
	204: "删除数据成功。",
	400: "发出的请求有错误，服务器没有进行新增或修改数据的操作。",
	401: "用户没有权限（令牌、用户名、密码错误）。",
	403: "用户得到授权，但是访问是被禁止的。",
	404: "发出的请求针对的是不存在的记录，服务器没有进行操作。",
	406: "请求的格式不可得。",
	410: "请求的资源被永久删除，且不会再得到的。",
	422: "当创建一个对象时，发生一个验证错误。",
	500: "服务器发生错误，请检查服务器。",
	502: "网关错误。",
	503: "服务不可用，服务器暂时过载或维护。",
	504: "网关超时。"
};

function checkStatus(response) {
	if (response.status >= 200 && response.status < 300) {
		return response;
	}
	const errortext = codeMessage[response.status] || response.statusText;

	const error = new Error(errortext);
	error.name = response.status;
	error.response = response;
	throw error;
}

/**
 * Requests a URL, returning a promise.
 *
 * @param  {string} url       The URL we want to request
 * @param  {object} [options] The options we want to pass to "fetch"
 * @return {object}           An object containing either "data" or "err"
 */
export default function request(url, options) {
	const defaultOptions = {
		credentials: "include"
	};
	const newOptions = { ...defaultOptions, ...options };
	if (newOptions.method === "POST" || newOptions.method === "PUT") {
		if (!(newOptions.body instanceof FormData)) {
			newOptions.headers = {
				Accept: "application/json",
				"Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
				...newOptions.headers
			};
			// newOptions.body = JSON.stringify(newOptions.body);
			newOptions.body = queryString.stringify(newOptions.body);
		} else {
			// newOptions.body is FormData
			newOptions.headers = {
				Accept: "application/json",
				"Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
				...newOptions.headers
			};
		}
	}
	url = Config.apiPrefix + url;

	if ((!!window.ActiveXObject || "ActiveXObject" in window) && newOptions.method && newOptions.method.toUpperCase() === "GET") {
		// ie兼容 阻止get请求缓存
		newOptions.headers = {
			...newOptions.headers,
			"Cache-Control": "no-cache",
			"Pragma": "no-cache"
		};
	}

	return fetch(url, newOptions)
		.then(checkStatus)
		.then(response => {
			return response.json();
		})
		.catch(e => {
			const { dispatch } = getAppStore();
			const status = e.name;
			if (status === 401) {
				const { response } = e;
				try {
					response.json().then(function(data) {
						console.log(data);
						const { code } = data || {};
						if (String(code) === "4011003") {
							dispatch({
								type: "global/setAttrValue",
								payload: {
									multiUserModal: true
								}
							});
						} else if (code === 100404) {
							dispatch(
								routerRedux.push(
									"/user/startup?currentTab=licence"
								)
							);
						} else {
							dispatch({
								type: "login/goLogin"
							});
						}
					});
				} catch (e) {
					dispatch({
						type: "login/goLogin"
					});
				}
				return;
			}
			if (status === 403) {
				if (process.env.SYS_ENV === "development") {
					return;
				} else {
					dispatch(routerRedux.push("/bridge/exception/403"));
					return;
				}
			}
			if (status <= 504 && status >= 500) {
				if (process.env.SYS_ENV === "development") {
					return;
				} else {
					dispatch(routerRedux.push("/bridge/exception/500"));
					return;
				}
			}
			if (status >= 404 && status < 422) {
				if (process.env.SYS_ENV === "development") {
					console.log("404");
					return;
				} else {
					dispatch(routerRedux.push("/bridge/exception/404"));
				}
			}
		});
}

/**
 * @description: 下载文件请求
 * @param {*} url
 * @param {*} options
 * @param {*} reportName
 */
export function downloadFileHandle(url, options, reportName, fileType) {
	let name = `${reportName}.${fileType || "pdf"}`;
	if (reportName && reportName.indexOf(".") > -1) {
		name = reportName;
	}
	url = Config.apiPrefix + url;

	if ((!!window.ActiveXObject || "ActiveXObject" in window) && options.method && options.method.toUpperCase() === "GET") {
		// ie兼容 阻止get请求缓存
		options.headers = {
			...options.headers,
			"Cache-Control": "no-cache",
			"Pragma": "no-cache"
		};
	}

	return fetch(url, { ...options, credentials: "include" })
		.then(res => res.blob())
		.then(blob => {
			// const url = URL.createObjectURL(blob);
			// let a = document.createElement("a");
			// a.download = name;
			// a.href = url;
			// document.body.appendChild(a);
			// a.click();
			// a.remove(); // document.body.removeChild(a)
			// for IE
			if (window.navigator && window.navigator.msSaveOrOpenBlob) {
				window.navigator.msSaveOrOpenBlob(blob, name);
			} else { // for Non-IE (chrome, firefox etc.)
				const url = URL.createObjectURL(blob);
				let a = document.createElement("a");
				a.download = name;
				a.href = url;
				document.body.appendChild(a);
				a.click();
				a.remove(); // document.body.removeChild(a)
				URL.revokeObjectURL(url);
			}
		})
		.catch(err => {
			console.error(err);
		});
}

/**
 * @description: 下载5个进件文件请求
 * @param {*} url
 * @param {*} options
 * @param {*} reportName
 */
export function downloadAllFile(url, options) {

	if ((!!window.ActiveXObject || "ActiveXObject" in window) && options.method && options.method.toUpperCase() === "GET") {
		// ie兼容 阻止get请求缓存
		options.headers = {
			...options.headers,
			"Cache-Control": "no-cache",
			"Pragma": "no-cache"
		};
	}

	return fetch(url, { ...options, credentials: "include" })
		.then(res => res.blob())
		.then(blob => {
			// const url = URL.createObjectURL(blob);
			// let a = document.createElement("a");
			// a.download = "report.zip";
			// a.href = url;
			// document.body.appendChild(a);
			// a.click();
			// a.remove(); // document.body.removeChild(a)

			if (window.navigator && window.navigator.msSaveOrOpenBlob) {
				window.navigator.msSaveOrOpenBlob(blob, "report.zip");
			} else { // for Non-IE (chrome, firefox etc.)
				const url = URL.createObjectURL(blob);
				let a = document.createElement("a");
				a.download = "report.zip";
				a.href = url;
				document.body.appendChild(a);
				a.click();
				a.remove(); // document.body.removeChild(a)
				URL.revokeObjectURL(url);
			}
		})
		.catch(err => {
			console.error(err);
		});
}

/**
 * 文件上传请求拦截
*/
export function uploadIntercept(status, response) {
	const { dispatch } = getAppStore();
	console.log(status, response);
	if (status === 401) {
		const { code } = response || {};
		if (String(code) === "4011003") {
			dispatch({
				type: "global/setAttrValue",
				payload: {
					multiUserModal: true
				}
			});
		} else {
			dispatch({
				type: "login/goLogin"
			});
		}
	}

}
