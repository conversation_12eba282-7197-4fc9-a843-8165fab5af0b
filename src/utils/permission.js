import store, { getAppStore } from "../app";
import Cookies from "universal-cookie";

export function getFunctionList(menuCode) {
	let globalStore = getAppStore().getState().global;
	let { customTree } = globalStore;

	let functionList = [];
	if (customTree["menuTree"] && customTree["menuTree"].length) {
		customTree["menuTree"].map((item, index) => {
			item["children"] && item["children"].map((subItem, subIndex) => {
				if (subItem.code && subItem.code === menuCode) {
					functionList = subItem.functionList ? subItem.functionList : [];
				}
			});
		});
	}
	return functionList;
};

export function checkFunctionHasPermission(menuCode, functionCode) {

	const cookies = new Cookies();
	let account = cookies.get("_td_account_");
	if (account && account === "tongdun") {
		return true;
	}

	let globalStore = getAppStore().getState().global;
	let { customTree } = globalStore;

	let functionList = [];
	let hasPermission = false;
	if (customTree["menuTree"] && customTree["menuTree"].length) {
		customTree["menuTree"].map((item, index) => {
			item["children"] && item["children"].map((subItem, subIndex) => {
				if (subItem.code && subItem.code === menuCode) {
					functionList = subItem.functionList ? subItem.functionList : [];
					if (functionList.find(fItem => fItem.code === functionCode)) {
						hasPermission = true;
					}
				}
			});
		});
	}
	// return hasPermission;
	return process.env.SYS_ENV === "development" ? true : hasPermission;
}
