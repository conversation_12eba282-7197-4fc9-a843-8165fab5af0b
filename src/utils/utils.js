import moment from "moment";
import { message } from "antd";

export function fixedZero(val) {
	return val * 1 < 10 ? `0${val}` : val;
}

export function getTimeDistance(type) {
	const now = new Date();
	const oneDay = 1000 * 60 * 60 * 24;

	if (type === "today") {
		now.setHours(0);
		now.setMinutes(0);
		now.setSeconds(0);
		return [moment(now), moment(now.getTime() + (oneDay - 1000))];
	}

	if (type === "week") {
		let day = now.getDay();
		now.setHours(0);
		now.setMinutes(0);
		now.setSeconds(0);

		if (day === 0) {
			day = 6;
		} else {
			day -= 1;
		}

		const beginTime = now.getTime() - (day * oneDay);

		return [moment(beginTime), moment(beginTime + ((7 * oneDay) - 1000))];
	}

	if (type === "month") {
		const year = now.getFullYear();
		const month = now.getMonth();
		const nextDate = moment(now).add(1, "months");
		const nextYear = nextDate.year();
		const nextMonth = nextDate.month();

		return [moment(`${year}-${fixedZero(month + 1)}-01 00:00:00`), moment(moment(`${nextYear}-${fixedZero(nextMonth + 1)}-01 00:00:00`).valueOf() - 1000)];
	}

	if (type === "year") {
		const year = now.getFullYear();

		return [moment(`${year}-01-01 00:00:00`), moment(`${year}-12-31 23:59:59`)];
	}
}

export function searchToObject(search) {
	let pairs = search.substring(1).split("&"); let obj = {}; let pair; let i;
	for (i in pairs) {
		if (pairs[i] === "") { continue; }

		pair = pairs[i].split("=");
		obj[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
	}
	return obj;
}

export function getPlainNode(nodeList, parentPath = "") {
	const arr = [];
	nodeList.forEach((node) => {
		const item = node;
		item.path = `${parentPath}/${item.path || ""}`.replace(/\/+/g, "/");
		item.exact = true;
		if (item.children && !item.component) {
			arr.push(...getPlainNode(item.children, item.path));
		} else {
			if (item.children && item.component) {
				item.exact = false;
			}
			arr.push(item);
		}
	});
	return arr;
}

// 弹窗未完全关闭禁止再次提交
export function messageError(payload) {
	return new Promise((resolve) => {
		message.error(payload, () => {
			resolve(false);
		});
	});
}

// JS判断字符串长度（英文占1个字符，中文汉字占2个字符）
export function getStrLen(str) {
	let len = 0;
	for (let i = 0; i < str.length; i++) {
		let c = str.charCodeAt(i);
		// 单字节加1
		if ((c >= 0x0001 && c <= 0x007e) || (c >= 0xff60 && c <= 0xff9f)) {
			len++;
		} else {
			len += 2;
		}
	}
	return len;
}

export function hasRepeat(arr) {
	var hash = {};
	for (var i in arr) {
		if (hash[arr[i]]) {
			return true;
		}
		hash[arr[i]] = true;
	}
	return false;
}

export const findMenuInfoByPath = (menuTree, path, location) => {
	let menu = null;
	let subMenu = null;

	// 解析iframeView的参数设置选中效果
	if (path.startsWith("/bridge/iframeView")) {
		const { search, pathname } = location;
		if (search) {
			path = search.replace(/^\?path=/, "");
			if (path && path.includes("?")) {
				path = path.split("?")[0];
			}
			path = `${pathname}?path=${path}`;
		}
	}
	if (path) {
		for (let i = 0; i < menuTree.length; i++) {
			menu = menuTree[i];
			for (let j = 0; j < (menu.children || []).length; j++) {
				if (menu.children[j].path === path) {
					subMenu = menu.children[j];
					break;
				}
			}
			if (subMenu) {
				break;
			}
		}
		// if (!menu) {
		// 	menu = get(menuTree, "0");
		// }
		// if (!subMenu) {
		// 	subMenu = get(menuTree, "0.children.0");
		// }
		return {
			subMenu,
			menu
		};
	}
	return { subMenu, menu };
};

export function isNewTarget(path) {
	if (path.startsWith("http")) {
		try {
			const pathObj = new URL(path);
			const { search } = pathObj || {};
			if (search) {
				const { blankType } = searchToObject(pathObj.search) || {};
				if (blankType === "newTarget") {
					return true;
				}
			}
			return false;
		} catch (e) {
			return false;
		}
	}
	return false;
}

// 过滤需要新开窗口的链接
export function openNewTarget({ path, userInfo }) {
	if (path.indexOf("{esAuth}") > -1) {
		const csrf = encodeURIComponent(sessionStorage.getItem("_csrf_"));
		const ac = userInfo.token;
		path = path.replace("{esAuth}", `ac=${ac}&cf=${csrf}`);
	}
	let a = document.createElement("a");
	a.href = path;
	a.target = "_blank";
	document.body.appendChild(a);
	a.click();
	a.remove();
}

export function flatten(arr) {
	var res = [];
	for (let i = 0, length = arr.length; i < length; i++) {
		if (Array.isArray(arr[i])) {
			res = res.concat(flatten(arr[i]));
		} else {
			res.push(arr[i]);
		}
	}
	return res;
}

// 需要展示app的路由写在这里
export const isAppListVisible = (pathname = window.location.pathname) => [

].some(item => pathname.includes(item) === true);
