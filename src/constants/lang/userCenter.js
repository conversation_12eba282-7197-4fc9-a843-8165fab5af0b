import store, { getLanguage } from "../../app";

// let globalStore = store.getState().global;
// let { personalMode } = globalStore;

const common = (field) => {
	let lang = getLanguage();
	let params = {
		"personalSetting": {
			"cn": "个人设置",
			"en": "Personal Settings"
		},
		"modifyPassword": {
			"cn": "修改密码",
			"en": "Modify password"
		},
		"loginHistory": {
			"cn": "登录历史",
			"en": "Login history"
		},
		"license": {
			"cn": "License管理",
			"en": "License"
		}
	};
	return params[field][lang];
};

const personal = (field) => {
	let lang = getLanguage();
	let params = {
		"avatar": {
			"cn": "头像",
			"en": "Avatar"
		},
		"imgTip": {
			"cn": "支持JPG、PNG、JPEG格式图片，文件大小不超过1M",
			"en": "JPG, PNG, JPEG images are supported. File size is less than 1M"
		},
		"account": {
			"cn": "登录账号",
			"en": "Account"
		},
		"userName": {
			"cn": "用户名",
			"en": "User Name"
		},
		"userNamePlaceholder": {
			"cn": "请输入用户名",
			"en": "Please enter username"
		},
		"mobile": {
			"cn": "手机号",
			"en": "Mobile"
		},
		"mobilePlaceholder": {
			"cn": "请输入手机号",
			"en": "Please enter mobile"
		},
		"email": {
			"cn": "邮箱",
			"en": "Email"
		},
		"emailPlaceholder": {
			"cn": "请输入邮箱地址",
			"en": "Please enter email"
		},
		"organization": {
			"cn": "所属机构",
			"en": "Organization"
		},
		"app": {
			"cn": "应用权限",
			"en": "App"
		},
		"gender": {
			"cn": "性别",
			"en": "Gender"
		},
		"secret": {
			"cn": "保密",
			"en": "Secret"
		},
		"male": {
			"cn": "男",
			"en": "Male"
		},
		"female": {
			"cn": "女",
			"en": "Female"
		},
		"expirationTime": {
			"cn": "过期时间",
			"en": "Expiration time"
		},
		"signature": {
			"cn": "签名",
			"en": "Signature"
		},
		"signaturePlaceholder": {
			"cn": "请输入签名",
			"en": "Please enter signature"
		},
		"save": {
			"cn": "保存",
			"en": "Save"
		},
		// 校验
		"wrongNumberTip": {
			"cn": "手机号码填写有误",
			"en": "Wrong number for mobile phone"
		},
		"wrongEmailTip": {
			"cn": "邮箱填写有误",
			"en": "Email is wrong"
		},
		"modifySuccessTip": {
			"cn": "修改成功",
			"en": "Modify successfully"
		},
		"avatarErrorTip": {
			"cn": "头像图片格式有误",
			"en": "The avatar image is misformatted"
		},
		"avatarSizeTip": {
			"cn": "头像大小不能超过1M",
			"en": "You can't have more than 1M avatar"
		}

	};
	return params[field][lang];
};

const password = (field) => {
	let lang = getLanguage();
	let params = {
		"originalPassword": {
			"cn": "原密码：",
			"en": "Original Password:"
		},
		"newPassword": {
			"cn": "新密码：",
			"en": "New Password:"
		},
		"confirmNewPassword": {
			"cn": "确认新密码：",
			"en": "Confirm new password:"
		},
		"originalPasswordPlaceholder": {
			"cn": "请输入原密码",
			"en": "Please enter your original password"
		},
		"newPasswordPlaceholder": {
			"cn": "请输入新密码",
			"en": "Please enter a new password"
		},
		"confirmNewPasswordPlaceholder": {
			"cn": "请确认新密码",
			"en": "Please confirm the new password"
		},
		"modify": {
			"cn": "修改",
			"en": "Modify"
		},
		// 验证
		"passwordEmpty1": {
			"cn": "原密码不能为空",
			"en": "Original password cannot be empty"
		},
		"passwordEmpty2": {
			"cn": "新密码不能为空",
			"en": "New password cannot be empty"
		},
		"passwordEmpty3": {
			"cn": "两次输入的密码不一致",
			"en": "The passwords entered are not the same"
		},
		"passwordEmpty4": {
			"cn": "修改密码成功",
			"en": "Password changed successfully"
		},
		"regPwd1": {
			"cn": "密码必须包含大小写字母、数字、特殊字符，长度8~18位",
			"en": "The password must contain uppercase and lowercase letters, numbers, special characters, 8-18 digits in length"
		},
		"regPwd2": {
			"cn": "确认密码必须包含大小写字母、数字、特殊字符，长度8~18位",
			"en": "The confirmation password must contain uppercase and lowercase letters, numbers and special characters, with a length of 8-18 digits"
		}
	};
	return params[field][lang];
};

const history = (field) => {
	let lang = getLanguage();
	let params = {
		"account": {
			"cn": "登录账号",
			"en": "Account"
		},
		"ip": {
			"cn": "IP地址",
			"en": "IP"
		},
		"loginTime": {
			"cn": "登录时间",
			"en": "Login Time"
		}
	};
	return params[field][lang];
};

const license = (field) => {
	let lang = getLanguage();
	let params = {
		"currentLicense": {
			"cn": "当前License：",
			"en": "Current License:"
		},
		"remainTime": {
			"cn": "剩余时间：",
			"en": "Remaining time:"
		},
		"modify": {
			"cn": "点击修改",
			"en": "Modify"
		},
		// 修改弹窗
		"modifyLicense": {
			"cn": "修改license",
			"en": "Modify License"
		},
		"enterLicensePlaceholder": {
			"cn": "请输入license",
			"en": "Please enter license"
		},
		"enterKeyPlaceholder": {
			"cn": "请输入公钥",
			"en": "Please enter the public key"
		},
		// 校验以及返回消息
		"activateSuccess": {
			"cn": "校验成功",
			"en": "Activate the success"
		},
		"empty1": {
			"cn": "license不能为空！",
			"en": "License cannot be empty!"
		},
		"empty2": {
			"cn": "公钥不能为空！",
			"en": "The public key cannot be empty!"
		}
	};
	return params[field][lang];
};

export default {
	common,
	personal,
	password,
	history,
	license
};
