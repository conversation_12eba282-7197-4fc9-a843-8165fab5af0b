import store, { getLanguage } from "../../app";

const common = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"permissionSet": {
			"cn": "权限设置",
			"en": "Authority Setting"
		},
		"saveChanges": {
			"cn": "保存修改",
			"en": "Save Changes"
		},
		"noMenuList": {
			"cn": "当前组内暂无菜单列表",
			"en": "No menu list within the current group"
		},
		"noMenuFun": {
			"cn": "暂无菜单和功能权限",
			"en": "No permission menu and function"
		},
		"selectIn": {
			"cn": "请选择机构",
			"en": "Please select organization"
		}
	};
	return params[field][lang];
};

const tipInfo = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"tip1": {
			"cn": "更新机构菜单成功",
			"en": "The menu update organization"
		},
		"tip2": {
			"cn": "更新角色菜单成功",
			"en": "Update menu successful role"
		}
	};
	return params[field][lang];
};

const tableSet = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"menu": {
			"cn": "菜单",
			"en": "Menu"
		},
		"menuFeatures": {
			"cn": "菜单功能",
			"en": "Menu functions"
		},
		"menuCode": {
			"cn": "菜单code",
			"en": "Menu Code"
		}
	};
	return params[field][lang];
};

export default {
	common,
	tipInfo,
	tableSet
};
