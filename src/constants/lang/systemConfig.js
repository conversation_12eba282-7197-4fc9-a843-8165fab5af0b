import store, { getLanguage } from "../../app";

const common = field => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		pageTitle: {
			cn: "参数配置",
			en: "Parameter Setting"
		},
		addConfig: {
			cn: "新增",
			en: "Add"
		}
	};
	return params[field][lang];
};

const table = field => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();

	let params = {
		configName: {
			cn: "参数名称",
			en: "Name"
		},
		configType: {
			cn: "参数类型",
			en: "Category"
		},
		configValue: {
			cn: "参数值",
			en: "Value"
		},
		description: {
			cn: "参数说明",
			en: "Description"
		},
		status: {
			cn: "状态",
			en: "Status"
		},
		operator: {
			cn: "操作",
			en: "Action"
		},
		enabled: {
			cn: "已启用",
			en: "Enabled"
		},
		disabled: {
			cn: "已禁用",
			en: "Disabled"
		},
		edit: {
			cn: "编辑",
			en: "Edit"
		},
		delete: {
			cn: "删除",
			en: "Delete"
		}
	};
	return params[field][lang];
};
const modal = field => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();

	let params = {
		addModalTitle: {
			cn: "新增参数配置",
			en: "Add Parameter Setting"
		},
		modifyModalTitle: {
			cn: "修改参数配置",
			en: "Modify Parameter Setting"
		},
		configName: {
			cn: "参数名称",
			en: "Name"
		},
		configNamePlaceholder: {
			cn: "请输入参数名称",
			en: "Please enter parameter name"
		},
		configValue: {
			cn: "参数值",
			en: "Parameter Value"
		},
		configValuePlaceholder: {
			cn: "请输入配置参数",
			en: "Please enter config value"
		},
		configType: {
			cn: "参数类型",
			en: "Category"
		},
		configDetail: {
			cn: "参数值",
			en: "Value"
		},
		description: {
			cn: "参数说明",
			en: "Description"
		},
		descriptionPlaceholder: {
			cn: "请输入参数说明",
			en: "Please enter description"
		},
		status: {
			cn: "状态",
			en: "Status"
		},
		switchTypeTip: {
			cn: "切换类型，参数配置会被清空。",
			en: "When you switch type, config value will be cleared."
		},
		on: {
			cn: "开",
			en: "On"
		},
		off: {
			cn: "关",
			en: "Off"
		},
		configNotJson: {
			cn: "参数值不是标准化JSON",
			en: "Configuration details are not standardized JSON"
		}
	};
	return params[field][lang];
};
const tip = field => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();

	let params = {
		configNameEmptyTip: {
			cn: "参数名称不能为空",
			en: "Parameter name cannot be empty"
		},
		configValueEmptyTip: {
			cn: "参数值不能为空",
			en: "Parameter value cannot be empty"
		},
		configDescriptionEmptyTip: {
			cn: "参数说明不能为空",
			en: "Parameter description cannot be empty"
		},
		isNotJson: {
			cn: "参数值不是标准的Json格式",
			en: "The config value is not standard Json"
		},
		deleteConfigRemind: {
			cn: "删除参数提醒",
			en: "Delete parameter reminder"
		},
		deleteConfigSuccessfully: {
			cn: "删除参数项成功",
			en: "Parameter item deleted successfully"
		},
		modifyStatusSuccessfully: {
			cn: "修改状态成功",
			en: "Modify status successfully"
		},
		clickViewConfigDetail: {
			cn: "点击查看详情",
			en: "Click view config detail"
		},
		configDetail: {
			cn: "参数值",
			en: "Parameter value"
		}
	};
	return params[field][lang];
};

const deleteConfig = name => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;

	let cn = `您真的要删除${name}吗？`;
	let en = `Do you really want to delete ${name}?`;
	return getLanguage() === "cn" ? cn : en;
};

const formSet = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"status": {
			"cn": "请选择状态",
			"en": "Please select"
		},
		"configKey": {
			"cn": "请输入参数名称",
			"en": "Please enter parameter name"
		},
		"configValue": {
			"cn": "请输入参数值",
			"en": "Please enter parameter value"
		},
		"description": {
			"cn": "请输入参数说明",
			"en": "Please enter description"
		},
		"type": {
			"cn": "请选择参数类型",
			"en": "Please select type"
		},
		"search": {
			"cn": "搜索",
			"en": "Search"
		},
		"reset": {
			"cn": "重置",
			"en": "Reset"
		}
	};
	return params[field][lang];
};
export default {
	common,
	table,
	modal,
	tip,
	deleteConfig,
	formSet
};
