import store, { getLanguage } from "../../app";

const common = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		title: {
			cn: "字典配置",
			en: "Codebook Setting"
		}
	};
	return params[field][lang];
};

const tipInfo = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"tip1": {
			"cn": "点击查看详情",
			"en": "Click for details."
		},
		"tip2": {
			"cn": "描述详情",
			"en": "Description details"
		},
		"tip3": {
			"cn": "确定删除当前字典配置吗？",
			"en": "Confirm delete the current configuration?"
		}
	};
	return params[field][lang];
};

const tableSet = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"dictionaryId": {
			"cn": "字典标识",
			"en": "Dictionary identification"
		},
		"name": {
			"cn": "唯一标识",
			"en": "Name"
		},
		"displayName": {
			"cn": "显示名称",
			"en": "Display Name"
		},
		"group": {
			"cn": "字典分组",
			"en": "Group"
		},
		"value": {
			"cn": "字典详情",
			"en": "Value"
		},
		"description": {
			"cn": "字典描述",
			"en": "Dict Description"
		},
		"modifier": {
			"cn": "修改人",
			"en": "Modifier"
		},
		"modifyTime": {
			"cn": "修改时间",
			"en": "Modification time"
		},
		"operation": {
			"cn": "操作",
			"en": "Action"
		},
		"modify": {
			"cn": "编辑",
			"en": "Modify"
		},
		"delete": {
			"cn": "删除",
			"en": "Delete"
		}
	};
	return params[field][lang];
};

const formSet = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"placeholder1": {
			"cn": "请选择",
			"en": "Please select"
		},
		"placeholder2": {
			"cn": "在当前条件下搜索",
			"en": "Current condition"
		},
		"placeholderGroup": {
			"cn": "请选择字典分组",
			"en": "Please select dictionary group"
		},
		"placeholderId": {
			"cn": "请输入字典标识",
			"en": "Please enter dictionary ID"
		},
		"placeholderInfo": {
			"cn": "请输入字典详情",
			"en": "Please enter dictionary details"
		},
		"placeholderDes": {
			"cn": "请输入字典描述",
			"en": "Please enter a dictionary description"
		},
		"name": {
			"cn": "唯一标识",
			"en": "Name"
		},
		"displayName": {
			"cn": "显示名称",
			"en": "Display Name"
		},
		"value": {
			"cn": "字典详情",
			"en": "Value"
		},
		"description": {
			"cn": "描述",
			"en": "Description"
		},
		"search": {
			"cn": "搜索",
			"en": "Search"
		},
		"add": {
			"cn": "新增",
			"en": "Add"
		},
		"all": {
			"cn": "全部",
			"en": "All"
		},
		"system": {
			"cn": "系统",
			"en": "System"
		},
		"auditForm": {
			"cn": "表单",
			"en": "AuditForm"
		}
	};
	return params[field][lang];
};

const addDict = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"title": {
			"cn": "新增字典配置",
			"en": "Add Dictionary Configuration"
		},
		"title1": {
			"cn": "修改字典配置",
			"en": "Modify Dictionary Configuration"
		},
		"name": {
			"cn": "唯一标识",
			"en": "Name"
		},
		"namePlaceholder": {
			"cn": "请输入唯一标识",
			"en": "Enter name"
		},
		"dictionaryId": {
			"cn": "字典标识",
			"en": "Dictionary identification"
		},
		"dictionaryIdPlaceholder": {
			"cn": "最长32个汉字(字符)长度",
			"en": "The maximum length of 32 Chinese characters (characters)"
		},
		"displayName": {
			"cn": "显示名称",
			"en": "Display Name"
		},
		"displayNamePlaceholder": {
			"cn": "请输入显示名称",
			"en": "Enter display name"
		},
		"value": {
			"cn": "字典详情",
			"en": "Value"
		},
		"valuePlaceholder": {
			"cn": "请填写JSON数组格式的数据",
			"en": "Enter the data in JSON Array format"
		},
		"group": {
			"cn": "所在分组",
			"en": "Group"
		},
		"groupPlaceholder": {
			"cn": "请选择所在分组",
			"en": "Select group"
		},
		"dictionaryGroup": {
			"cn": "字典分组",
			"en": "Dictionary grouping"
		},
		"dictionaryGroupPlaceholder": {
			"cn": "分组由数据字典配置",
			"en": "Grouping is configured by the data dictionary"
		},
		"description": {
			"cn": "字典描述",
			"en": "Dict Description"
		},
		"descriptionPlaceholder": {
			"cn": "请输入字典描述",
			"en": "Enter dict description content"
		},
		"standMode": {
			"cn": "标准模式",
			"en": "Standard mode"
		},
		"jsonMode": {
			"cn": "json模式",
			"en": "JSON mode"
		},
		"showEnName": {
			"cn": "英文显示名称",
			"en": "English display name"
		}
	};
	return params[field][lang];
};

const valueDialog = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"title": {
			"cn": "字典详情",
			"en": "Dictionary Configuration Detail"
		},
		"alertInfo": {
			"cn": "字典详情说明",
			"en": "Configuration Description"
		},
		"alertInfoContent": {
			"cn": "（唯一标识）name; （中文显示名）dName; （英文显示名）enDName",
			"en": "(unique identifier) name; (Chinese display name) dName; (English display name) enDName"
		}
	};
	return params[field][lang];
};

const message = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"fillRequired": {
			"cn": "请填写必填项",
			"en": "Fill in the required fields."
		},
		"max32": {
			"cn": "最长32个汉字(字符)长度",
			"en": "The maximum length of 32 Chinese characters (characters)"
		},
		"JsonFormat": {
			"cn": "字典详情请填写JSON格式的数据",
			"en": "The configuration value must be in JSON format."
		},
		"arrayFormat": {
			"cn": "字典详情请填写JSON数组格式的数据",
			"en": "For dictionary details, please fill in the data in JSON array format."
		},
		"completeJson": {
			"cn": "请填写完整的JSON数组格式的数据,包含键值name,dName",
			"en": "Fill in the complete JSON Array format data."
		},
		"completeEnJson": {
			"cn": "请填写完整的数组JSON格式的数据,存在部分英文显示名称缺失",
			"en": "Please fill in the complete array JSON format data. Some English display names are missing."
		},
		"selectGroup": {
			"cn": "请选择分组",
			"en": "Please select group."
		},
		"currentKey": {
			"cn": "存在键值",
			"en": "Key exists"
		},
		"jsonKey": {
			"cn": "只支持name，dName，enDName",
			"en": "Only supports name, dName, enDName."
		},
		"max200": {
			"cn": "描述不能超过200字",
			"en": "Description cannot exceed 200 words."
		}
	};
	return params[field][lang];
};

export default {
	common,
	tipInfo,
	tableSet,
	formSet,
	addDict,
	valueDialog,
	message
};
