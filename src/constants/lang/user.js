import store, { getLanguage } from "../../app";

const common = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"title": {
			"cn": "用户列表",
			"en": "User List"
		}
	};
	return params[field][lang];
};

const tipInfo = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"deleteUser": {
			"cn": "删除用户成功",
			"en": "Delete the user successfully"
		},
		"resetPwd": {
			"cn": "重置密码成功",
			"en": "Password reset successfully"
		},
		"updateSuccess": {
			"cn": "修改用户状态成功",
			"en": "Modify the user state of success"
		},
		"sureResetPwd": {
			"cn": "确定要重置当前用户密码吗？",
			"en": "Sure you want to reset the current user password?"
		},
		"sureDeleteUser": {
			"cn": "确定要删除当前用户吗？",
			"en": "Sure you want to delete the current user?"
		},
		"wrongPhoneTip": {
			"cn": "手机号码填写有误",
			"en": "Wrong number for mobile phone"
		},
		"wrongEmailTip": {
			"cn": "邮箱填写有误",
			"en": "Email is wrong"
		}
	};
	return params[field][lang];
};

const searchParams = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"selectIn": {
			"cn": "请选择机构",
			"en": "Please select organization"
		},
		"allIn": {
			"cn": "全部机构",
			"en": "All organization"
		},
		"loginId": {
			"cn": "登录账号",
			"en": "Login ID"
		},
		"username": {
			"cn": "用户名",
			"en": "Username"
		},
		"enterName": {
			"cn": "输入登录名搜索",
			"en": "Enter login name"
		},
		"enterUsername": {
			"cn": "输入用户名搜索",
			"en": "Enter the user name"
		},
		"addUser": {
			"cn": "添加用户",
			"en": "Add User"
		},
		"search": {
			"cn": "搜索",
			"en": "Search"
		}
	};
	return params[field][lang];
};

const table = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"name": {
			"cn": "用户名/登录名",
			"en": "User Name/Login ID"
		},
		"agency": {
			"cn": "所在机构/角色",
			"en": "Organization/Role"
		},
		"permission": {
			"cn": "应用权限",
			"en": "Application Permissions"
		},
		"expirationTime": {
			"cn": "过期时间",
			"en": "Expiration Time"
		},
		"lastLoginTime": {
			"cn": "最后登录时间",
			"en": "Last Login Time"
		},
		"lastLoginIp": {
			"cn": "最后登录IP",
			"en": "Last Login IP"
		},
		"status": {
			"cn": "状态",
			"en": "Status"
		},
		"operation": {
			"cn": "操作",
			"en": "Action"
		},
		"resetPwd": {
			"cn": "重置密码",
			"en": "Reset Password"
		},
		"edit": {
			"cn": "编辑",
			"en": "Modify"
		},
		"delete": {
			"cn": "删除",
			"en": "Delete"
		},
		"normal": {
			"cn": "启用",
			"en": "Enable"
		},
		"disable": {
			"cn": "禁用",
			"en": "Disable"
		},
		"loginId": {
			"cn": "登录名",
			"en": "Login ID"
		}
	};
	return params[field][lang];
};

const modal = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"nameEmpty": {
			"cn": "登录账号不能为空！",
			"en": "Login name cannot be empty!"
		},
		"userNameEmpty": {
			"cn": "用户名不能为空！",
			"en": "User name cannot be empty!"
		},
		"orgEmpty": {
			"cn": "机构不能为空！",
			"en": "Organization cannot be empty!"
		},
		"roleEmpty": {
			"cn": "角色不能为空！",
			"en": "Role cannot be empty!"
		},
		"expirationEmpty": {
			"cn": "过期时间不能为空!",
			"en": "The expiration time should not be empty!"
		},
		"newSuccess": {
			"cn": "新增用户成功",
			"en": "New user successfully"
		},
		"selectIn": {
			"cn": "请选择机构",
			"en": "Please select organization"
		},
		"modifySuccess": {
			"cn": "修改用户成功",
			"en": "Modify the user successfully"
		},
		"addUser": {
			"cn": "添加用户",
			"en": "Add User"
		},
		"modifyUser": {
			"cn": "修改用户",
			"en": "Modify User"
		},
		"loginId": {
			"cn": "登录账号",
			"en": "Login ID"
		},
		"inputLoginId": {
			"cn": "请输入登录账号",
			"en": "Please enter the login ID"
		},
		"phone": {
			"cn": "手机号",
			"en": "Mobile"
		},
		"phonePlaceHolder": {
			"cn": "请输入手机号",
			"en": "Mobile"
		},
		"email": {
			"cn": "邮箱",
			"en": "Email"
		},
		"dataPlatForm": {
			"cn": "数据分析角色",
			"en": "Data analysis role"
		},
		"dataPlatFormHolder": {
			"cn": "请选择数据分析角色",
			"en": "Please select data analysis role"
		},
		"emailPlaceHolder": {
			"cn": "请输入邮箱",
			"en": "Email"
		},
		"userName": {
			"cn": "用户名",
			"en": "User Name"
		},
		"inputUserName": {
			"cn": "请输入用户昵称",
			"en": "Please enter the user nickname"
		},
		"chooseApp": {
			"cn": "选择应用",
			"en": "Application"
		},
		"inputApp": {
			"cn": "请选择应用，多选",
			"en": "Please choose applications (multiple choices)"
		},
		"selectInstri": {
			"cn": "选择机构",
			"en": "Organization"
		},
		"inputInstri": {
			"cn": "请选择机构",
			"en": "Please choose one organization"
		},
		"selectRole": {
			"cn": "选择角色",
			"en": "Role"
		},
		"inputSelectRole": {
			"cn": "请选择角色，多选",
			"en": "Please choose roles (multiple choices)"
		},
		"expirationTime": {
			"cn": "过期时间",
			"en": "Expiration time"
		},
		"inputExpirationTime": {
			"cn": "请选择过期时间",
			"en": "Choose"
		},
		"sex": {
			"cn": "性别",
			"en": "Sex"
		},
		"secret": {
			"cn": "保密",
			"en": "Secret"
		},
		"male": {
			"cn": "男",
			"en": "Male"
		},
		"female": {
			"cn": "女",
			"en": "Female"
		}
	};
	return params[field][lang];
};

export default {
	common,
	tipInfo,
	searchParams,
	table,
	modal
};
