import store, { getLanguage} from "../../app";

const common = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	// let lang = personalMode.lang === "cn" ? "cn" : "en";
	let lang = getLanguage();
	let params = {
		"logo": {
			"cn": "统一登录",
			"en": "Unified Login"
		},
		"allApplication": {
			"cn": "全部应用",
			"en": "All Application"
		},
		"personalSet": {
			"cn": "个人设置",
			"en": "Personal Settings"
		},
		"changePwd": {
			"cn": "修改密码",
			"en": "Change the password"
		},
		"exit": {
			"cn": "退出",
			"en": "Exit"
		},
		"noNickname": {
			"cn": "暂无昵称",
			"en": "No nickname"
		}
	};
	return params[field][lang];
};

export default {
	common
};
