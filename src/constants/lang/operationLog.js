import store, { getLanguage } from "../../app";

const common = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		title: {
			cn: "操作日志",
			en: "Operation Log"
		},
		searchPlaceholder: {
			cn: "请选择菜单名称",
			en: "Select menu name"
		},
		inputPlaceholder1: {
			"cn": "操作人",
			"en": "Enter operator"
		},
		inputPlaceholder2: {
			"cn": "业务名称",
			"en": "Enter Business name"
		},
		search: {
			"cn": "搜索",
			"en": "Search"
		}
	};
	return params[field][lang];
};
const table = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"menuName": {
			"cn": "菜单名称",
			"en": "Menu Name"
		},
		"operationType": {
			"cn": "操作类型",
			"en": "Operation Type"
		},
		"businessDescription": {
			"cn": "业务描述",
			"en": "Business Description"
		},
		"businessDescriptionContent": {
			"cn": "业务描述详情",
			"en": "Business Description Detail"
		},
		"operator": {
			"cn": "操作人",
			"en": "Operator"
		},
		"operatorTime": {
			"cn": "操作时间",
			"en": "Operating Time"
		},
		"detail": {
			"cn": "详情",
			"en": "Detail"
		},
		"detailTipInfo": {
			"cn": "操作日志详情",
			"en": "Operation Log Detail"
		},
		"noDetail": {
			"cn": "暂无详情",
			"en": "No Detail"
		},
		"logDetail": {
			"cn": "日志详情",
			"en": "Log Detail"
		}
	};
	return params[field][lang];
};

export default {
	common,
	table
};
