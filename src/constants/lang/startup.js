import store, { getLanguage} from "../../app";

// let globalStore = store.getState().global;
// let { personalMode } = globalStore;

const common = field => {
	let lang = getLanguage();// personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		systermConfig: {
			cn: "系统设置",
			en: "Systerm Config"
		},
		systermConfigDes: {
			cn: "这里可以进行License、解决方案管理。",
			en: "License Manage & Solution Manage"
		},
		return: {
			cn: "返回",
			en: "return"
		},
		nowLicence: {
			cn: "当前license",
			en: "License Now"
		},
		restTime: {
			cn: "剩余时间",
			en: "Rest Time"
		},
		overdueTime: {
			cn: "过期时间",
			en: "Overdue Time"
		},
		maxAppName: {
			cn: "最大应用数量",
			en: "Max App Number"
		},
		ok: {
			cn: "确定",
			en: "OK"
		},
		tip: {
			cn: "提升",
			en: "tips"
		},
		chineseName: {
			cn: "中文名称",
			en: "Chinese Name"
		},
		chineseNamePlaceholder: {
			cn: "中文名称",
			en: "please enter chinese name"
		},
		englishName: {
			cn: "英文名称",
			en: "English Name"
		},
		englishNamePlaceholder: {
			cn: "请输入英文名称",
			en: "please enter an english name"
		},
		noSolution: {
			cn: "暂无解决方案，请导入。",
			en: "There is no solution,please import"
		},
		solutionDetail: {
			cn: "解决方案详情",
			en: "Solution Detail"
		},
		clickDetail: {
			cn: "点击查看",
			en: "Click To View"
		},
		importSolution: {
			cn: "导入解决方案",
			en: "Import Solution"
		},
		save: {
			cn: "保存",
			en: "Save"
		},
		addMenu: {
			cn: "添加菜单",
			en: "Add a menu"
		},
		uploadFailed: {
			cn: "上传失败",
			en: "upload failed"
		},
		uploadButtonText: {
			cn: "点击上传",
			en: "Click To Upload"
		},
		groupList: {
			cn: "分组列表",
			en: "Group List"
		},
		enterLicense: {
			cn: "请输入License!",
			en: "please enter license"
		},
		enterPublicKey: {
			cn: "请输入公钥!",
			en: "please enter publick key"
		},
		licenceWarning: {
			cn: "license缺失，请补充license！",
			en: "Miss license! please enter license！"
		},
		modifyLicence: {
			cn: "修改license",
			en: "Miss license! please enter license！"
		},
		enterLicence: {
			cn: "请输入license",
			en: "please enter license！"
		},
		config: {
			cn: "配置",
			en: "config"
		},

		modify: {
			cn: "修改",
			en: "Modify"
		},

		success: {
			cn: "成功",
			en: "success"
		},
		delete: {
			cn: "删除",
			en: "delete"
		},
		cancel: {
			cn: "取消",
			en: "cancel"
		}
	};
	return params[field][lang];
};

export default {
	common
};
