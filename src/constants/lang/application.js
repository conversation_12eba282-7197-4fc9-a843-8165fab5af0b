import store, { getLanguage } from "../../app";

const common = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();

	let params = {
		"title": {
			"cn": "应用管理",
			"en": "Application Management"
		},
		"addApplication": {
			"cn": "新增",
			"en": "Add"
		},
		"submit": {
			"cn": "确定",
			"en": "Ok"
		},
		"cancel": {
			"cn": "取消",
			"en": "Cancel"
		},
		"search": {
			"cn": "搜索",
			"en": "Search"
		},
		"reset": {
			"cn": "重置",
			"en": "Reset"
		}
	};
	return params[field][lang];
};
const table = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	// let lang = personalMode.lang === "cn" ? "cn" : "en";
	let lang = getLanguage();
	let params = {
		"appName": {
			"cn": "应用名称",
			"en": "Application Name"
		},
		"appIdentifier": {
			"cn": "应用标识",
			"en": "Application ID"
		},
		"filterRequest": {
			"cn": "过滤请求",
			"en": "Filter Request"
		},
		"filterRequestInfo": {
			"cn": "一键过滤应用请求",
			"en": "One-click filtering application request"
		},
		"filterData": {
			"cn": "过滤数据",
			"en": "Filter Data"
		},
		"filterDataInfo": {
			"cn": "打开则过滤请求后保留数据",
			"en": "Open to save data after filtering request"
		},
		"operation": {
			"cn": "操作",
			"en": "Action"
		},
		"delete": {
			"cn": "删除",
			"en": "Delete"
		},
		"edit": {
			"cn": "修改",
			"en": "Modify"
		},
		"switchControl1": {
			"cn": "启用",
			"en": "Enable"
		},
		"switchControl2": {
			"cn": "禁用",
			"en": "Disable"
		}
	};
	return params[field][lang];
};

const modal = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	// let lang = personalMode.lang === "cn" ? "cn" : "en";
	let lang = getLanguage();
	let params = {
		"title": {
			"cn": "新增应用",
			"en": "Add Application"
		},
		"title1": {
			"cn": "修改应用",
			"en": "Modify Application"
		},
		"displayName": {
			"cn": "应用名称",
			"en": "Display Name"
		},
		"timeZone": {
			"cn": "时区",
			"en": "TimeZone"
		},
		"displayNamePlaceholder": {
			"cn": "请输入应用名称",
			"en": "Please enter an app name"
		},
		"timeZonePlaceholder": {
			"cn": "请选择时区",
			"en": "Please select the time zone"
		},
		"identifier": {
			"cn": "应用标识",
			"en": "Identifier"
		},
		"identifierPlaceholder": {
			"cn": "请输入应用标识",
			"en": "Please enter the app ID"
		},
		"description": {
			"cn": "应用描述",
			"en": "Description"
		},
		"descriptionPlaceholder": {
			"cn": "应用描述",
			"en": "Please enter description"
		}
	};
	return params[field][lang];
};

const messageInfo = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	// let lang = personalMode.lang === "cn" ? "cn" : "en";
	let lang = getLanguage();
	let params = {
		"submitMeg": {
			"cn": "请填写必须字段",
			"en": "Please fill in the required fields"
		},
		"submitMeg1": {
			"cn": "应用名称含有非法字符，请重新填写（仅允许输入中文、英文、数字、'_'和'.'）",
			"en": "Name contains illegal characters, please re-fill(Only Chinese, English, numbers, '_' and '.' are allowed)"
		},
		"submitMeg2": {
			"cn": "应用标识含有非法字符，请重新填写（仅允许输入英文、数字、'_'和'.'）",
			"en": "Identifier contains illegal characters, please re-fill(Only English, numbers, '_' and '.' are allowed)"
		},
		"submitMeg3": {
			"cn": "应用描述不能超过512个字符",
			"en": "Description cannot exceed 512 characters"
		},
		"submitMeg4": {
			"cn": "应用名称不能超过32个字符",
			"en": "Name cannot exceed 32 characters"
		},
		"submitMeg5": {
			"cn": "应用标识不能超过32个字符",
			"en": "Identifier cannot exceed 32 characters"
		},
		"warningCode": {
			"cn": "应用标识不能是all",
			"en": "Identifier contains illegal characters all"
		},
		"name": {
			"cn": "应用名称",
			"en": "Name"
		},
		"identifier": {
			"cn": "应用标识",
			"en": "ID"
		},
		"description": {
			"cn": "应用描述",
			"en": "Description"
		},
		"permissionMes": {
			"cn": "没有权限",
			"en": "Permission denied"
		}
	};
	return params[field][lang];
};

export default {
	common,
	table,
	modal,
	messageInfo
};
