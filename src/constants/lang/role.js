import store, { getLanguage } from "../../app";

const common = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"title": {
			"cn": "角色管理",
			"en": "Role Management"
		},
		"title2": {
			"cn": "请选择机构",
			"en": "Please select organization"
		},
		"title3": {
			"cn": "当前机构下暂无角色",
			"en": "No role under the current organization"
		},
		"btn": {
			"cn": "添加角色",
			"en": "Add Roles"
		}
	};
	return params[field][lang];
};

const tipInfo = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"removeSuccess": {
			"cn": "删除角色成功",
			"en": "Remove roles successfully"
		},
		"sureDelete": {
			"cn": "确定要删除当前角色吗？",
			"en": "Sure you want to delete the current roles?"
		},
		"noPower": {
			"cn": "没有权限",
			"en": "Have no legal power"
		},
		"checkDetail": {
			"cn": "查看详情",
			"en": "View detail"
		},
		"name": {
			"cn": "角色名称",
			"en": "Role name"
		},
		"identity": {
			"cn": "角色标识",
			"en": "Role ID"
		},
		"creationTime": {
			"cn": "创建时间",
			"en": "Created time"
		},
		"modifyTime": {
			"cn": "修改时间",
			"en": "Modify time"
		}
	};
	return params[field][lang];
};

const modal = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"title": {
			"cn": "新增角色",
			"en": "Add Roles"
		},
		"nameEmpty": {
			"cn": "角色名称不能为空！",
			"en": "Role Name cannot be empty!"
		},
		"identityEmpty": {
			"cn": "唯一标识不能为空！",
			"en": "Unique ID can't be empty!"
		},
		"newSuccess": {
			"cn": "新增角色成功",
			"en": "New roles successfully"
		},
		"modifyRoleSuccess": {
			"cn": "角色修改成功",
			"en": "Modify roles successfully"
		},
		"name": {
			"cn": "角色名称",
			"en": "Character Name"
		},
		"identifier": {
			"cn": "角色标识",
			"en": "Role ID"
		},
		"inputTip": {
			"cn": "请输入功能名称",
			"en": "Please enter the function name"
		},
		"inputTip2": {
			"cn": "请输入唯一标识",
			"en": "Please enter a unique ID"
		},
		"modifySuccess": {
			"cn": "修改机构成功",
			"en": "Modify agency success"
		},
		"title2": {
			"cn": "修改角色信息",
			"en": "Modify roles information"
		}
	};
	return params[field][lang];
};

export default {
	common,
	tipInfo,
	modal
};
