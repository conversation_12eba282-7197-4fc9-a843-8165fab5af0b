import store, { getLanguage } from "../../app";

const common = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"title": {
			"cn": "机构管理",
			"en": "Organization Management"
		},
		"title2": {
			"cn": "机构列表",
			"en": "Organization List"
		},
		"btn": {
			// "cn": "添加一级机构",
			// "en": "First class organization"
			"cn": "添加",
			"en": "Add"
		}
	};
	return params[field][lang];
};

const tipInfo = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"removeSuccess": {
			"cn": "删除机构成功",
			"en": "Remove organization success"
		},
		"sureDelete": {
			"cn": "确定要删除当前机构吗？",
			"en": "Sure you want to delete the current organization?"
		},
		"noPower": {
			"cn": "没有权限",
			"en": "Have no legal power"
		},
		"checkConfig": {
			"cn": "查看配置",
			"en": "View organization config"
		},
		"name": {
			"cn": "机构名称",
			"en": "Organization name"
		},
		"identity": {
			"cn": "机构标识",
			"en": "Organization ID"
		},
		"creationTime": {
			"cn": "创建时间",
			"en": "Created time"
		},
		"modifyTime": {
			"cn": "修改时间",
			"en": "Modify time"
		}
	};
	return params[field][lang];
};

const addOrgModal = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let lang = getLanguage();
	let params = {
		"title": {
			"cn": "添加机构",
			"en": "Add the agency"
		},
		"nameEmpty": {
			"cn": "机构名称不能为空！",
			"en": "Name cannot be empty!"
		},
		"nameLen": {
			"cn": "限制在400字以内",
			"en": "Limited to 400 words"
		},
		"identityEmpty": {
			"cn": "唯一标识不能为空！",
			"en": "Unique ID can't be empty!"
		},
		"newSuccess": {
			"cn": "新增机构成功",
			"en": "New success"
		},
		"name": {
			"cn": "机构名称",
			"en": "Name of organization"
		},
		"identifier": {
			"cn": "唯一标识",
			"en": "A unique ID"
		},
		"inputTip": {
			"cn": "请输入机构",
			"en": "Please enter the organization"
		},
		"inputTip2": {
			"cn": "请输入唯一标识",
			"en": "Please enter a unique ID"
		},
		"modifySuccess": {
			"cn": "修改机构成功",
			"en": "Modify agency success"
		},
		"title2": {
			"cn": "修改机构信息",
			"en": "Modify agency information"
		}
	};
	return params[field][lang];
};

export default {
	common,
	tipInfo,
	addOrgModal
};
