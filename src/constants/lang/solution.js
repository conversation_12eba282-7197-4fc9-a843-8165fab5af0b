import store, { getLanguage} from "../../app";

// let globalStore = store.getState().global;
// let { personalMode } = globalStore;

const common = field => {
	let lang = getLanguage();
	let params = {
		solution: {
			cn: "解决方案",
			en: "Solution"
		},
		viewName: {
			cn: "显示名",
			en: "show name"
		},
		ingroup: {
			cn: "已在分组",
			en: "In Group"
		},
		watchSolution: {
			cn: "查看解决方案详情",
			en: "Solution Info"
		},
		noCustomMenu: {
			cn: "还未添加用户自定义菜单",
			en: "no user custom menu"
		},
		addGroup: {
			cn: "添加",
			en: "Add"
		},

		functionRegiste: {
			cn: "功能注册",
			en: "Function Register"
		},
		menuList: {
			cn: "菜单列表",
			en: "Menu List"
		},
		save: {
			cn: "保存",
			en: "Save"
		},
		addMenu: {
			cn: "添加菜单",
			en: "Add a menu"
		},
		groupList: {
			cn: "分组列表",
			en: "Group List"
		},

		addFunction: {
			cn: "添加功能",
			en: "Add a function"
		},
		addSysterm: {
			cn: "添加系统",
			en: "Add a system"
		},
		chineseName: {
			cn: "中文名称",
			en: "Chinese Name"
		},
		chineseNamePlaceholder: {
			cn: "请输入中文名称",
			en: "please enter a chinese name"
		},
		englishName: {
			cn: "英文名称",
			en: "English Name"
		},
		englishNamePlaceholder: {
			cn: "请输入英文名称",
			en: "please enter an english name"
		},
		uniqTag: {
			cn: "唯一标示",
			en: "Uniq tag"
		},
		uniqTagPlaceholder: {
			cn: "请输入唯一标示",
			en: "please enter an uniq tag"
		},
		modifyGroup: {
			cn: "修改分组",
			en: "Modify Group"
		},

		config: {
			cn: "配置",
			en: "config"
		},
		icon: {
			cn: "图标",
			en: "Icon"
		},
		iconPlaceholder: {
			cn: "请输入图标",
			en: "please enter an icon"
		},
		jumpPath: {
			cn: "跳转路径",
			en: "Link Path"
		},
		jumpPathPlaceholder: {
			cn: "请输入跳转路径",
			en: "please enter a link path "
		},
		logoName: {
			cn: "logo名称",
			en: "Logo Name"
		},
		logoNamePlaceholder: {
			cn: "请输入logo名称",
			en: "please enter the logo name"
		},
		route: {
			cn: "路由",
			en: "Route"
		},
		jumpMode: {
			cn: "跳转方式",
			en: "Page turn type"
		},
		modify: {
			cn: "修改",
			en: "Modify"
		},
		newWindow: {
			cn: "新页面打开",
			en: "Open a new window"
		},
		singleWindow: {
			cn: "原有窗口打开",
			en: "Stay on the current window"
		},

		routePlaceholder: {
			cn: "请输入路由",
			en: "please enter a route"
		},
		path: {
			cn: "路径",
			en: "Path"
		},
		pathPlaceholder: {
			cn: "请输入路径",
			en: "please enter a path"
		},

		requestMethodPlaceholder: {
			cn: "请输入路径",
			en: "please enter a path"
		},

		requestMethod: {
			cn: "请求方式",
			en: "Request Method"
		},
		success: {
			cn: "成功",
			en: "success"
		},
		delete: {
			cn: "删除",
			en: "delete"
		},

		deleteConfirm: {
			cn: "确定要删除当前功能吗？",
			en: "Are you sure to delete this?"
		},
		cancel: {
			cn: "取消",
			en: "cancel"
		},
		noMenuGroup: {
			cn: "当前分组暂无菜单",
			en: "This group has no menus"
		}
	};
	return params[field][lang];
};

const dialog = field => {
	let lang = getLanguage();
	let params = {
		blankName: {
			cn: "中文名称不能为空！",
			en: "please enter a chinese name"
		},
		errorChinese: {
			cn: "中文名称必须为中文！",
			en: "Chinese name must be Chinese "
		},
		errorEnglish: {
			cn: "英文名称必须为英文！",
			en: "English name must be English "
		},
		blankEnName: {
			cn: "英文名称不能为空！",
			en: "please enter an english name"
		},
		blankIcon: {
			cn: "图标不能为空！",
			en: "please enter an icon"
		},
		blankCode: {
			cn: "唯一标识不能为空！",
			en: "please enter an uniq tag!"
		},
		blankRoute: {
			cn: "路径不能为空！",
			en: "please enter a route!"
		},
		blankMethod: {
			cn: "功能方式不能为空！",
			en: "please enter a request method!"
		},
		errorName: {
			cn: "名称必须为中文或英文",
			en: "name must be chinese or english"
		},
		errorCode: {
			cn: "标识必须为英文或数字",
			en: "tag must be english or number"
		}
	};
	return params[field][lang];
};
export default {
	common,
	dialog
};
