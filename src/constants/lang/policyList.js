import store, { getLanguage } from "../../app";

// let globalStore = store.getState().global;
// let { personalMode } = globalStore;

const common = (field) => {
	let lang = getLanguage();
	let params = {
		"title": {
			"cn": "策略集列表",
			"en": "Policy set list"
		},
		"searchPlaceholder": {
			"cn": "请输入搜索内容",
			"en": "Please enter the search content"
		},
		"addNewPolicySet": {
			"cn": "新增策略集",
			"en": "Add new policy set"
		},
		"policySetName": {
			"cn": "策略集名称",
			"en": "Policy set name"
		},
		"event": {
			"cn": "接入事件",
			"en": "Access the event"
		}
	};
	return params[field][lang];
};
const table = (field) => {
	let lang = getLanguage();
	let params = {
		"policySet": {
			"cn": "策略集/策略",
			"en": "PolicySet/Policy"
		},
		"app": {
			"cn": "所属应用",
			"en": "Application"
		},
		"description": {
			"cn": "描述",
			"en": "Description"
		},
		"operation": {
			"cn": "操作",
			"en": "Action"
		}
	};
	return params[field][lang];
};

export default {
	common,
	table
};
