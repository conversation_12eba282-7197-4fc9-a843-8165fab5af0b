import store, { getLanguage } from "../../app";

const getRecords = (number) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	let cn = "共" + number + "条记录";
	let en = "A total of " + number + " records";
	return getLanguage() === "cn" ? cn : en;
};

const getDeleteInfo = (name) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;

	let cn = "确认删除\"" + name + "\"吗？";
	let en = "Confirm delete \"" + name + "\"?";
	return getLanguage() === "cn" ? cn : en;
};

const opera = (field) => {
	// let globalStore = store.getState().global;
	// let { personalMode } = globalStore;
	// personalMode.lang === "cn" ? "cn" : "en";
	let lang = getLanguage();
	let params = {
		"delete": {
			"cn": "删除",
			"en": "Delete"
		},
		"edit": {
			"cn": "修改",
			"en": "Modify"
		},
		"add": {
			"cn": "添加",
			"en": "Add"
		},
		"view": {
			"cn": "查看",
			"en": "View"
		}
	};
	return params[field][lang];

};

export default {
	getRecords,
	getDeleteInfo,
	opera
};
