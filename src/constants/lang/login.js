import store, { getLanguage } from "../../app";

const common = (field, langType) => {
	let lang = getLanguage();
	let params = {
		"title": {
			"cn": "智能网络 ",
			"en": "Intelligent Network "
		},
		"title1": {
			"cn": " 诚信互联",
			"en": " Integrity Connected"
		},
		"changePwd": {
			"cn": "修改密码",
			"en": "Change Password"
		},
		"unifiedPlatform": {
			"cn": "统一登录平台",
			"en": "Unified Login Platform"
		},
		"enterName": {
			"cn": "请输入账户名",
			"en": "Enter account name"
		},
		"enterPwd": {
			"cn": "请输入密码",
			"en": "Enter password"
		},
		"logIn": {
			"cn": "登录",
			"en": "Log In"
		},
		"newPwd": {
			"cn": "请输入新密码",
			"en": "Enter a new password"
		},
		"sureNewPwd": {
			"cn": "请确认新密码",
			"en": "Make sure the new password"
		},
		"modify": {
			"cn": "修改",
			"en": "Modify"
		},
		"enterLicense": {
			"cn": "请输入license",
			"en": "Enter the license"
		},
		"enterKey": {
			"cn": "请输入公钥",
			"en": "Enter public key"
		},
		"licenseActive": {
			"cn": "license激活",
			"en": "License activation"
		},
		"goBack": {
			"cn": "返回",
			"en": "Go Back"
		},
		"authCodePlaceholder": {
			"cn": "请输入验证码",
			"en": "Please enter the validation code"
		}
	};
	return params[field][lang];
};

const tipInfo = (field, langType) => {
	let lang = langType === "en" ? "en" : "cn";
	let params = {
		"tip1": {
			"cn": "请修改初始密码",
			"en": "Please modify the initial password"
		},
		"tip2": {
			"cn": "账户或密码错误",
			"en": "Account or password error"
		},
		"tip3": {
			"cn": "激活成功",
			"en": "Activate the success"
		},
		"tip4": {
			"cn": "密码输入不能为空",
			"en": "Password cannot be empty"
		},
		"tip5": {
			"cn": "两次输入的密码不一致",
			"en": "Two input password is not consistent"
		},
		"tip6": {
			"cn": "修改密码成功",
			"en": "Change the password successfully"
		},
		"tip7": {
			"cn": "必须包含大小写字母、数字、特殊字符，长度8~18位",
			"en": "Must contain upper and lower case letters, numbers, special characters, 8-18 digits in length"
		},
		"tip8": {
			"cn": "请输入验证码",
			"en": "Error validation code"
		},
		"tip9": {
			"cn": "密码必须包含大小写字母、数字、特殊字符，长度8~18位",
			"en": "The password must contain uppercase and lowercase letters, numbers, special characters, 8-18 digits in length"
		},
		"tip10": {
			"cn": "确认密码必须包含大小写字母、数字、特殊字符，长度8~18位",
			"en": "The confirmation password must contain uppercase and lowercase letters, numbers and special characters, with a length of 8-18 digits"
		},
		"pwdExpired": {
			"cn": "密码已过期，请修改密码",
			"en": "The password has expired. Please change the password"
		}
	};
	return params[field][lang];
};
export default {
	common,
	tipInfo
};
