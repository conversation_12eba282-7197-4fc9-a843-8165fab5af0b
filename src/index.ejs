<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>统一登录平台</title>
    <link rel="shortcut icon" href="/bifrost-resource/favicon/favicon.png" type="image/x-icon">
    <script src="/bifrost-resource/polyfill/polyfill.min.js"></script>
    <link rel="stylesheet" type="text/css" href="/bifrost-resource/fonts/systemFonts/iconfont.css">
    <link rel="stylesheet" type="text/css" href="/bifrost-resource/fonts/bifrostFonts/iconfont.css">
    <script>
        (function () {
            // 该事件是核心
            window.addEventListener("storage", function (event) {
                if (event.key == "getSessionStorage") {
                    // 已存在的标签页会收到这个事件
                    localStorage.setItem("_up_qjt_csrf_", JSON.stringify(sessionStorage));
                    localStorage.removeItem("_up_qjt_csrf_");
                } else if (event.key == "_up_qjt_csrf_" && !sessionStorage.length) {
                    // 新开启的标签页会收到这个事件
                    var data, value;
                    if(event.newValue){
                        data = JSON.parse(event.newValue)
                    }
                    for (key in data) {
                        sessionStorage.setItem(key, data[key]);
                    }
                } else if(event.key === "_sync_qjt_csrf_"){ // 重置密码或登出重新登录需要同步其他页面的session
                    sessionStorage.setItem("_csrf_", event.newValue);
                }
            });

            if (!sessionStorage.length) {
                // 这个调用能触发目标事件，从而达到共享数据的目的
                localStorage.setItem("getSessionStorage", Date.now());
            }
        })();
    </script>
</head>

<body>
    <div id="root"></div>
    <script>
		(function(){
			Object.setPrototypeOf = Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? setProtoOf : mixinProperties);
			function setProtoOf(obj, proto) {
				obj.__proto__ = proto;
				return obj;
			}
			function mixinProperties(obj, proto) {
				for (var prop in proto) {
				if (!obj.hasOwnProperty(prop)) {
					obj[prop] = proto[prop];
				}
				}
				return obj;
			}
		})()
	</script>
    <script src="/bifrost-resource/vendor/vendor_dll.js"></script>
</body>

</html>
