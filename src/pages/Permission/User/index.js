import { useEffect, useState } from "react";
import { connect } from "dva";
import { <PERSON>ton, Popconfirm, Tag, message, Switch, TreeSelect, Tooltip, Popover } from "antd";
import { QueryListScene, Handle } from "tntd";
import moment from "moment";
import { userAPI } from "@/services";
import UserModal from "./Modal/UserModal";
import { colorArray } from "@/constants/common";
import { mainImages } from "@/constants/images";
import { userLang } from "@/constants/lang";
import "./User.less";

const { QueryList, QueryForm, Field, createActions } = QueryListScene;
const actions = createActions();

export default connect(state => ({
	globalStore: state.global,
	userStore: state.user,
	permissionStore: state.permission
}))((props)=>{
	const { dispatch, permissionStore, userStore } = props;
	const { orgList } = permissionStore;
	const { dialogShow } = userStore;

	const [objK<PERSON>, setObjKey] = useState("account");

	const formatTreeData = (orgList, index) => {
		if (orgList) {
			return orgList.reduce((total, org, ind)=>{
				let key = String(ind);
				if (index || index === 0) {
					key = `${index}-${ind}`;
				}
				return [
					...total,
					{
						title: org.name,
						value: org.uuid,
						key,
						children: formatTreeData(org.children, ind)
					}
				];
			}, []);
		}
		return [];
	};

	useEffect(()=>{
		// 获取机构列表
	    dispatch({
    		type: "permission/getOrgList"
    	});

    	// 获取系统字典：轻量化数据分析平台角色
    	dispatch({
    		type: "global/getDictionaryType",
    		payload: "SystemInitRole"
    	});
	}, []);

	// 查询
	const query = (params = {}) => {
		const { current, obj, ...rest } = params;
		return userAPI.getUserList({
			...rest,
			...obj,
			curPage: current || 1,
			pageSize: params.pageSize || 10
		})
			.then((data) => {
				data = data?.data;
				return {
					pageSize: Number(data?.pageSize),
					current: Number(data?.curPage),
					total: Number(data?.total),
					data: data?.userList || []
				};
			});
	};

	// 修改查询表单
	const onFormChange = (values, changeInfo) => {
		if (["orgUuid"].includes(changeInfo.name)) {
			actions.search({
				...values,
				current: 1
			});
		}
		if (["obj"].includes(changeInfo.name)) {
			setObjKey(Object.keys(values.obj)[0]);
		}
	};

	const addUserHandle = () => {
    	dispatch({
    		type: "user/setDialogData",
    		payload: {
    			operationType: "add",
    			userData: {
    				status: 0,
    				gender: 0
    			}
    		}
    	});
    	// 清空机构
    	dispatch({
    		type: "user/setDialogData",
    		payload: {
    			isThisOrgList: true,
    			selectOrgList: []
    		}
    	});
    	dispatch({
    		type: "user/setDialogShow",
    		payload: {
    			operateUser: true
    		}
    	});
	};

	const modifyUserHandle = (item) => {
    	// 清空机构
    	dispatch({
    		type: "user/setDialogData",
    		payload: {
    			isThisOrgList: true,
    			selectOrgList: []
    		}
    	});
    	dispatch({
    		type: "user/setDialogData",
    		payload: {
    			operationType: "modify",
    			userData: {
    				account: item.account,
    				userName: item.userName,
    				appName: item.appName,
    				orgUuid: item.orgUuid,
    				uuid: item.uuid,
    				roleUuids: item.roleUuids,
    				expiration: item.expiration,
    				gender: item.gender,
    				status: item.status,
    				email: item.email,
    				phone: item.phone,
    				otherRoles: item.otherRoles,
    				avatar: item.avatar
    			}
    		}
    	});
    	dispatch({
    		type: "user/setDialogShow",
    		payload: {
    			operateUser: true
    		}
    	});
	};

	const deleteUserHandle = (uuid) => {
    	userAPI.deleteUser(uuid).then((res) => {
    		if (res.success) {
    			// dispatch({
    			// 	type: "user/getUserList",
    			// 	payload: searchParams
    			// });
				actions.search({
					current: 1
				});
    			message.success(userLang.tipInfo("deleteUser")); // 删除用户成功
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
	};

	const resetUserPassword = (uuid) => {
    	const params = {
    		uuid: uuid
    	};
    	userAPI.resetUserPassword(params).then((res) => {
    		if (res.success) {
    			// 修改密码成功后，原csrfToken失效，需要重新获取csrfToken
    			const { csrfToken } = res.data || {};
    			sessionStorage.setItem("_csrf_", csrfToken);
    			localStorage.setItem("_sync_qjt_csrf_", csrfToken); // 同步到其他页面

    			// dispatch({
    			// 	type: "user/getUserList",
    			// 	payload: searchParams
    			// });
				actions.search();
    			message.success(userLang.tipInfo("resetPwd")); // 重置密码成功
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
	};

	const changeUserStatus = (item, value) => {
		console.log("value", value);
    	const params = {
    		uuid: item.uuid,
    		status: value ? 0 : 1 // item.status
    	};
    	// params["status"] = value ? 0 : 1;
    	userAPI.changeUserStatus(params).then((res) => {
    		if (res.success) {
    			// dispatch({
    			// 	type: "user/getUserList",
    			// 	payload: searchParams
    			// });
				actions.search();
    			message.success(userLang.tipInfo("updateSuccess")); // 修改用户状态成功
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
	};

	const columns = [
		{
			title: userLang.table("name"), // 用户名/登录名
			dataIndex: "",
			render: (record) => {
				return (
					<div className="user-item-meta">
						<div className="user-item-meta-avatar">
							{
								record.avatar && record.avatar.length > 10 &&
                                <img src={record.avatar || mainImages.defaultAvatar} />
							}
							{
								record.avatar && record.avatar.length < 10 &&
                                <img src={`/bifrost-resource/avatar/${record["avatar"]}.png`} />
							}
							{
								!record.avatar && <img src="/bifrost-resource/avatar/male1.png" />
							}
						</div>
						<div className="user-item-meta-info">
							<h3>{record.userName || "-"}</h3>
							<p>
								{/* 登录名： */}
								{userLang.table("loginId")}：
								{record.account || "-"}
							</p>
						</div>
					</div>
				);
			}
		},
		{
			title: userLang.table("agency"), // 所在机构/角色
			dataIndex: "",
			width: 250,
			render: (record) => {
				return (
					<div className="user-item-line">
						<div>{record["orgName"] || "-"}</div>
						<div style={{ marginTop: "4px" }}>
							{
								record.roles &&
                                record.roles.map((item, index) => {
                                	return <Tag color={colorArray[index]}
                                		key={index}>{item.name}</Tag>;
                                })
							}
						</div>
					</div>
				);
			}
		},
		{
			title: userLang.table("permission"), // 应用权限
			dataIndex: "apps",
			width: 250,
			render: (record) => {
				const content = record && record.map((item, index, arr) => {
					return (
						<span key={index}>
							{item.displayName}{index < arr.length - 1 ? " , " : ""}
						</span>
					);
				});
				return (
					<div className="user-item-line">
						{
							record &&
                            <Popover
                            	title={`${userLang.table("permission")} (${record.length})`}
                            	content={
                            		<div style={{ width: "200px" }}>{content}</div>
                            	}
                            >
                            	{content}
                            </Popover>
						}
					</div>
				);
			}
		},
		{
			title: userLang.table("expirationTime"), // 过期时间
			dataIndex: "expiration",
			width: 170,
			render: (record) => {
				let day = record ? moment(record).startOf("hours").fromNow() : "";
				if (day) {
					day = day.replace("内", "后");
				}
				return (
					<Tooltip title={moment(record).format("YYYY-MM-DD HH:mm:ss")}>
						{ day }
					</Tooltip>
				);
			}
		},
		{
			title: userLang.table("lastLoginTime"), // 最后登录时间
			dataIndex: "loginTime",
			width: 190
		},
		{
			title: userLang.table("lastLoginIp"), // 最后登录IP
			dataIndex: "ipAddr",
			width: 180
		},
		{
			title: userLang.table("status"), // 状态
			dataIndex: "status",
			width: 100,
			render: (record, records) => {
				return (
					<Switch
						disabled={!window.auth("QX0103", "userLockUnlock")}
						checkedChildren={userLang.table("normal")} // 启用
						unCheckedChildren={userLang.table("disable")} // 禁用
						checked={record === 0}
						onChange={(e)=>changeUserStatus(records, e)}
					/>
				);
			}
		},
		{
			title: userLang.table("operation"), // 操作
			dataIndex: "",
			width: 200,
			fixed: "right",
			render: (record) => {
				return (
					<Handle>
						{
							window.auth("QX0103", "resetPassword")
								? (
									<Popconfirm
										title={userLang.tipInfo("sureResetPwd")} // 确定要重置当前用户密码吗？
										onConfirm={()=>resetUserPassword(record.uuid)}
										onCancel={() => {
										}}>
										<a>
											{/* 重置密码 */}
											{userLang.table("resetPwd")}
										</a>
									</Popconfirm>
								)
								: (
									<a className="a-disabled">
										{/* 重置密码 */}
										{userLang.table("resetPwd")}
									</a>
								)
						}
						<a onClick={!window.auth("QX0103", "modifyUser") ? null : ()=>modifyUserHandle(record)}
							className={!window.auth("QX0103", "modifyUser") ? "a-disabled" : null}
						>
							{/* 编辑 */}
							{userLang.table("edit")}
						</a>
						{
							window.auth("QX0103", "deleteUser")
								? (
									<Popconfirm
										title={userLang.tipInfo("sureDeleteUser")} // 确定要删除当前用户吗？
										onConfirm={()=>deleteUserHandle(record.uuid)}
										onCancel={() => {
										}}>
										<a>
											{/* 删除 */}
											{userLang.table("delete")}
										</a>
									</Popconfirm>
								)
								: (
									<a className={"a-disabled"}>
										{/* 删除 */}
										{userLang.table("delete")}
									</a>
								)
						}
					</Handle>
				);
			}
		}
	];

	return (
		<QueryListScene title={userLang.common("title")} query={query} actions={actions}>
			<QueryForm
				onChange={onFormChange}
				extralActions={
					window.auth("QX0103", "addUser") &&
                    <Button
                    	type="primary"
                    	onClick={addUserHandle}
                    >
                    	{/* 添加用户 */}
                    	{userLang.searchParams("addUser")}
                    </Button>
				}
				initialValues={{
					obj: {
						account: ""
					}
				}}
			>
				<Field
					name="orgUuid"
					component={TreeSelect}
					props={{
						placeholder: userLang.searchParams("selectIn"),
						allowClear: true,
						treeData: formatTreeData(orgList),
						dropdownStyle: {
							maxHeight: 400,
							overflow: "auto"
						}
					}}
				/>
				<Field
					type="selectInput"
					name="obj"
					props={{
						placeholder: objKey === "account" ? userLang.searchParams("enterName") : userLang.searchParams("enterUsername"), // "输入登录名搜索" : "输入用户名搜索"
						options: [
							{ value: "account", label: userLang.searchParams("loginId") },
							{ value: "userName", label: userLang.searchParams("username") }
						],
						onPressEnter: () => {
							actions.search({
								...actions.getFormData().obj,
								current: 1
							});
						}
					}}
				/>
			</QueryForm>
		    <QueryList
				className="user-list-management"
				columns={columns}
				rowKey="uuid"
				scroll={{
					x: 1600
				}}
			/>
			{
				dialogShow.operateUser &&
                <UserModal
                	refresh={actions.search}
                />
			}
		</QueryListScene>
	);
});
