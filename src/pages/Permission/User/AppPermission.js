import { Fragment, PureComponent } from "react";
import { Row, Icon } from "antd";
import "./AppPermission.less";

export default class AppPermission extends PureComponent {
	constructor(props) {
		super(props);
		this.state = {
			show: false
		};
	}
    changeShow = () => {
    	const { show } = this.state;
    	this.setState({
    		show: !show
    	});
    }
    render() {
    	const { show } = this.state;
    	const { record } = this.props;
    	const recordName = record && record.map((item) => (item.displayName));
    	const longTxt = recordName && recordName.join("，").length > 15;
    	return (
    		<Fragment>
    			{
    				recordName &&
                    recordName.length > 0 &&
                    <Row
                    	type="flex"
                    	align="top"
                    	justify="start"
                    	className="user-app-permission-wrap"
                    >
                    	<div className={`user-app-permission-content ${show ? "" : "one-line-show"}`}>
                    		{recordName.join("，")}
                    	</div>
                    	{
                    		longTxt ? (
                    			show
                    				? <Icon type="up" onClick={this.changeShow.bind(this)} /> : <Icon type="down" onClick={this.changeShow.bind(this)} />
                    		) : <i className="empty-icon" />
                    	}
                    	<span className="user-permission-num">({recordName.length})</span>
                    </Row>
    			}
    		</Fragment>
    	);
    }
}
