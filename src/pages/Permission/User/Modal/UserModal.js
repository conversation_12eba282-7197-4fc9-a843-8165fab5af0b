import { PureComponent } from "react";
import { connect } from "dva";
import moment from "moment";
import { Input, Form, message, Modal, Select, DatePicker, Radio, TreeSelect } from "antd";
import { userAPI, roleAPI, appAPI } from "@/services";
import string from "@/utils/string";
import { userLang } from "@/constants/lang";

const FormItem = Form.Item;
const Option = Select.Option;
const RadioGroup = Radio.Group;
const TreeNode = TreeSelect.TreeNode;

class UserModal extends PureComponent {
	constructor(props) {
		super(props);
		this.state = {
			selectOrgList: [],
			isThisOrgList: false,
			genderType: null
		};
		this.changeUserDataHandle = this.changeUserDataHandle.bind(this);
		this.submitUserHandle = this.submitUserHandle.bind(this);
		this.getOrgTree = this.getOrgTree.bind(this);
		this.getRoleByOrg = this.getRoleByOrg.bind(this);
		this.getAppList = this.getAppList.bind(this);
	}

	componentDidMount() {
		let { userStore, permissionStore } = this.props;
		let { dialogData } = userStore;
		let { orgList } = permissionStore;
		let { userData, operationType } = dialogData;
		if (operationType === "modify" && userData.orgUuid) {
			this.getOrgTree(userData.orgUuid, orgList);
			this.getRoleByOrg(userData.orgUuid);
			this.setState({
				genderType: userData.gender
			});
			console.log("change gender", userData.gender);
		}
		this.getAppList();
	}

	getOrgTree(orgUuid, orgList) {
		let { dispatch, userStore } = this.props;
		let { isThisOrgList } = userStore;
		orgList.map(item => {
			if (!isThisOrgList) {
				if (item.uuid === orgUuid) {
					dispatch({
						type: "user/setDialogData",
						payload: {
							isThisOrgList: true,
							selectOrgList: [item.uuid]
						}
					});
				} else if (item.children && item.children.length) {
					item.children.map(item2 => {
						if (item2.uuid === orgUuid) {
							dispatch({
								type: "user/setDialogData",
								payload: {
									isThisOrgList: true,
									selectOrgList: [item.uuid, item2.uuid]
								}
							});
						} else if (item2.children && item2.children.length) {
							item2.children.map(item3 => {
								if (item3.uuid === orgUuid) {
									dispatch({
										type: "user/setDialogData",
										payload: {
											isThisOrgList: true,
											selectOrgList: [item.uuid, item2.uuid, item3.uuid]
										}
									});
								}
							});
						}
					});
				}
			}
		});
	};

	changeUserDataHandle(field, type, e) {
		let { dispatch, userStore, permissionStore } = this.props;
		let { userData } = userStore.dialogData;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		} else if (type === "date") {
			value = Date.parse(e);
		} else if (type === "treeSelect") {
			let { orgList } = permissionStore;
			// value = e[e.length - 1];
			value = e;
			// 重新选择机构的时候清除掉上次选择的角色，一个用户只能有一个机构下的角色。
			userData.roleUuids = "";
			if (value) {
				this.getOrgTree(value, orgList);
				this.getRoleByOrg(value);
			} else {
				// 清空机构
				dispatch({
					type: "user/setDialogData",
					payload: {
						isThisOrgList: true,
						selectOrgList: []
					}
				});
			}
		} else if (type === "arrStr") {
			value = JSON.stringify(e);
		}
		userData[field] = value;
		dispatch({
			type: "user/setDialogData",
			payload: {
				userData: userData
			}
		});
	}

	getAppList() {
		let { dispatch } = this.props;
		let params = {
			curPage: 1,
			pageSize: 1500
		};
		appAPI.getAppList(params).then((res) => {
			if (res.success) {
				console.log(res);
				let contents = res.data && res.data.contents ? res.data.contents : [];
				dispatch({
					type: "user/setDialogData",
					payload: {
						appList: contents || []
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	getRoleByOrg(orgUuid) {
		let { dispatch } = this.props;
		let params = {
			orgUuid: orgUuid
		};
		roleAPI.getRoleByOrg(params).then((res) => {
			if (res.success) {
				dispatch({
					type: "user/setDialogData",
					payload: {
						roleList: res.data || []
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	submitUserHandle() {
		let { userStore, dispatch, refresh } = this.props;
		let { dialogData } = userStore;
		let { userData, operationType } = dialogData;

		// let { searchParams } = userStore.userListData;

		const { genderType } = this.state;
		// 提交时判断为空
		if (string.isBlank(userData.account)) {
			message.error(userLang.modal("nameEmpty")); // 登录账号不能为空！
			return;
		}
		if (string.isBlank(userData.userName)) {
			message.error(userLang.modal("userNameEmpty")); // 用户名不能为空！
			return;
		}
		if (string.isBlank(userData.orgUuid)) {
			message.error(userLang.modal("orgEmpty")); // 机构不能为空！
			return;
		}
		if (string.isBlank(userData.roleUuids)) {
			message.error(userLang.modal("roleEmpty")); // 角色不能为空！
			return;
		}
		// 编辑的时候是个空数组字符串
		if (typeof userData.roleUuids === "string") {
			try {
				const roleUuids = JSON.parse(userData.roleUuids);
				if (!(roleUuids && roleUuids.length)) {
					message.error(userLang.modal("roleEmpty")); // 角色不能为空！
					return;
				}
			} catch (e) {}
		}
		if (!userData.expiration) {
			message.error(userLang.modal("expirationEmpty")); // 过期时间不能为空
			return;
		}

		console.log("gender", userData.gender);
		// 当不存在头像 或者 性别发生明显变更例如男性改为女性或者女性改为男性修改头像，秘密的场景保持原先用于默认设置
		if (!userData["avatar"] || (genderType !== userData.gender && userData.gender)) {
			if (userData.gender === 2) {
				userData["avatar"] = "female1";
			} else {
				userData["avatar"] = "male1";
			}
		}

		// 校验手机号
		if (userData.phone && userData.phone.length > 0 && !(/^1[3456789]\d{9}$/.test(userData.phone))) {
			// lang:手机号码填写有误
			message.error(userLang.tipInfo("wrongPhoneTip"));
			return;
		}
		// 校验邮箱
		if (userData.email && userData.email.length > 0 && !(/^([0-9A-Za-z\-_\.]+)@([0-9a-z]+\.[a-z]{2,3}(\.[a-z]{2})?)$/g.test(userData.email))) {
			// lang:邮箱填写有误
			message.error(userLang.tipInfo("wrongEmailTip"));
			return;
		}

		// 分情况提交，判断是新增还是修改
		if (operationType === "add") {
			userAPI.addUser(userData).then((res) => {
				if (res.success) {
					// dispatch({
					// 	type: "user/getUserList",
					// 	payload: searchParams
					// });
					refresh && refresh({current: 1});
					message.success(userLang.modal("newSuccess")); // 新增用户成功
					dispatch({
						type: "user/setDialogShow",
						payload: {
							operateUser: false
						}
					});
				} else {
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		} else if (operationType === "modify") {
			if (!userData.orgUuid && userData.roleUuids) {
				message.error(userLang.modal("selectIn")); // 请选择机构
				return;
			}
			// 删除机构需要传入一个空值
			if (!userData.orgUuid) {
				userData.orgUuid = "";
			}
			if (!userData.roleUuids) {
				userData.roleUuids = "";
			}
			userAPI.modifyUser(userData).then((res) => {
				if (res.success) {
					// dispatch({
					// 	type: "user/getUserList",
					// 	payload: searchParams
					// });
					refresh && refresh();
					message.success(userLang.modal("modifySuccess")); // 修改用户成功
					dispatch({
						type: "user/setDialogShow",
						payload: {
							operateUser: false
						}
					});
				} else {
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		}
		// 清空机构
		dispatch({
			type: "user/setDialogData",
			payload: {
				isThisOrgList: true,
				selectOrgList: []
			}
		});
	}

    disabledDate = (current) => {
    	// Can not select days before today and today
    	return current && current < moment();
    };

    render() {
    	let { userStore, permissionStore, dispatch, globalStore } = this.props;
    	let { dialogShow, dialogData } = userStore;
    	let { orgList } = permissionStore;
    	let { userData, operationType, selectOrgList, roleList, appList } = dialogData;
    	const { personalMode, systemDictionaryMap } = globalStore;
    	const { lang } = personalMode;
    	let formItemLayout = {
    		labelCol: { span: lang === "cn" ? 6 : 8 },
    		wrapperCol: { span: lang === "cn" ? 18 : 16 }
    	};

    	let children = [];
    	roleList.map((item, index) => {
    		children.push(
    			<Option key={index} value={item.uuid}>{item.name}</Option>
    		);
    	});

    	let appOptions = [];
    	appList.map((item, index) => {
    		appOptions.push(
    			<Option key={index} value={item.name}>{item.displayName}</Option>
    		);
    	});

    	let disabled = operationType === "modify";

    	const { SystemInitRole = [] } = systemDictionaryMap || {};
    	console.log(SystemInitRole);
    	return (
    		<Modal
    			title={operationType === "add" ? userLang.modal("addUser") : userLang.modal("modifyUser")} // "添加用户" : "修改用户"
    			visible={dialogShow.operateUser}
    			maskClosable={false}
    			onOk={this.submitUserHandle.bind(this)}
    			onCancel={() => {
    				dispatch({
    					type: "user/setDialogShow",
    					payload: {
    						operateUser: false
    					}
    				});
    				dispatch({
    					type: "user/setDialogData",
    					payload: {
    						roleList: []
    					}
    				});
    			}}
    		>
    			<Form>
    				{/* 登录账号 */}
    				<FormItem label={userLang.modal("loginId")} {...formItemLayout}>
    					<span className="require">*</span>
    					<Input
    						type="text"
    						value={userData.account || undefined}
    						placeholder={userLang.modal("inputLoginId")} // 请输入登录账号
    						onChange={this.changeUserDataHandle.bind(this, "account", "input")}
    						disabled={disabled}
    					/>
    				</FormItem>
    				{/* 用户名 */}
    				<FormItem label={userLang.modal("userName")} {...formItemLayout}>
    					<span className="require">*</span>
    					<Input
    						type="text"
    						value={userData.userName || undefined}
    						placeholder={userLang.modal("inputUserName")} // 请输入用户昵称
    						onChange={this.changeUserDataHandle.bind(this, "userName", "input")}
    					/>
    				</FormItem>
    				{/* 选择应用 */}
    				<FormItem label={userLang.modal("chooseApp")} {...formItemLayout}>
    					<Select
    						options={appList}
    						mode="multiple"
    						style={{ width: "100%" }}
    						placeholder={userLang.modal("inputApp")} // 请选择应用，多选
    						value={userData.appName ? JSON.parse(userData.appName) : []}
    						onChange={this.changeUserDataHandle.bind(this, "appName", "arrStr")}
    						filterOption={(input, option) => {
    							return (
    								option.props.children
    									.toLowerCase()
    									.indexOf(input.toLowerCase()) >= 0
    							);
    						}}
    					>
    						{appOptions}
    					</Select>
    				</FormItem>
    				{/* 选择机构 */}
    				<FormItem label={userLang.modal("selectInstri")} {...formItemLayout}>
    					<span className="require">*</span>
    					<TreeSelect
    						className="register-header-select"
    						value={selectOrgList[selectOrgList.length - 1] || []}
    						placeholder={userLang.modal("inputInstri")} // 请选择机构
    						treeDefaultExpandAll
    						onChange={this.changeUserDataHandle.bind(this, "orgUuid", "treeSelect")}
    					>
    						{
    							orgList.map((item, index) =>
    								<TreeNode key={index} title={item.name} value={item.uuid}>
    									{
    										item.children && item.children.map((child, i) =>
    											<TreeNode title={child.name} key={index + "-" + i} value={child.uuid}>
    												{
    													child.children && child.children.map((secChild, j) =>
    														<TreeNode title={secChild.name}
    															key={index + "-" + i + "-" + j}
    															value={secChild.uuid}></TreeNode>
    													)
    												}
    											</TreeNode>)
    									}
    								</TreeNode>)
    						}
    					</TreeSelect>
    				</FormItem>
    				{/* 选择角色 */}
    				<FormItem label={userLang.modal("selectRole")} {...formItemLayout}>
    					<span className="require">*</span>
    					<Select
    						mode="multiple"
    						style={{ width: "100%" }}
    						placeholder={userLang.modal("inputSelectRole")} // 请选择角色，多选
    						value={userData.roleUuids ? JSON.parse(userData.roleUuids) : []}
    						onChange={this.changeUserDataHandle.bind(this, "roleUuids", "arrStr")}
    					>
    						{children}
    					</Select>
    				</FormItem>
    				{/* 过期时间 */}
    				<FormItem label={userLang.modal("expirationTime")} {...formItemLayout}>
    					<span className="require">*</span>
    					<DatePicker
    						style={{ width: "100%" }}
    						format="YYYY-MM-DD"
    						value={userData.expiration ? moment(userData.expiration) : undefined}
    						// showTime={{defaultValue: moment("00:00:00", "HH:mm:ss")}}
    						onChange={this.changeUserDataHandle.bind(this, "expiration", "date")}
    						placeholder={userLang.modal("inputExpirationTime")} // 请选择过期时间
    						// 不可选的日期
    						disabledDate={this.disabledDate}
    					/>
    				</FormItem>
    				{/* 性别 */}
    				<FormItem label={userLang.modal("sex")} {...formItemLayout}>
    					<RadioGroup onChange={this.changeUserDataHandle.bind(this, "gender", "input")}
    						value={userData.gender || 0}>
    						<Radio value={0}>
    							{/* 保密 */}
    							{userLang.modal("secret")}
    						</Radio>
    						<Radio value={1}>
    							{/* 男 */}
    							{userLang.modal("male")}
    						</Radio>
    						<Radio value={2}>
    							{/* 女 */}
    							{userLang.modal("female")}
    						</Radio>
    					</RadioGroup>
    				</FormItem>
    				{/* 手机号 */}
    				<FormItem label={userLang.modal("phone")} {...formItemLayout}>
    					<Input
    						type="text"
    						value={userData.phone || undefined}
    						placeholder={userLang.modal("phonePlaceHolder")} // 请输入用户昵称
    						onChange={this.changeUserDataHandle.bind(this, "phone", "input")}
    					/>
    				</FormItem>
    				{/* 邮箱 */}
    				<FormItem label={userLang.modal("email")} {...formItemLayout}>
    					<Input
    						type="text"
    						value={userData.email || undefined}
    						placeholder={userLang.modal("emailPlaceHolder")} // 请输入邮箱
    						onChange={this.changeUserDataHandle.bind(this, "email", "input")}
    					/>
    				</FormItem>
    				{/* 数据分析平台角色 */}
    				{
    					SystemInitRole &&
                        SystemInitRole.length > 0 &&
                        <FormItem label={userLang.modal("dataPlatForm")} {...formItemLayout}>
                        	<Select
                        		mode="multiple"
                        		placeholder={userLang.modal("dataPlatFormHolder")} // 请选择数据分析平台角色
                        		value={userData.otherRoles ? JSON.parse(userData.otherRoles) : []}
    						    onChange={this.changeUserDataHandle.bind(this, "otherRoles", "arrStr")}
                        	>
                        		{
                        			SystemInitRole.map(role=>{
                        				return (
                        					<Option value={role.name}>{role.dName}</Option>
                        				);
                        			})
                        		}
                        	</Select>
                        </FormItem>
    				}
    				{/* <FormItem label="用户状态" {...formItemLayout}>*/}
    				{/* <Switch checkedChildren="正常" unCheckedChildren="禁用" defaultChecked/>*/}
    				{/* </FormItem>*/}
    			</Form>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	userStore: state.user,
	permissionStore: state.permission,
	globalStore: state.global
}))(UserModal);
