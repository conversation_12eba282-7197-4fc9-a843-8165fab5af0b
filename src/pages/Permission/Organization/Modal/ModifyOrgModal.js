import { PureComponent } from "react";
import { connect } from "dva";
import { Form, Input, message, Modal } from "antd";
import { orgAPI } from "@/services";
import string from "@/utils/string";
import { orgLang } from "@/constants/lang";

const FormItem = Form.Item;

class ModifyOrgModal extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.changeFiled = this.changeFiled.bind(this);
    	this.modifyOrgHandle = this.modifyOrgHandle.bind(this);
    }

    changeFiled(field, e) {
    	let { permissionStore, dispatch } = this.props;
    	let { modifyOrgData } = permissionStore.dialogData;
    	let value = e.target.value;
    	modifyOrgData[field] = value;
    	dispatch({
    		type: "permission/setDialogData",
    		payload: {
    			modifyOrgData: modifyOrgData
    		}
    	});
    }

    modifyOrgHandle() {
    	let { permissionStore, dispatch } = this.props;
    	let { dialogData } = permissionStore;
    	let { modifyOrgData } = dialogData;

    	if (string.isBlank(modifyOrgData.name)) {
    		message.error(orgLang.addOrgModal("nameEmpty")); // 机构名称不能为空！
    		return;
    	}

    	if (modifyOrgData.name && modifyOrgData.name.length > 400) {
    		message.error(orgLang.addOrgModal("nameLen")); // 限制400字
    		return;
    	}

    	if (string.isBlank(modifyOrgData.code)) {
    		message.error(orgLang.addOrgModal("identityEmpty")); // 唯一标识不能为空！
    		return;
    	}

    	let params = {
    		code: modifyOrgData.code,
    		name: modifyOrgData.name,
    		uuid: modifyOrgData.uuid
    	};

    	orgAPI.modifyOrg(params).then((res) => {
    		if (res.success) {
    			dispatch({
    				type: "permission/getOrgList",
    				payload: {}
    			});
    			message.success(orgLang.addOrgModal("modifySuccess")); // 修改机构成功
    			dispatch({
    				type: "permission/setDialogShow",
    				payload: {
    					modifyOrg: false
    				}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    render() {
    	const { permissionStore, dispatch, globalStore } = this.props;
    	const { dialogShow, dialogData } = permissionStore;
    	const { modifyOrgData } = dialogData;
    	const { personalMode } = globalStore;
    	const { lang } = personalMode;

    	const formItemLayout = {
    		labelCol: { span: lang === "cn" ? 6 : 8 },
    		wrapperCol: { span: lang === "cn" ? 16 : 14 }
    	};
    	return (
    		<div>
    			<Modal
    				// disabled={!checkFunctionHasPermission("QX0101", "modifyOrg")}
    				title={orgLang.addOrgModal("title2")} // 修改机构信息
    				wrapClassName="vertical-center-modal"
    				visible={dialogShow.modifyOrg}
    				maskClosable={false}
    				className="report-modal"
    				onOk={this.modifyOrgHandle.bind(this)}
    				onCancel={() => {
    					dispatch({
    						type: "permission/setDialogShow",
    						payload: {
    							modifyOrg: false
    						}
    					});
    					dispatch({
    						type: "permission/setDialogData",
    						payload: {
    							modifyOrgData: {
    								name: null,
    								code: null,
    								level: null,
    								parentUuid: null
    							}
    						}
    					});
    				}}
    			>
    				<Form layout="horizontal" disabled={true}>
    					<FormItem
    						label={orgLang.addOrgModal("name")} // 机构名称
    						{...formItemLayout}
    					>
    						<Input name="workflowName" value={modifyOrgData.name || undefined}
    							onChange={this.changeFiled.bind(this, "name")}
    							placeholder={orgLang.addOrgModal("inputTip")} />
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={orgLang.addOrgModal("identifier")} // 唯一标识
    						{...formItemLayout}
    					>
    						<Input name="workflowCode" value={modifyOrgData.code || undefined}
    							onChange={this.changeFiled.bind(this, "code")}
    							placeholder={orgLang.addOrgModal("inputTip2")} />
    						<span className="require">*</span>
    					</FormItem>
    				</Form>
    			</Modal>
    		</div>
    	);
    }
};

export default connect(state => ({
	globalStore: state.global,
	permissionStore: state.permission
}))(ModifyOrgModal);
