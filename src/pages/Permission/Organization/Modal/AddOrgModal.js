import { PureComponent } from "react";
import { connect } from "dva";
import { Form, Input, message, Modal } from "antd";
import { orgAPI } from "@/services";
import { checkFunctionHasPermission } from "@/utils/permission";
import { orgLang } from "@/constants/lang";

const FormItem = Form.Item;

class AddOrgModal extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.changeFiled = this.changeFiled.bind(this);
    	this.addOrgHandle = this.addOrgHandle.bind(this);
    }

    changeFiled(field, e) {
    	let { permissionStore, dispatch } = this.props;
    	let { addOrgData } = permissionStore.dialogData;
    	let value = e.target.value;
    	addOrgData[field] = value;
    	dispatch({
    		type: "permission/setDialogData",
    		payload: {
    			addOrgData: addOrgData
    		}
    	});
    }

    addOrgHandle() {
    	let { permissionStore, dispatch } = this.props;
    	let { dialogData } = permissionStore;
    	let { addOrgData } = dialogData;
    	if (!addOrgData.name) {
    		message.error(orgLang.addOrgModal("nameEmpty")); // 机构名称不能为空！
    		return;
    	}

    	if (addOrgData.name && addOrgData.name.length > 400) {
    		message.error(orgLang.addOrgModal("nameLen")); // 限制400字
    		return;
    	}

    	if (!addOrgData.code) {
    		message.error(orgLang.addOrgModal("identityEmpty")); // 唯一标识不能为空！
    		return;
    	}

    	let params = {
    		code: addOrgData.code,
    		name: addOrgData.name,
    		level: addOrgData.level,
    		parentUuid: addOrgData.parentUuid
    	};
    	orgAPI.addOrg(params).then((res) => {
    		if (res.success) {
    			dispatch({
    				type: "permission/getOrgList",
    				payload: {}
    			});
    			message.success(orgLang.addOrgModal("newSuccess")); // 新增机构成功
    			dispatch({
    				type: "permission/setDialogShow",
    				payload: {
    					addOrg: false
    				}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    render() {
    	let { permissionStore, dispatch, globalStore } = this.props;
    	let { dialogShow, dialogData } = permissionStore;
    	let { addOrgData } = dialogData;
    	const { personalMode } = globalStore;
    	const { lang } = personalMode;
    	let formItemLayout = {
    		labelCol: { span: lang === "cn" ? 6 : 8 },
    		wrapperCol: { span: lang === "cn" ? 16 : 14 }
    	};
    	return (
    		<div>
    			<Modal
    				disabled={!checkFunctionHasPermission("QX0101", "addOrg")}
    				title={orgLang.addOrgModal("title")} // 添加机构
    				wrapClassName="vertical-center-modal"
    				visible={dialogShow.addOrg}
    				maskClosable={false}
    				className="report-modal"
    				onOk={this.addOrgHandle.bind(this)}
    				onCancel={() => {
    					dispatch({
    						type: "permission/setDialogShow",
    						payload: {
    							addOrg: false
    						}
    					});
    					dispatch({
    						type: "permission/setDialogData",
    						payload: {
    							addOrgData: {
    								name: null,
    								code: null,
    								level: null,
    								parentUuid: null
    							}
    						}
    					});
    				}}
    			>
    				<Form layout="horizontal">
    					<FormItem
    						label={orgLang.addOrgModal("name")} // 机构名称
    						{...formItemLayout}
    					>
    						<Input name="workflowName" value={addOrgData.name || undefined}
    							onChange={this.changeFiled.bind(this, "name")}
    							placeholder={orgLang.addOrgModal("inputTip")} />
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={orgLang.addOrgModal("identifier")} // 唯一标识
    						{...formItemLayout}
    					>
    						<Input name="workflowCode" value={addOrgData.code || undefined}
    							onChange={this.changeFiled.bind(this, "code")}
    							placeholder={orgLang.addOrgModal("inputTip2")} />
    						<span className="require">*</span>
    					</FormItem>
    				</Form>
    			</Modal>
    		</div>
    	);
    }
};

export default connect(state => ({
	globalStore: state.global,
	permissionStore: state.permission
}))(AddOrgModal);
