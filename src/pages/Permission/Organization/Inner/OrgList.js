import { PureComponent } from "react";
import { connect } from "dva";
import moment from "moment";
import { Button, Popconfirm, message, Icon, Popover, Empty } from "antd";
import { orgAPI } from "@/services";
import AddOrgModal from "../Modal/AddOrgModal";
import ModifyOrgModal from "../Modal/ModifyOrgModal";
import RightOperaWrap from "@/components/RightOperaWrap";
import { checkFunctionHasPermission } from "@/utils/permission";
import { orgLang, commonLang } from "@/constants/lang";

const ButtonGroup = Button.Group;
message.config({
	maxCount: 1
});

// 显示层级
const MAX_LEVEL = 3;

class OrgList extends PureComponent {
    state = {
    	arrowTree: {}
    };
    constructor(props) {
    	super(props);
    	this.addOrgHandle = this.addOrgHandle.bind(this);
    	this.modifyOrgHandle = this.modifyOrgHandle.bind(this);
    	this.deleteOrg = this.deleteOrg.bind(this);
    	this.getPermissionConfig = this.getPermissionConfig.bind(this);
    }

    getPermissionConfig(item) {
    	let { dispatch } = this.props;
    	dispatch({
    		type: "permission/getOrgPermission",
    		payload: {
    			orgUuid: item.uuid
    		}
    	});
    	dispatch({
    		type: "permission/setAttrValue",
    		payload: {
    			selectOrg: {
    				name: item.name,
    				code: item.code,
    				uuid: item.uuid,
    				level: item.level
    			}
    		}
    	});
    }

    addOrgHandle(item) {
    	let { dispatch } = this.props;
    	dispatch({
    		type: "permission/setDialogData",
    		payload: {
    			addOrgData: {
    				level: item.level,
    				parentUuid: item.parentUuid
    			}
    		}
    	});
    	dispatch({
    		type: "permission/setDialogShow",
    		payload: {
    			addOrg: true
    		}
    	});
    }

    modifyOrgHandle(item) {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "permission/setDialogShow",
    		payload: {
    			modifyOrg: true
    		}
    	});
    	dispatch({
    		type: "permission/setDialogData",
    		payload: {
    			modifyOrgData: {
    				name: item.name,
    				code: item.code,
    				level: item.level,
    				uuid: item.uuid,
    				parentUuid: item.parentUuid
    			}
    		}
    	});
    }

    deleteOrg(uuid) {
    	let { dispatch } = this.props;
    	let params = {
    		uuid: uuid
    	};

    	orgAPI.deleteOrg(params).then((res) => {
    		if (res.success) {
    			dispatch({
    				type: "permission/getOrgList",
    				payload: {}
    			});
    			message.success(orgLang.tipInfo("removeSuccess")); // 删除机构成功
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    // 控制折叠
    arrowControl(levelCurDetail, show, e) {
    	e.stopPropagation();
    	let open = show;
    	const { arrowTree } = this.state;
    	if (arrowTree[levelCurDetail]) {
    		open = false;
    	}
    	const newArrowTree = Object.assign({}, arrowTree, {
    		[levelCurDetail]: open
    	});
    	this.setState({
    		arrowTree: newArrowTree
    	});
    }

    renderOrgItemInfo = (item, level, levelCurDetail, show) => {
    	const { arrowTree } = this.state;
    	const hasChild = item && item.children && item.children.length > 0;
    	let itemInfoObj = (
    		<div
    			className={"system-menu-title " + level}
    			onClick={this.getPermissionConfig.bind(this, item)}
    			key={item.uuid}
    		>
    			{
    				hasChild &&
                    <Icon
                    	type={`caret-${arrowTree[levelCurDetail] ? "right" : "down"}`}
                    	onClick={this.arrowControl.bind(this, levelCurDetail, show)}
                    />
    			}
    			<span className={hasChild ? "" : "oper-name"}>{item.name}</span>
    			<RightOperaWrap>
    				{/* 添加 */}
    				{
    					item.level < MAX_LEVEL &&
                        <div
                        	className="oper-list-opera-wrap"
                        	onClick={(e) => {
                        		e.stopPropagation();
                        		if (checkFunctionHasPermission("QX0101", "addOrg")) {
                        			this.addOrgHandle({
                        				level: item.level + 1,
                        				parentUuid: item.uuid
                        			});
                        		} else {
                        			message.info(orgLang.tipInfo("noPower")); // 没有权限
                        		}
                        	}}
                        >
                        	<Icon
                        		type="plus"
                        		className={checkFunctionHasPermission("QX0101", "addOrg") ? null : "a-disabled"}
                        	/>
                        	<span className="oper-list-opera-desc">{commonLang.opera("add")}</span>
                        </div>
    				}
    				{/* 删除 */}
    				{
    					checkFunctionHasPermission("QX0101", "deleteOrg")
    						? <Popconfirm
    							onClick={(e) => {
    								e.stopPropagation();
    							}}
    							title={orgLang.tipInfo("sureDelete")} // 确定要删除当前机构吗？
    							onConfirm={(e) => {
    								e.stopPropagation();
    								this.deleteOrg(item.uuid);
    							}}
    							onCancel={() => {
    							}}>
    							<div
    								className="oper-list-opera-wrap"
    							>
    								<Icon type="delete" />
    								<span className="oper-list-opera-desc">{commonLang.opera("delete")}</span>
    							</div>
    						</Popconfirm>
    						: <div
    							className="oper-list-opera-wrap"
    							onClick={(e) => {
    								e.stopPropagation();
    								message.info(orgLang.tipInfo("noPower")); // 没有权限
    							}}>
    							<Icon
    								titleDesc={commonLang.opera("delete")}
    								type="delete"
    								className="icon-disabled"

    							/>
    							<span className="oper-list-opera-desc">{commonLang.opera("delete")}</span>
    						</div>
    				}

    				{/* 查看 */}
    				<Popover
    					placement="left"
    					content={
    						<div>
    							{/* 机构名称 */}
    							<p>{orgLang.tipInfo("name")}：{item.name}</p>
    							{/* 机构标识： */}
    							<p>{orgLang.tipInfo("identity")}：{item.code}</p>
    							{/* 创建时间： */}
    							<p>{orgLang.tipInfo("creationTime")}：{moment(item.gmtCreate).format("YYYY-MM-DD HH:mm:ss")}</p>
    							{/* 修改时间： */}
    							<p>{orgLang.tipInfo("modifyTime")}：{moment(item.gmtModified).format("YYYY-MM-DD HH:mm:ss")}</p>
    						</div>
    					}
    					title={orgLang.tipInfo("checkConfig")}
    				>
    					{/* 查看配置 */}
    					<div
    						className="oper-list-opera-wrap"
    					>
    						<Icon type="profile" />
    						<span className="oper-list-opera-desc">{commonLang.opera("view")}</span>
    					</div>
    				</Popover>

    				{/* 编辑 */}
    				<div
    					onClick={(e) => {
    						e.stopPropagation();
    						if (checkFunctionHasPermission("QX0101", "modifyOrg")) {
    							this.modifyOrgHandle(item);
    						} else {
    							message.info(orgLang.tipInfo("noPower")); // 没有权限
    						}
    					}}
    				>
    					<div
    						className="oper-list-opera-wrap"
    					>
    						<Icon
    							className={checkFunctionHasPermission("QX0101", "modifyOrg") ? null : "a-disabled"}
    							type="edit"

    						/>
    						<span className="oper-list-opera-desc">{commonLang.opera("edit")}</span>
    					</div>
    				</div>

    			</RightOperaWrap>
    		</div>
    	);
    	return itemInfoObj;
    };

    // 递归渲染机构列表
    renderRecursiveTree(level, orgList, treeLevel = 0, levelDetail) {
    	const { permissionStore } = this.props;
    	const { selectOrg } = permissionStore;
    	treeLevel++;
    	let { arrowTree } = this.state;
    	let show = true;
    	if (arrowTree[levelDetail]) {
    		show = false;
    	}
    	return (
    		<ul
    			className={`system-menu-list tree top${treeLevel} ${show ? "" : "hide"}`}
    		>
    			{
    				orgList && orgList.map((item, index) => {
    					// 控制显隐的层级关系
    					let levelCurDetail = levelDetail;
    					if (levelCurDetail) {
    						levelCurDetail = levelCurDetail + "-" + index;
    					} else {
    						levelCurDetail = String(index);
    					}

    					let itemObj = (
    						<li
    							className={item.code === selectOrg.code ? "system-menu-item selected" : "system-menu-item"}
    							key={index}>
    							{
    								this.renderOrgItemInfo(item, "level" + treeLevel, levelCurDetail, show)
    							}
    							{
    								(treeLevel < (level + 1)) &&
                                    this.renderRecursiveTree(level, item.children, treeLevel, levelCurDetail)
    							}
    						</li>
    					);
    					return itemObj;
    				})
    			}
    		</ul >
    	);
    }

    render() {
    	let { permissionStore } = this.props;
    	let { orgList } = permissionStore;

    	return (
    		<div className="fix-height">
    			<div className="register-box-header">
    				<h3>
    					{/* 机构列表 */}
    					{orgLang.common("title2")}
    				</h3>
    				<ButtonGroup className="btns">
    					<Button
    						disabled={!checkFunctionHasPermission("QX0101", "addOrg")}
    						size="small"
    						icon="plus"
    						type="primary"
    						onClick={this.addOrgHandle.bind(this, {
    							level: 1,
    							parentUuid: null
    						})}
    					>
    						{/* 添加 */}
    						{orgLang.common("btn")}
    					</Button>
    				</ButtonGroup>
    			</div>
    			<div className="register-box-body white-bg">
    				{/* 递归渲染机构列表 */}
    				{
    					orgList && orgList.length > 0 && this.renderRecursiveTree(MAX_LEVEL, orgList, 0)
    				}
    				{
    					orgList.length === 0 &&
                        <Empty
                        	style={{ marginTop: "80px" }}
                        />
    				}
    			</div>
    			<AddOrgModal />
    			<ModifyOrgModal />
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	permissionStore: state.permission
}))(OrgList);
