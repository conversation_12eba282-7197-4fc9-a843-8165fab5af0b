import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import OrgList from "./Inner/OrgList";
import PermissionSetting from "@/components/Permission/Setting";
import { orgLang } from "@/constants/lang";

class Organization extends PureComponent {

	constructor(props) {
		super(props);
	}

	componentDidMount() {
		this.search();
	}

	search() {
		let { dispatch } = this.props;
		dispatch({
			type: "permission/setAttrValue",
			payload: {
				selectOrg: {},
				selectRole: {}
			}
		});
		dispatch({
			type: "permission/getOrgList",
			payload: {}
		});
	}

	render() {
		return (
			<Fragment>
				<div className="page-global-header">
					<div className="left-info">
						<h2>
							{/* 机构管理 */}
							{orgLang.common("title")}
						</h2>
					</div>
				</div>

				<div className="page-global-body">
					<div className="system-register">
						<div className="system-register-body">
							<div className="register-box register-box-5">
								<OrgList />
							</div>
							<div className="register-box register-box-6-no-margin">
								<PermissionSetting pageName="org" />
							</div>
						</div>
					</div>
				</div>
			</Fragment>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	permissionStore: state.permission
}))(Organization);
