import { PureComponent } from "react";
import { connect } from "dva";
import { Form, Input, message, Modal } from "antd";
import { roleAPI } from "@/services";
import { roleLang } from "@/constants/lang";

const FormItem = Form.Item;

class AddRoleModal extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.changeFiled = this.changeFiled.bind(this);
    	this.addRoleHandle = this.addRoleHandle.bind(this);
    }

    changeFiled(field, e) {
    	let { permissionStore, dispatch } = this.props;
    	let { addRoleData } = permissionStore.dialogData;
    	let value = e.target.value;
    	addRoleData[field] = value;
    	dispatch({
    		type: "permission/setDialogData",
    		payload: {
    			addRoleData: addRoleData
    		}
    	});
    }

    addRoleHandle() {
    	let { permissionStore, dispatch } = this.props;
    	let { dialogData, rolePageData } = permissionStore;
    	let { addRoleData } = dialogData;
    	let { selectOrgList } = rolePageData;

    	if (!addRoleData.name) {
    		message.error(roleLang.modal("nameEmpty")); // 角色名称不能为空！
    		return;
    	}
    	if (!addRoleData.code) {
    		message.error(roleLang.modal("identityEmpty")); // 唯一标识不能为空！
    		return;
    	}
    	// console.log(addRoleData.code);
    	let params = {
    		code: addRoleData.code,
    		name: addRoleData.name,
    		orgUuid: addRoleData.orgUuid,
    		type: addRoleData.type || "default"
    	};
    	roleAPI.addRole(params).then((res) => {
    		if (res.success) {
    			if (selectOrgList.length) {
    				dispatch({
    					type: "permission/getRoleByOrg",
    					payload: {
    						orgUuid: selectOrgList[selectOrgList.length - 1]
    					}
    				});
    			}
    			message.success(roleLang.modal("newSuccess")); // 新增角色成功
    			dispatch({
    				type: "permission/setDialogShow",
    				payload: {
    					addRole: false
    				}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    render() {
    	let { permissionStore, dispatch, globalStore } = this.props;
    	let { dialogShow, dialogData } = permissionStore;
    	let { addRoleData } = dialogData;
    	const { personalMode } = globalStore;
    	const { lang } = personalMode;
    	let formItemLayout = {
    		labelCol: { span: lang === "cn" ? 6 : 8 },
    		wrapperCol: { span: lang === "cn" ? 16 : 14 }
    	};
    	return (
    		<div>
    			<Modal
    				title={roleLang.modal("title")} // 新增角色
    				visible={dialogShow.addRole}
    				maskClosable={false}
    				onOk={this.addRoleHandle.bind(this)}
    				onCancel={() => {
    					dispatch({
    						type: "permission/setDialogShow",
    						payload: {
    							addRole: false
    						}
    					});
    					dispatch({
    						type: "permission/setDialogData",
    						payload: {
    							addRoleData: {
    								name: null,
    								code: null,
    								orgUuid: null,
    								type: null
    							}
    						}
    					});
    				}}
    			>
    				<Form layout="horizontal">
    					<FormItem
    						label={roleLang.modal("name")} // 角色名称
    						{...formItemLayout}
    					>
    						<Input value={addRoleData.name || undefined}
    							onChange={this.changeFiled.bind(this, "name")}
    							placeholder={roleLang.modal("inputTip")} />
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={roleLang.modal("identifier")} // 角色标识
    						{...formItemLayout}
    					>
    						<Input value={addRoleData.code || undefined}
    							onChange={this.changeFiled.bind(this, "code")}
    							placeholder={roleLang.modal("inputTip2")} />
    						<span className="require">*</span>
    					</FormItem>
    				</Form>
    			</Modal>
    		</div>
    	);
    }
};

export default connect(state => ({
	permissionStore: state.permission,
	globalStore: state.global
}))(AddRoleModal);
