import { PureComponent } from "react";
import { connect } from "dva";
import { Form, Input, message, Modal } from "antd";
import { roleAPI } from "@/services";
import string from "@/utils/string";
import { roleLang } from "@/constants/lang";

const FormItem = Form.Item;

class ModifyRoleModal extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.changeFiled = this.changeFiled.bind(this);
    	this.modifyRoleHandle = this.modifyRoleHandle.bind(this);
    }

    changeFiled(field, e) {
    	let { permissionStore, dispatch } = this.props;
    	let { modifyRoleData } = permissionStore.dialogData;
    	let value = e.target.value;
    	modifyRoleData[field] = value;
    	dispatch({
    		type: "permission/setDialogData",
    		payload: {
    			modifyRoleData: modifyRoleData
    		}
    	});
    }

    modifyRoleHandle() {
    	let { permissionStore, dispatch } = this.props;
    	let { dialogData, rolePageData } = permissionStore;
    	let { modifyRoleData } = dialogData;
    	let { selectOrgList } = rolePageData;

    	if (string.isBlank(modifyRoleData.name)) {
    		message.error(roleLang.modal("nameEmpty")); // 角色名称不能为空！
    		return;
    	}
    	if (string.isBlank(modifyRoleData.code)) {
    		message.error(roleLang.modal("identityEmpty")); // 唯一标识不能为空！
    		return;
    	}
    	let params = {
    		code: modifyRoleData.code,
    		name: modifyRoleData.name,
    		orgUuid: modifyRoleData.orgUuid,
    		uuid: modifyRoleData.uuid,
    		type: modifyRoleData.type || "default"
    	};
    	roleAPI.modifyRole(params).then((res) => {
    		if (res.success) {
    			if (selectOrgList.length) {
    				dispatch({
    					type: "permission/getRoleByOrg",
    					payload: {
    						orgUuid: modifyRoleData.orgUuid
    					}
    				});
    			}
    			message.success(roleLang.modal("modifyRoleSuccess")); // 修改角色成功
    			dispatch({
    				type: "permission/setDialogShow",
    				payload: {
    					modifyRole: false
    				}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    render() {
    	let { permissionStore, dispatch, globalStore } = this.props;
    	let { dialogShow, dialogData } = permissionStore;
    	let { modifyRoleData } = dialogData;
    	const { personalMode } = globalStore;
    	const { lang } = personalMode;
    	let formItemLayout = {
    		labelCol: { span: lang === "cn" ? 6 : 8 },
    		wrapperCol: { span: lang === "cn" ? 16 : 14 }
    	};
    	return (
    		<div>
    			<Modal
    				title={roleLang.modal("title2")} // 修改角色信息
    				visible={dialogShow.modifyRole}
    				maskClosable={false}
    				onOk={this.modifyRoleHandle.bind(this)}
    				onCancel={() => {
    					dispatch({
    						type: "permission/setDialogShow",
    						payload: {
    							modifyRole: false
    						}
    					});
    					dispatch({
    						type: "permission/setDialogData",
    						payload: {
    							modifyRoleData: {
    								name: null,
    								code: null,
    								level: null,
    								parentUuid: null
    							}
    						}
    					});
    				}}
    			>
    				<Form layout="horizontal">
    					<FormItem
    						label={roleLang.modal("name")} // 角色名称
    						{...formItemLayout}
    					>
    						<Input value={modifyRoleData.name || undefined}
    							onChange={this.changeFiled.bind(this, "name")}
    							placeholder={roleLang.modal("inputTip")} />
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={roleLang.modal("identifier")} // 角色标识
    						{...formItemLayout}
    					>
    						<Input value={modifyRoleData.code || undefined}
    							onChange={this.changeFiled.bind(this, "code")}
    							placeholder={roleLang.modal("inputTip2")} />
    						<span className="require">*</span>
    					</FormItem>
    				</Form>
    			</Modal>
    		</div>
    	);
    }
};

export default connect(state => ({
	permissionStore: state.permission,
	globalStore: state.global
}))(ModifyRoleModal);
