import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { TreeSelect, Row, Col } from "antd";
import RoleList from "./Inner/RoleList";
import PermissionSetting from "@/components/Permission/Setting";
import { roleLang } from "@/constants/lang";
import { gutterSpan } from "@/constants/common";

const TreeNode = TreeSelect.TreeNode;

class Role extends PureComponent {

	constructor(props) {
		super(props);
	}

	componentDidMount() {
		this.search();
	}

	search() {
		let { dispatch } = this.props;
		dispatch({
			type: "permission/setAttrValue",
			payload: {
				selectOrg: {},
				selectRole: {},
				rolePageData: {
					selectOrgList: [],
					roleList: []
				}
			}
		});
		dispatch({
			type: "permission/getOrgList",
			payload: {}
		});
	}

    changeOrgHandle = (value) => {
    	if (!value) {
    		return;
    	}
    	let { dispatch } = this.props;
    	let orgList = [];

    	orgList.push(value);
    	dispatch({
    		type: "permission/setRolePageData",
    		payload: {
    			selectOrgList: orgList || []
    		}
    	});
    	dispatch({
    		type: "permission/getRoleByOrg",
    		payload: {
    			orgUuid: value
    		}
    	});
    }

    render() {
    	let { permissionStore } = this.props;
    	let { orgList, rolePageData } = permissionStore;
    	let { selectOrgList } = rolePageData;
    	return (
    		<Fragment>
    			<div className="page-global-header">
    				<div className="left-info">
    					<h2>
    						{/* 角色管理 */}
    						{roleLang.common("title")}
    					</h2>
    				</div>
    			</div>
    			<div className="page-global-body">
    				<Row gutter={gutterSpan} className="mb10">
    					<Col span={5}>
    						<TreeSelect
    							className="right-info-tree w100p"
    							value={selectOrgList || []}
    							placeholder={roleLang.common("title2")} // 请选择机构
    							treeDefaultExpandAll
    							onChange={this.changeOrgHandle.bind(this)}
    						>
    							{
    								orgList.map((item, index) =>
    									<TreeNode key={index} title={item.name} value={item.uuid}>
    										{
    											item.children && item.children.map((child, i) =>
    												<TreeNode title={child.name} key={index + "-" + i} value={child.uuid}>
    													{
    														child.children && child.children.map((secChild, j) =>
    															<TreeNode title={secChild.name} key={index + "-" + i + "-" + j}
    																value={secChild.uuid}></TreeNode>
    														)
    													}
    												</TreeNode>)
    										}
    									</TreeNode>)
    							}
    						</TreeSelect>
    					</Col>
    				</Row>
    				<div className="system-register">
    					<div className="system-register-body" style={{"height": "calc(100vh - 160px)"}}>
    						<div className="register-box register-box-5">
    							<RoleList />
    						</div>
    						<div className="register-box register-box-6-no-margin">
    							<PermissionSetting pageName="role" />
    						</div>
    					</div>
    				</div>
    			</div>
    		</Fragment>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	permissionStore: state.permission
}))(Role);
