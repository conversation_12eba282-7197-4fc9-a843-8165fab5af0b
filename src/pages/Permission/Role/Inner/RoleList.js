import { PureComponent } from "react";
import { connect } from "dva";
import moment from "moment";
import { Button, Popconfirm, message, Icon, Popover } from "antd";
import { roleAPI } from "@/services";
import AddRoleModal from "../Modal/AddRoleModal";
import ModifyRoleModal from "../Modal/ModifyRoleModal";
import { checkFunctionHasPermission } from "@/utils/permission";
import { roleLang } from "@/constants/lang";
import EmptyPage from "@/components/EmptyPage";

const ButtonGroup = Button.Group;

class RoleList extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.addRoleHandle = this.addRoleHandle.bind(this);
    	this.modifyRoleHandle = this.modifyRoleHandle.bind(this);
    	this.getPermissionConfig = this.getPermissionConfig.bind(this);
    	this.deleteRole = this.deleteRole.bind(this);
    }

    addRoleHandle() {
    	let { permissionStore, dispatch } = this.props;
    	let { rolePageData } = permissionStore;
    	let { selectOrgList } = rolePageData;
    	let orgUuid = selectOrgList[selectOrgList.length - 1];
    	dispatch({
    		type: "permission/setDialogData",
    		payload: {
    			addRoleData: {
    				orgUuid: orgUuid
    			}
    		}
    	});
    	dispatch({
    		type: "permission/setDialogShow",
    		payload: {
    			addRole: true
    		}
    	});
    }

    modifyRoleHandle(item) {
    	let { permissionStore, dispatch } = this.props;
    	let { rolePageData } = permissionStore;
    	let { selectOrgList } = rolePageData;
    	let orgUuid = selectOrgList[selectOrgList.length - 1];
    	dispatch({
    		type: "permission/setDialogData",
    		payload: {
    			modifyRoleData: {
    				orgUuid: orgUuid,
    				uuid: item.uuid,
    				name: item.name,
    				code: item.code,
    				type: item.type
    			}
    		}
    	});
    	dispatch({
    		type: "permission/setDialogShow",
    		payload: {
    			modifyRole: true
    		}
    	});
    }

    getPermissionConfig(item) {
    	let { permissionStore, dispatch } = this.props;
    	let { selectRole } = permissionStore;
    	if (selectRole.uuid !== item.uuid) {
    		dispatch({
    			type: "permission/getRolePermission",
    			payload: {
    				roleUuid: item.uuid
    			}
    		});
    		dispatch({
    			type: "permission/setAttrValue",
    			payload: {
    				selectRole: item
    			}
    		});
    	}
    }

    deleteRole(uuid) {
    	let { permissionStore, dispatch } = this.props;
    	let { orgList, rolePageData } = permissionStore;
    	let { selectOrgList } = rolePageData;
    	let orgUuid = selectOrgList[selectOrgList.length - 1];
    	roleAPI.deleteRole(uuid).then((res) => {
    		if (res.success) {
    			if (orgList.length) {
    				dispatch({
    					type: "permission/getRoleByOrg",
    					payload: {
    						orgUuid: orgUuid
    					}
    				});
    			}
    			message.success(roleLang.tipInfo("removeSuccess")); // 删除角色成功
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    render() {
    	let { permissionStore } = this.props;
    	let { selectRole, rolePageData } = permissionStore;
    	let { selectOrgList, roleList } = rolePageData;
    	return (
    		<div style={{ position: "relative", height: "100%" }}>
    			<div className="register-box-header">
    				<h3>角色列表</h3>
    				<ButtonGroup className="btns">
    					<Button
    						size="small"
    						icon="plus"
    						type="primary"
    						disabled={selectOrgList.length ? !checkFunctionHasPermission("QX0102", "addRole") : true}
    						onClick={this.addRoleHandle.bind(this)}
    					>
    						{/* 添加角色 */}
    						{roleLang.common("btn")}
    					</Button>
    				</ButtonGroup>
    			</div>
    			<div className="register-box-body white-bg">
    				<ul className={`system-menu-list ${roleList && roleList.length ? "pt10" : ""}`}>
    					{
    						roleList && roleList.map((item, index) => {
    							return (
    								<li
    									className={item.uuid === selectRole.uuid ? "system-menu-item selected" : "system-menu-item"}
    									onClick={this.getPermissionConfig.bind(this, item)}
    									key={index}
    								>
    									<div className="system-menu-title">
    										<span>{item.name}</span>
    										<div className="oper-list" onClick={(e)=>{e.stopPropagation();}}>
    											{/* 删除 */}
    											<Popconfirm title={roleLang.tipInfo("sureDelete")} // 确定要删除当前角色吗？
    												onConfirm={(e) => {
    													e.stopPropagation();
    													if (checkFunctionHasPermission("QX0102", "deleteRole")) {
    														this.deleteRole(item.uuid);
    													} else {
    														message.info(roleLang.tipInfo("noPower")); // 没有权限
    													}
    												}}
    												onCancel={() => {
    												}}>
    												<Icon type="delete" />
    											</Popconfirm>
    											{/* 查看 */}
    											<Popover placement="left"
    												content={
    													<div>
    														<p>
    															{/* 角色名称： */}
    															{roleLang.tipInfo("name")}：
																	 {item.name}
    														</p>
    														<p>
    															{/* 角色标识： */}
    															{roleLang.tipInfo("identity")}：
																	 {item.code}
    														</p>
    														<p>
    															{/* 创建时间： */}
    															{roleLang.tipInfo("creationTime")}：
																	 {moment(item.gmtCreate).format("YYYY-MM-DD HH:mm:ss")}
    														</p>
    														<p>
    															{/* 修改时间： */}
    															{roleLang.tipInfo("modifyTime")}：
																	 {moment(item.gmtModified).format("YYYY-MM-DD HH:mm:ss")}
    														</p>
    													</div>
    												}
    												title={roleLang.tipInfo("checkDetail")}>
    												{/* 查看详情 */}
    												<Icon type="profile" />
    											</Popover>
    											<Icon type="edit"
    												onClick={(e) => {
    													e.stopPropagation();
    													if (checkFunctionHasPermission("QX0102", "modifyRole")) {
    														this.modifyRoleHandle(item);
    													} else {
    														message.info(roleLang.tipInfo("noPower")); // 没有权限
    													}
    												}}
    											/>
    										</div>
    									</div>
    								</li>
    							);
    						})
    					}
    					{
    						roleList.length === 0 &&
                            <EmptyPage
                            	text={
                            		selectOrgList.length ? roleLang.common("title3") : roleLang.common("title2") // "当前机构下暂无角色" : "请选择机构"
                            	}
                            />
    					}
    				</ul>
    			</div>
    			<AddRoleModal />
    			<ModifyRoleModal />
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	permissionStore: state.permission
}))(RoleList);
