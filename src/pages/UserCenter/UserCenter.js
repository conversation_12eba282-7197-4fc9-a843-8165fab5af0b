import { PureComponent } from "react";
import { connect } from "dva";
import { Tabs } from "antd";
import "./UserCenter.less";
import BaseInfo from "./Inner/BaseInfo";
import ModifyPassword from "./Inner/ModifyPassword";
import LoginTime from "./Inner/LoginTime";
import Cookies from "universal-cookie";
import { routerRedux } from "dva/router";
import { searchToObject } from "@/utils/utils";
import { userCenterLang } from "@/constants/lang";

const TabPane = Tabs.TabPane;

class UserCenter extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.switchTab = this.switchTab.bind(this);
    }

    componentDidMount() {
    	let { dispatch } = this.props;
    	// const cookies = new Cookies();
    	// let uuid = cookies.get("_uid_");
    	// let params = {
    	// 	uuid: uuid
    	// };
    	dispatch({
    		type: "userCenter/getUserInfo"
    		// payload: params
    	});
    }

    switchTab(key) {
    	let { location, dispatch } = this.props;
    	let { pathname } = location;
    	let search = "?currentTab=" + key;
    	dispatch(routerRedux.push(pathname + search));
    }

    render() {
    	let { location } = this.props;
    	let { search } = location;
    	let searchObj = search && searchToObject(search) ? searchToObject(search) : null;
    	let currentTab = searchObj && searchObj.currentTab ? parseInt(searchObj.currentTab, 10) : 1;
    	return (
    		<div className="user-center-tab page-global-tab">
    			<Tabs activeKey={currentTab.toString()} onChange={this.switchTab.bind(this)}>
    				<TabPane
    					tab={userCenterLang.common("personalSetting")} // lang:个人设置
    					key="1"
    				>
    					<BaseInfo />
    				</TabPane>
    				<TabPane
    					tab={userCenterLang.common("modifyPassword")} // lang:修改密码
    					key="2"
    				>
    					<ModifyPassword />
    				</TabPane>
    				<TabPane
    					tab={userCenterLang.common("loginHistory")} // lang:登录历史
    					key="3"
    				>
    					<LoginTime />
    				</TabPane>
    			</Tabs>
    		</div>
    	);
    }
}

export default connect(state => ({
	userCenterStore: state.userCenter
}))(UserCenter);
