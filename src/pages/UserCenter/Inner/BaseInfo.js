import { PureComponent } from "react";
import { connect } from "dva";
import { Tag, Button, Radio, Form, Row, Col, Input, message, DatePicker } from "antd";
import { userAPI } from "@/services";
import { gutterSpan, colorArray } from "@/constants/common";
import { userCenterLang } from "@/constants/lang";
import moment from "moment";

const TextArea = Input.TextArea;
const RadioGroup = Radio.Group;

message.config({
	maxCount: 1
});

class BaseInfo extends PureComponent {
	constructor(props) {
		super(props);
		this.modifyUserInfo = this.modifyUserInfo.bind(this);
		this.submitModify = this.submitModify.bind(this);
		this.handleChangeAvatar = this.handleChangeAvatar.bind(this);
	}

	modifyUserInfo(type, e) {
		let { dispatch, userCenterStore } = this.props;
		let { userInfo } = userCenterStore;
		let value = null;
		if (type === "avatar") {
			value = e;
		} else {
			value = e.target.value;
		}
		userInfo[type] = value;
		dispatch({
			type: "userCenter/setAttrValue",
			payload: {
				userInfo: userInfo
			}
		});
	}

	submitModify() {
		let { userCenterStore, globalStore, dispatch } = this.props;
		let { userInfo } = userCenterStore;
		let { userInfoMode } = globalStore;

		if (userInfo.phone && userInfo.phone.length > 0 && !(/^1[3456789]\d{9}$/.test(userInfo.phone))) {
			// lang:手机号码填写有误
			message.error(userCenterLang.personal("wrongNumberTip"));
			return;
		}
		if (userInfo.email && userInfo.email.length > 0 && !(/^([0-9A-Za-z\-_\.]+)@([0-9a-z]+\.[a-z]{2,3}(\.[a-z]{2})?)$/g.test(userInfo.email))) {
			// lang:邮箱填写有误
			message.error(userCenterLang.personal("wrongEmailTip"));
			return;
		}
		let params = {
			userName: userInfo.userName,
			phone: userInfo.phone,
			email: userInfo.email,
			gender: userInfo.gender,
			sign: userInfo.sign,
			avatar: userInfo.avatar
		};
		userAPI.modifyUserInfo(params).then((res) => {
			if (res.success) {
				// lang:修改成功
				message.success(userCenterLang.personal("modifySuccessTip"));

				userInfoMode["avatar"] = userInfo.avatar;

				dispatch({
					type: "global/setAttrValue",
					payload: {
						userInfoMode: userInfoMode
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	getBase64(img, callback) {
		const reader = new FileReader();
		reader.addEventListener("load", () => callback(reader.result));
		reader.readAsDataURL(img);
	}

	handleChangeAvatar(avatarName) {
		let { dispatch, userCenterStore } = this.props;
		let { userInfo } = userCenterStore;

		userInfo["avatar"] = avatarName;
		dispatch({
			type: "userCenter/setAttrValue",
			payload: {
				userInfo: userInfo
			}
		});
	}

	render() {
		let { userCenterStore } = this.props;
		let { userInfo } = userCenterStore;

		let avatarImageMap = ["male1", "male2", "male3", "male4", "male5", "male6", "female1", "female2", "female3", "female4", "female5", "female6"];
		return (
			<div className="page-global-body">
				<div className="page-global-body-main">
					<div className="basic-form user-center-main">
						<Form>
							<Row gutter={gutterSpan} className="auto-height">
								<Col span={5} className="basic-info-title">
									{/* lang:头像 */}
									{userCenterLang.personal("avatar")}
								</Col>
								<Col span={12}>
									<ul className="avatar-list">
										{
											avatarImageMap.map((item, index) => {
												return (
													<li
														key={index}
														className={item === userInfo.avatar ? "select" : ""}
														onClick={this.handleChangeAvatar.bind(this, item)}
													>
														<img src={`/bifrost-resource/avatar/${item}.png`} />
													</li>
												);
											})
										}
									</ul>
								</Col>
							</Row>
							<Row gutter={gutterSpan} className="auto-height">
								<Col span={5} className="basic-info-title">
									{/* lang:登录账号 */}
									{userCenterLang.personal("account")}
								</Col>
								<Col span={6}>
									<Input
										type="text"
										value={userInfo && userInfo.account || undefined}
										disabled={true}
									/>
								</Col>
							</Row>
							<Row gutter={gutterSpan} className="auto-height">
								<Col span={5} className="basic-info-title">
									{/* lang:用户名 */}
									{userCenterLang.personal("userName")}
								</Col>
								<Col span={6}>
									<Input
										type="text"
										placeholder={userCenterLang.personal("userNamePlaceholder")} // lang:请输入用户名
										value={userInfo && userInfo.userName || undefined}
										onChange={this.modifyUserInfo.bind(this, "userName")}
									/>
								</Col>
							</Row>
							<Row gutter={gutterSpan} className="auto-height">
								<Col span={5} className="basic-info-title">
									{/* lang:手机号 */}
									{userCenterLang.personal("mobile")}
								</Col>
								<Col span={6}>
									<Input
										type="text"
										placeholder={userCenterLang.personal("mobilePlaceholder")} // lang:请输入手机号
										value={userInfo && userInfo.phone || undefined}
										onChange={this.modifyUserInfo.bind(this, "phone")}
									/>
								</Col>
							</Row>
							<Row gutter={gutterSpan} className="auto-height">
								<Col span={5} className="basic-info-title">
									{/* lang:邮箱 */}
									{userCenterLang.personal("email")}
								</Col>
								<Col span={6}>
									<Input
										type="text"
										placeholder={userCenterLang.personal("emailPlaceholder")} // lang:请输入手机号
										value={userInfo && userInfo.email || undefined}
										onChange={this.modifyUserInfo.bind(this, "email")}
									/>
								</Col>
							</Row>
							<Row gutter={gutterSpan} className="auto-height">
								<Col span={5} className="basic-info-title">
									{/* lang:所属机构 */}
									{userCenterLang.personal("organization")}
								</Col>
								<Col span={6}>
									<Input
										type="text"
										disabled={true}
										value={userInfo && userInfo.orgName || undefined}
									/>
								</Col>
							</Row>
							<Row gutter={gutterSpan} className="auto-height">
								<Col span={5} className="basic-info-title">
									{/* lang:应用权限 */}
									{userCenterLang.personal("app")}
								</Col>
								<Col span={12}>
									<div>
										{
											userInfo && userInfo.apps && userInfo.apps.map((item, index) => {
												return (
													<Tag
														color={colorArray[index]}
														key={index}>{item.displayName}
													</Tag>
												);
											})
										}
									</div>
								</Col>
							</Row>
							<Row gutter={gutterSpan} className="auto-height">
								<Col span={5} className="basic-info-title">
									{/* lang:性别 */}
									{userCenterLang.personal("gender")}
								</Col>
								<Col span={9}>
									<RadioGroup
										value={userInfo && userInfo.gender || 0}
										onChange={this.modifyUserInfo.bind(this, "gender")}
									>
										<Radio value={0}>
											{/* lang:保密 */}
											{userCenterLang.personal("secret")}
										</Radio>
										<Radio value={1}>
											{/* lang:男 */}
											{userCenterLang.personal("male")}
										</Radio>
										<Radio value={2}>
											{/* lang:女 */}
											{userCenterLang.personal("female")}
										</Radio>
									</RadioGroup>
								</Col>
							</Row>
							<Row gutter={gutterSpan} className="auto-height">
								<Col span={5} className="basic-info-title">
									{/* lang: 过期时间 */}
									{userCenterLang.personal("expirationTime")}
								</Col>
								<Col span={9}>
									<DatePicker
										style={{ width: "100%" }}
										format="YYYY-MM-DD"
										value={userInfo.expiration ? moment(userInfo.expiration) : undefined}
										// 不可选的日期
										disabled
									/>
								</Col>
							</Row>
							<Row gutter={gutterSpan} className="auto-height">
								<Col span={5} className="basic-info-title">
									{/* lang:签名 */}
									{userCenterLang.personal("signature")}
								</Col>
								<Col span={10}>
									<TextArea
										placeholder={userCenterLang.personal("signaturePlaceholder")} // lang:请输入签名
										row={4}
										value={userInfo && userInfo.sign || undefined}
										onChange={this.modifyUserInfo.bind(this, "sign")}
									></TextArea>
								</Col>
							</Row>
							<Row gutter={gutterSpan} className="mt20" className="auto-height">
								<Col span={5}></Col>
								<Col span={6} className="basic-info-btns">
									<Button type="primary" onClick={this.submitModify.bind(this)}>
										{/* lang:保存 */}
										{userCenterLang.personal("save")}
									</Button>
								</Col>
							</Row>
						</Form>
					</div>
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	userCenterStore: state.userCenter,
	globalStore: state.global
}))(BaseInfo);
