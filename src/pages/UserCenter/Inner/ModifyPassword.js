import { PureComponent } from "react";
import { connect } from "dva";
import { Button, message, Form, Row, Col, Input, Icon } from "antd";
import { gutterSpan } from "@/constants/common";
import { userCenterLang } from "@/constants/lang";
import userAPI from "@/services/user";
import { rsaPwd, checkRegPwd } from "@/utils/user";

const form = {};

class ModifyPassword extends PureComponent {
    state = {
    	oldPwd: true,
    	newPwd: true,
    	checkPwd: true
    }
    constructor(props) {
    	super(props);
    	this.submitModify = this.submitModify.bind(this);
    	this.changeValue = this.changeValue.bind(this);
    }

    changeValue(type, e) {
    	form[type] = e.target.value;
    }

    changeEye=(type)=>{
    	this.setState({
    		[type]: !this.state[type]
    	});
    }

    submitModify() {
    	const { oldPwd, newPwd, checkPwd } = form;
    	if (!oldPwd) {
    		// lang:原密码不能为空
    		message.warning(userCenterLang.password("passwordEmpty1"));
    		return;
    	}
    	if (!newPwd) {
    		// lang:新密码不能为空
    		message.warning(userCenterLang.password("passwordEmpty2"));
    		return;
    	}
    	if (!checkRegPwd(newPwd)) {
    		message.error(userCenterLang.password("regPwd1")); // 密码必须包含大小写字母、数字、特殊字符，长度8~18位
    		return;
    	}
    	if (checkPwd && !checkRegPwd(checkPwd)) {
    		message.error(userCenterLang.password("regPwd2")); // 确认密码必须包含大小写字母、数字、特殊字符，长度8~18位
    		return;
    	}
    	if (newPwd !== checkPwd) {
    		// lang:两次输入的密码不一致
    		message.warning(userCenterLang.password("passwordEmpty3"));
    		return;
    	}
    	let params = {
    		oldPwd: rsaPwd(oldPwd),
    		newPwd: rsaPwd(newPwd)
    	};
    	userAPI.changePwd(params).then((res) => {
    		if (res.success) {

    			// 修改密码成功后，原csrfToken失效，需要重新获取csrfToken
    			const { csrfToken } = res.data || {};
    			sessionStorage.setItem("_csrf_", csrfToken);
    			localStorage.setItem("_sync_qjt_csrf_", csrfToken); // 同步到其他页面

    			// lang:修改密码成功
    			message.success(userCenterLang.password("passwordEmpty4"));
    			setTimeout(() => {
    				this.props.dispatch({
    					type: "login/logout"
    				});
    			}, 500);
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    render() {
    	const { oldPwd, newPwd, checkPwd } = this.state;
    	return (
    		<div className="page-global-body">
    			<div className="page-global-body-main">
    				<div className="basic-form user-center-main">
    					<Form>
    						<Row gutter={gutterSpan}>
    							<Col span={5} className="basic-info-title">
    								{/* lang:原密码 */}
    								{userCenterLang.password("originalPassword")}
    							</Col>
    							<Col span={6}>
    								<Input
    									onChange={this.changeValue.bind(this, "oldPwd")}
    									type={oldPwd ? "password" : "text"}
    									autocomplete="new-password"
    									placeholder={userCenterLang.password("originalPasswordPlaceholder")} // lang:请输入原密码
    									suffix={
    										<span onClick={this.changeEye.bind(this, "oldPwd")}>
    											{
    										        oldPwd ? <Icon type="eye" /> : <Icon type="eye-invisible" />
    											}
    										</span>
    									}
    								/>
    							</Col>
    						</Row>
    						<Row gutter={gutterSpan}>
    							<Col span={5} className="basic-info-title">
    								{/* lang:新密码 */}
    								{userCenterLang.password("newPassword")}
    							</Col>
    							<Col span={6}>
    								<Input
    									onChange={this.changeValue.bind(this, "newPwd")}
    									type={newPwd ? "password" : "text"}
    									autocomplete="new-password"
    									placeholder={userCenterLang.password("newPasswordPlaceholder")} // lang:请输入新密码
    									suffix={
    										<span onClick={this.changeEye.bind(this, "newPwd")}>
    											{
    										        newPwd ? <Icon type="eye" /> : <Icon type="eye-invisible" />
    											}
    										</span>
    									}
    								/>
    							</Col>
    						</Row>
    						<Row gutter={gutterSpan}>
    							<Col span={5} className="basic-info-title">
    								{/* lang:确认新密码 */}
    								{userCenterLang.password("confirmNewPassword")}
    							</Col>
    							<Col span={6}>
    								<Input
    									onChange={this.changeValue.bind(this, "checkPwd")}
    									type={checkPwd ? "password" : "text"}
    									autocomplete="new-password"
    									placeholder={userCenterLang.password("confirmNewPasswordPlaceholder")} // lang:请确认新密码
    									suffix={
    										<span onClick={this.changeEye.bind(this, "checkPwd")}>
    											{
    										        checkPwd ? <Icon type="eye" /> : <Icon type="eye-invisible" />
    											}
    										</span>
    									}
    								/>
    							</Col>
    						</Row>
    						<Row gutter={gutterSpan} className="mt20">
    							<Col span={5}></Col>
    							<Col span={6} className="basic-info-btns">
    								<Button type="primary" onClick={this.submitModify.bind(this)}>
    									{/* lang:修改 */}
    									{userCenterLang.password("modify")}
    								</Button>
    							</Col>
    						</Row>
    					</Form>
    				</div>
    			</div>
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	solutionStore: state.solution
}))(ModifyPassword);
