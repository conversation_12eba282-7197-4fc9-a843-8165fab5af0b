import { PureComponent } from "react";
import { connect } from "dva";
import { Table, Pagination } from "antd";
import moment from "moment";
import { commonLang, userCenterLang } from "@/constants/lang";

class LoginTime extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.paginationOnChange = this.paginationOnChange.bind(this);
    	let { dispatch } = this.props;
    	let params = {
    		pageSize: 10,
    		curPage: 1
    	};
    	dispatch({
    		type: "userCenter/getLoginHistory",
    		payload: params
    	});
    }

    paginationOnChange(current, pageSize) {
    	let { dispatch } = this.props;
    	dispatch({
    		type: "userCenter/initloginHistoryData",
    		payload: {
    			pageSize: pageSize,
    			curPage: current
    		}
    	});
    	dispatch({
    		type: "userCenter/getLoginHistory",
    		payload: {
    			pageSize: pageSize,
    			curPage: current
    		}
    	});
    }

    render() {
    	let { userCenterStore } = this.props;
    	let { loginHistoryData } = userCenterStore;
    	const columns = [{
    		title: userCenterLang.history("account"),		// lang:登录账号
    		dataIndex: "account",
    		key: "account"
    	}, {
    		title: userCenterLang.history("ip"),		// lang:ip地址
    		dataIndex: "ipAddr",
    		key: "ipAddr"
    	}, {
    		title: userCenterLang.history("loginTime"),		// lang:登录时间
    		dataIndex: "loginTime",
    		key: "loginTime"
    	}];
    	loginHistoryData.loginHistory && loginHistoryData.loginHistory.map((item, index) => {
    		item["loginTime"] = moment(item["loginTime"]).format("YYYY-MM-DD HH:mm:ss");
    		item["key"] = index;
    	});

    	return (
    		<div className="page-global-body">
    			<div className="page-global-body-main">
    				<Table
    					columns={columns}
    					dataSource={loginHistoryData && loginHistoryData.loginHistory}
    					pagination={false}
    				/>
    				<div className="page-global-body-pagination">
    					<span className="count">
    						{commonLang.getRecords(loginHistoryData && loginHistoryData.total)}
    					</span>
    					<Pagination
    						showSizeChanger
    						defaultCurrent={1}
    						onChange={this.paginationOnChange.bind(this)}
    						onShowSizeChange={this.paginationOnChange.bind(this)}
    						total={loginHistoryData && loginHistoryData.total}
    						current={loginHistoryData && loginHistoryData.curPage}
    						rowKey="uuid"
    					/>
    				</div>
    			</div>
    		</div>
    	);
    }
}

export default connect(state => ({
	loginStore: state.login,
	userCenterStore: state.userCenter
}))(LoginTime);
