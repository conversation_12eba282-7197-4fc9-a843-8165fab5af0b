import { connect } from "dva";
import { routerRedux } from "dva/router";
import { useEffect, useState, useRef, memo } from "react";
import { Spin } from "antd";
import "./index.less";

const IframeView = (props) => {
	const { dispatch, location, userCenterStore } = props;
	const { userInfo = {} } = userCenterStore;
	// 获取路径
	const { search } = location;
	let path = search.replace(/^\?path=/, "");

	const [pathUrl, setPathUrl] = useState(path);
	const [iframeLoad, setIframeLoad] = useState(true);

	//  监听回调
	const postMessageCb = useRef({
		// 委托跳转
		openLink: (path) => {
			if (path) {
				setIframeLoad(true);
				dispatch(routerRedux.push(`/bridge/iframeView?path=${path}`));
			}
		},
		// 委托新开窗口
		openNewTarget: (path) => {
			if (path) {
				window.open(`/bridge/iframeView?path=${path}`);
			}
		},
		// 401弹窗
		authModal: (code)=>{
			code = String(code);
			if (code === "4011003") {
				dispatch({
					type: "global/setAttrValue",
					payload: {
						multiUserModal: true
					}
				});
			} else if (code === "100404") {
				dispatch(
					routerRedux.push(
						"/user/startup?currentTab=licence"
					)
				);
			} else {
				dispatch({
					type: "login/goLogin"
				});
			}
		}
	});

	const onMessage = (event) => {
		let data = event.data;
		if (typeof data === "string") {
			try {
				data = JSON.parse(data);
			} catch (e) {}
		}
		if (typeof (postMessageCb.current[data.func]) === "function") {
			postMessageCb.current[data.func].call(null, data.message);
		}
	};

	useEffect(()=>{
		// 获取用户信息
		dispatch({
    		type: "userCenter/getUserInfo"
		});

		// 监听跳转
		if (window.addEventListener) {
			window.addEventListener("message", onMessage, false);
		} else if (window.attachEvent) {
			window.attachEvent("onmessage", onMessage, false);
		}

		return ()=>{
			if (window.addEventListener) {
				window.removeEventListener("message", onMessage);
			} else if (window.attachEvent) {
				window.detachEvent("onmessage", onMessage);
			}
		};

	}, []);

	useEffect(() => {
		if (userInfo && userInfo.token) {
			const csrf = encodeURIComponent(sessionStorage.getItem("_csrf_"));
			const ac = userInfo.token;
			const pathParams = `ac=${ac}&cf=${csrf}`;
			if (decodeURIComponent(path).indexOf("?") > -1) {
				path = path + `&${pathParams}`;
			} else {
				path = path + `?${pathParams}`;
			}
			setPathUrl(path);
		}
	}, [userInfo.token, search]);

	return (
		<div className="iframeWrapper">
			{
				userInfo.token &&
                <iframe
                	id="childIframe"
                	src={pathUrl}
                	key={pathUrl}
                	style={{ display: iframeLoad ? "none" : "flex" }}
                	onLoad={() => {
                		setIframeLoad(false);
                	}}
                />
			}

			{iframeLoad && <Spin className="globalSpin" spinning={iframeLoad} />}
		</div>
	);
};

export default memo(connect((state) => ({
	userCenterStore: state.userCenter
}))(IframeView));
