import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, Form, message, Row, Col } from "antd";
import { applicationAPI } from "@/services";
import { applicationLang } from "@/constants/lang";

const TextArea = Input.TextArea;

class OperatorApplicationModal extends PureComponent {

	constructor(props) {
		super(props);
		this.changeFiledValue = this.changeFiledValue.bind(this);
	}

    submitModal = () => {
    	let { applicationStore, dispatch, refresh } = this.props;
    	let { dialogData: { applicationData }, dialogShow } = applicationStore;
    	let { addApplication, modifyApplication } = dialogShow;

    	if (addApplication) {
            const { uuid, name, displayName, description } = applicationData;
            let reg1 = new RegExp("[^a-zA-Z0-9\_\.\u4e00-\u9fa5]", "i");
            let reg2 = new RegExp("[^a-zA-Z0-9\_\.]", "i");
            if (displayName === "" || name === "") {
                return message.warning(applicationLang.messageInfo("submitMeg"));
            }
            if (reg1.test(displayName)) {
                return message.warning(applicationLang.messageInfo("submitMeg1"));
            }
            if (reg2.test(name)) {
                return message.warning(applicationLang.messageInfo("submitMeg2"));
            }
            if (displayName === "all") {
                return message.warning(applicationLang.messageInfo("warningCode"));
            }
            if (description?.length > 512) {
                return message.warning(applicationLang.messageInfo("submitMeg3"));
            }
            if (displayName?.length > 32) {
                return message.warning(applicationLang.messageInfo("submitMeg4"));
            }
            if (name?.length > 32) {
                return message.warning(applicationLang.messageInfo("submitMeg5"));
            }

    		applicationAPI.addApplication(applicationData).then(res => {
    			if (res.success) {
    				message.success(res.message);
    				// 刷新列表
    				// dispatch({
    				// 	type: "application/getApplicationList"
    				// });
    				refresh && refresh({ current: 1 });
    			} else {
    				message.error(res.message);
    			}
    		}).catch(e => {
    			console.log(e);
    		});
    	} else if (modifyApplication) {
    		applicationAPI.modifyApplication(applicationData).then(res => {
    			if (res.success) {
    				message.success(res.message);
    				// dispatch({
    				// 	type: "application/getApplicationList"
    				// });
    				refresh && refresh();
    			} else {
    				message.error(res.message);
    			}
    		}).catch(e => {
    			// console.log(e);
    		});
    	}

    	this.closeModal();
    }

    closeModal = () => {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "application/setAttrValue",
    		payload: {
    			dialogShow: {
    				addApplication: false,
    				modifyApplication: false
    			}
    		}
    	});

    	setTimeout(() => {
    		dispatch({
    			type: "application/setAttrValue",
    			payload: {
    				dialogData: {
    					applicationData: {
    						uuid: null,
    						name: null,
    						displayName: null,
    						description: null
    					}
    				}
    			}
    		});
    	}, 300);
    }

    changeFiledValue(field, type, e) {
    	let { applicationStore, dispatch } = this.props;
    	let { applicationData } = applicationStore.dialogData;

    	let value;
    	if (type === "select") {
    		value = e;
    	} else if (type === "input") {
    		value = e.target.value;
    	}

    	applicationData[field] = value;

    	dispatch({
    		type: "application/setAttrValue",
    		payload: {
    			dialogData: {
    				applicationData
    			}
    		}
    	});
    }

    render() {
    	let { applicationStore } = this.props;
    	let { dialogShow, dialogData: { applicationData } } = applicationStore;
    	let { addApplication, modifyApplication } = dialogShow;
    	let { name, displayName, description } = applicationData;

    	console.log(applicationData);
    	const disabled = modifyApplication;
    	return (
    		<Modal
    			title={addApplication ? applicationLang.modal("title") : applicationLang.modal("title1")} // lang:新增系统配置or修改系统配置
    			visible={addApplication || modifyApplication}
    			maskClosable={false}
    			onCancel={this.closeModal}
    			onOk={this.submitModal}
    			width={600}
    		>
    			<Form className="basic-form">
    				<Row gutter={10}>
    					<Col span={6} className="basic-info-title required">
    						{applicationLang.modal("displayName")}：
    					</Col>
    					<Col span={18}>
    						<Input
    							type="text"
    							placeholder={applicationLang.modal("displayNamePlaceholder")} // 请输入配置名称
    							value={displayName || undefined}
    							onChange={(e) => {
    								this.changeFiledValue("displayName", "input", e);
    							}}
    							disabled={disabled}
    						/>
    					</Col>
    				</Row>
    				<Row gutter={10}>
    					<Col span={6} className="basic-info-title required">
    						{applicationLang.modal("identifier")}：
    					</Col>
    					<Col span={18}>
    						<Input
    							type="text"
    							placeholder={applicationLang.modal("identifierPlaceholder")} // 请输入配置名称
    							value={name || undefined}
    							onChange={(e) => {
    								this.changeFiledValue("name", "input", e);
    							}}
    							disabled={disabled}
    						/>
    					</Col>
    				</Row>
    				<Row gutter={10} style={{ height: "auto" }}>
    					<Col span={6} className="basic-info-title">
    						{applicationLang.modal("description")}：
    					</Col>
    					<Col span={18}>
    						<TextArea
    							placeholder={applicationLang.modal("descriptionPlaceholder")} // lang:请输入描述内容
    							rows={4}
    							value={description || undefined}
    							onChange={(e) => {
    								this.changeFiledValue("description", "input", e);
    							}}
    						/>
    					</Col>
    				</Row>
    			</Form>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	applicationStore: state.application
}))(OperatorApplicationModal);
