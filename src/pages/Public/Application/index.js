import { connect } from "dva";
import { <PERSON><PERSON>, message, Switch, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { QueryListScene, <PERSON>le } from "tntd";
import { applicationAPI } from "@/services";
import NoPermission from "@/components/NoPermission";
import { applicationLang, commonLang } from "@/constants/lang";
import OperatorApplicationModal from "./Modal/OperatorApplicationModal";

const { QueryList, QueryForm, Field, createActions } = QueryListScene;
const actions = createActions();
const confirm = Modal.confirm;

export default connect(state => ({
	globalStore: state.global,
	applicationStore: state.application
}))((props)=>{
	const { globalStore, dispatch } = props;
	const { personalMode } = globalStore;
	const lang = personalMode.lang === "cn" ? "cn" : "en";

	// 查询
	const query = (params = {}) => {
		const { current } = params;
		delete params.current;
		return applicationAPI.getApplicationList({
			...params,
			curPage: current || 1,
			pageSize: params.pageSize || 10
		})
			.then((data) => {
				data = data?.data;
				return {
					pageSize: Number(data?.pageSize),
					current: Number(data?.curPage),
					total: Number(data?.total),
					data: data?.contents || []
				};
			});
	};

	const createApp = () => {
    	dispatch({
    		type: "application/setAttrValue",
    		payload: {
    			dialogShow: {
    				addApplication: true
    			}
    		}
    	});
	};

	const deleteConfirm = (data) => {
    	confirm({
    		title: commonLang.getDeleteInfo(lang === "cn" ? data.displayName || data.enDisplayName : data.enDisplayName || data.displayName),
    		okText: applicationLang.common("submit"),
    		cancelText: applicationLang.common("cancel"),
    		onOk() {
    			const params = {
    				uuid: data.uuid
    			};
    			applicationAPI.deleteApplication(params).then(res => {
    				if (res.success) {
    					// dispatch({
    					// 	type: "application/getApplicationList"
    					// });
						actions.search({current: 1});
    					message.success(res.message);
    				} else {
    					message.error(res.message);
    				}
    			}).catch(err => {
    				console.log(err);
    			});
    		}
    	});
	};

	const editApplication = (item) => {
    	dispatch({
    		type: "application/setAttrValue",
    		payload: {
    			dialogShow: {
    				modifyApplication: true
    			}
    		}
    	});
    	dispatch({
    		type: "application/setAttrValue",
    		payload: {
    			dialogData: {
    				applicationData: {
    					uuid: item.uuid,
    					name: item.name,
    					displayName: item.displayName,
    					description: item.description
    				}
    			}
    		}
    	});
	};

	const changeStatus = (type, record) => {
    	const params = {
    		uuid: record.uuid
    	};
    	params[type] = !record[type];
    	applicationAPI.modifyApplication(params).then(res => {
    		if (res.success) {
    			// dispatch({
    			// 	type: "application/getApplicationList"
    			// });
				actions.search();
    			message.success(res.message);
    		} else {
    			message.error(res.message);
    		}
    	}).catch(error => {
    		console.log(error);
    	});
	};

	const columns = [
		{
			/* lang: 应用名 */
			title: applicationLang.table("appName"),
			key: "displayName",
			render: (record) => {
				return (
					<span>{lang === "cn" ? record["displayName"] || record["enDisplayName"] : record["enDisplayName"] || record["displayName"]}</span>
				);
			}
		},
		{
			/* lang: 应用标识 */
			title: applicationLang.table("appIdentifier"),
			dataIndex: "name",
			key: "name"
		},
		{
			/* lang: 过滤请求 */
			title: applicationLang.table("filterRequest"),
			dataIndex: "signForSwitch",
			key: "signForSwitch",
			render: (text, record, index) => {
				return (
					window.auth("QX0105", "modifyApplication")
						? <Tooltip placement="top" title={applicationLang.table("filterRequestInfo")}>
							<Switch
								checkedChildren={applicationLang.table("switchControl1")}
								unCheckedChildren={applicationLang.table("switchControl2")}
								key={index}
								checked={record.signForSwitch}
								onChange={() => {
									if (window.auth("QX0105", "modifyApplication")) {
										changeStatus("signForSwitch", record);
									} else {
										/* lang: 没有权限 */
										message.info(applicationLang.messageInfo("permissionMes"));
									}
								}}
							/>
						</Tooltip>
						: <Switch
							disabled={true}
							checkedChildren={applicationLang.table("switchControl1")}
							unCheckedChildren={applicationLang.table("switchControl2")}
							checked={record.signForSwitch}
							key={index}
						/>
				);
			}
		},
		{
			/* lang: 过滤数据 */
			title: applicationLang.table("filterData"),
			dataIndex: "signForWrite",
			key: "signForWrite",
			render: (text, record, index) => {
				return (
					window.auth("QX0105", "modifyApplication")
						? <Tooltip placement="top" title={applicationLang.table("filterDataInfo")}>
							<Switch
								checkedChildren={applicationLang.table("switchControl1")}
								unCheckedChildren={applicationLang.table("switchControl2")}
								key={index}
								checked={record.signForWrite}
								onChange={() => {
									if (window.auth("QX0105", "modifyApplication")) {
										changeStatus("signForWrite", record);
									} else {
										message.info(applicationLang.messageInfo("permissionMes"));
									}
								}}
							/>
						</Tooltip>
						: <Switch
							checkedChildren={applicationLang.table("switchControl1")}
							unCheckedChildren={applicationLang.table("switchControl2")}
							key={index}
							disabled={true}
							checked={record.signForWrite}
						/>
				);
			}
		},
		{
			/* lang: 操作 */
			title: applicationLang.table("operation"),
			dataIndex: "operation",
			key: "operation",
			width: 140,
			render: (text, record) => {
				return (
					<Handle>
						<a
							className={window.auth("QX0105", "modifyApplication") ? "operation" : "operation a-disabled"}
							onClick={() => {
								if (window.auth("QX0105", "modifyApplication")) {
									editApplication(record);
								} else {
									message.info(applicationLang.messageInfo("permissionMes"));
								}
							}}
						>
							{applicationLang.table("edit")}
						</a>
						<a
							className={window.auth("QX0105", "deleteApplication") ? "operation" : "operation a-disabled"}
							onClick={() => {
								if (window.auth("QX0105", "deleteApplication")) {
									deleteConfirm(record);
								} else {
									message.info(applicationLang.messageInfo("permissionMes"));
								}
							}}
						>
							{applicationLang.table("delete")}
						</a>
					</Handle>
				);
			}
		}
	];

	if (!window.auth("QX0105", "viewApplicationList")) {
		return <NoPermission/>;
	}
	return (
		<QueryListScene title={applicationLang.common("title")} query={query} actions={actions}>
			<QueryForm
				extralActions={
					window.auth("QX0105", "createApplication") &&
                    <Button
                    	type="primary"
                    	onClick={createApp}
                    >
                    	{/* lang:新增应用 */}
                    	{applicationLang.common("addApplication")}
                    </Button>
				}
			>
				<Field
					name="displayName"
					props={{
						placeholder: applicationLang.modal("displayNamePlaceholder"), // 请输入应用名称
						allowClear: true
					}}
				/>
				<Field
					name="name"
					props={{
						placeholder: applicationLang.modal("identifierPlaceholder"), // 请输入应用标识
						allowClear: true
					}}
				/>
			</QueryForm>

			<QueryList
				columns={columns}
				rowKey="uuid"
			/>

			<OperatorApplicationModal
				refresh={actions.search}
			/>

		</QueryListScene>
	);

});
