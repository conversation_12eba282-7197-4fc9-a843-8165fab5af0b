import { connect } from "dva";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>rm, message, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { QueryListScene, <PERSON>le, Ellipsis } from "tntd";
import NoPermission from "@/components/NoPermission";
import { dictionaryLang } from "@/constants/lang";
import moment from "moment";
import AddDictionary from "./Modal/AddDictionary";
import { dictionaryAPI } from "@/services";
import { isJSON } from "@/utils/isJSON";
import { initObj } from "@/constants/dicitionary";

const { QueryList, QueryForm, Field, createActions } = QueryListScene;
const actions = createActions();

export default connect(state => ({
	globalStore: state.global,
	dictionaryStore: state.dictionary
}))((props)=>{
	const { dispatch, dictionaryStore, globalStore } = props;
	const { personalMode = {} } = globalStore;
	const { lang } = personalMode || {};

	const { dialogShow, dialogData } = dictionaryStore;
	const { myValueData } = dialogData || {};
	const { addDict, modifyDict } = dialogShow;

	// 查询
	const query = (params = {}) => {
		const { current } = params;
		delete params.current;
		return dictionaryAPI.getDictionaryList({
			...params,
			curPage: current || 1,
			pageSize: params.pageSize || 10
		})
			.then((data) => {
				data = data?.data;
				return {
					pageSize: Number(data?.pageSize),
					current: Number(data?.curPage),
					total: Number(data?.total),
					data: data?.dictionaryList || []
				};
			});
	};

	// 修改查询表单
	const onFormChange = (values, changeInfo) => {
		if (["group"].includes(changeInfo.name)) {
			actions.search({
				...values,
				current: 1
			});
		}
	};

	// 增加字典配置
	const addDictHandle = () => {
    	dispatch({
    		type: "dictionary/setAttrValue",
    		payload: {
    			dialogShow: {
    				addDict: true
    			}
    		}
    	});
	};

	// 删除字典
	const deleteDictHandle = (data) => {
    	const params = {
    		uuid: data.uuid
    	};
    	dictionaryAPI.deleteDictionary(params).then(res => {
    		if (res.success) {
    			message.success(res.message);
				actions.search({
					current: 1
				});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(e => {
    		console.log(e);
    	});
	};

	// myValue格式转换
	const transferNormalMyValue = (myValue)=>{
    	let transferNormalMyValue = [];
    	if (typeof myValue === "string") {
    		try {
    			transferNormalMyValue = JSON.parse(myValue);
    		} catch (e) {
    			console.log(e);
    			message.error("不规范的数组json格式：" + e.message);
    			return;
    		}
    	}
    	if (!(transferNormalMyValue && transferNormalMyValue.length > 0)) {
    		transferNormalMyValue = [initObj];
    	}
    	return transferNormalMyValue;
	};

	// 修改字段
	const modifyDictHandle = (data) => {
    	dispatch({
    		type: "dictionary/setAttrValue",
    		payload: {
    			dialogShow: {
    				modifyDict: true
    			},
    			dialogData: {
    				dictData: {
    					uuid: data.uuid,
    					myKey: data.myKey,
    					myValue: data.myValue,
    					normalMyValue: transferNormalMyValue(data.myValue),
    					groups: data.groups || [],
    					description: data.description
    				}
    			}
    		}
    	});
	};

	// 查看
	const viewMyValueDetail = (record) => {
    	dispatch({
    		type: "dictionary/setAttrValue",
    		payload: {
    			dialogShow: {
    				myValueDialog: true
    			},
    			dialogData: {
    				myValueData: {
    					myValue: record.myValue
    				}
    			}
    		}
    	});
	};

	const columns = [
		{
			title: dictionaryLang.tableSet("group"), // 字典分组
			dataIndex: "groups",
			key: "groups",
			width: 130,
			render: (groups) => {
				const groupMap = {
					system: dictionaryLang.formSet("system"), // 系统
					auditForm: dictionaryLang.formSet("auditForm")// 表单
				};
				const groupNames = [];
				if (groups) {
					groups.forEach((group) => {
						if (groupMap[group]) {
							groupNames.push(groupMap[group]);
						}
					});
				}
				return (
					groupNames.join(lang !== "cn" ? "," : "，")
				);
			}
		},
		{
			title: dictionaryLang.tableSet("dictionaryId"), // 字典标识
			dataIndex: "myKey",
			key: "myKey",
			ellipsis: true,
			width: 160,
			render: (text)=>{
				return (
					<Ellipsis
						copyable
						title={text}
					/>
				);
			}
		},
		{
			title: dictionaryLang.tableSet("value"), // 字典详情
			dataIndex: "myValue",
			key: "myValue",
			ellipsis: true,
			render: (text, record) => {
				let tempText = text ? text.replace(/\s+/g, "") : ""; // 去掉空格
				tempText = tempText ? tempText.replace(/[\{\[\]\}]/g, "") : ""; // 去掉[]{}
				let textArr = tempText.split(",");
				return (
				/* lang: 点击查看详情*/
					<Tooltip
						placement="top"
						title={dictionaryLang.tipInfo("tip1")}
					>
						<a
							// className="text-overflow"
							onClick={() => {
								viewMyValueDetail(record);
							}}
							// style={{
							// 	display: "inline-block",
							// 	maxWidth: "200px"
							// }}
						>
							{textArr[0]}
						</a>
					</Tooltip>
				);
			}
		},
		{
			title: dictionaryLang.tableSet("description"), // 描述
			dataIndex: "description",
			key: "description",
			ellipsis: true,
			render: (text) => {
				if (text) {
					/* lang: 描述详情*/
					return (
						<Popover
							placement="top"
							title={dictionaryLang.tipInfo("tip2")}
							content={
								<div style={{ maxWidth: 350, maxHeight: 200, overflow: "scroll" }}>{text}</div>
							}
							trigger="hover"
						>
							<span>{text}</span>
						</Popover>
					);
				} else {
					return "--";
				}
			}
		},
		{
			title: dictionaryLang.tableSet("modifier"), // 修改人
			dataIndex: "updatedBy",
			key: "updatedBy",
			width: 120
		},
		{
			title: dictionaryLang.tableSet("modifyTime"), // 修改时间
			dataIndex: "gmtModified",
			key: "gmtModified",
			render: (text)=>{
				return moment(text).format("YYYY-MM-DD HH:mm:ss");
			}
		},
		{
			title: dictionaryLang.tableSet("operation"), // 操作
			dataIndex: "operation",
			key: "operation",
			fixed: "right",
			width: 140,
			render: (text, record) => {
				const { defaultFlag } = record || {};
				return (
					<Handle>
						{/* lang: 编辑 */}
						<a
							className={window.auth("QX0401", "modifyDictionary") ? "" : "a-disabled"}
							onClick={() => {
								if (window.auth("QX0401", "modifyDictionary")) {
									modifyDictHandle(record);
								}
							}}
						>
							{dictionaryLang.tableSet("modify")}
						</a>
						{/* lang: 确定删除当前字典配置吗？ */}
						{
							!defaultFlag &&
                            (
                            	window.auth("QX0401", "deleteDictionary")
                            		? <Popconfirm
                            			placement="topRight"
                            			title={dictionaryLang.tipInfo("tip3")}
                            			onConfirm={() => {
                            				deleteDictHandle(record);
                            			}}
                            			onCancel={() => {
                            			}}>
                            			{/* lang: 删除 */}
                            			<a>{dictionaryLang.tableSet("delete")}</a>
                            		</Popconfirm>
                            		: <a className="a-disabled">{dictionaryLang.tableSet("delete")}</a>
                            )
						}
					</Handle>
				);
			}
		}
	];

	if (!window.auth("QX0401", "viewDictionary")) {
		return <NoPermission/>;
	}
	return (
		<QueryListScene title={dictionaryLang.common("title")} query={query} actions={actions}>
			<QueryForm
				onChange={onFormChange}
				extralActions={
					window.auth("QX0401", "addDictionary") &&
                    <Button
                    	type="primary"
                    	onClick={addDictHandle}
                    >新增</Button>
				}
			>
				<Field
					name="group"
					type="select"
					props={{
						allowClear: true,
						placeholder: dictionaryLang.formSet("placeholderGroup"),
						options: [{
							label: dictionaryLang.formSet("all"),
							value: ""
						}, {
							label: dictionaryLang.formSet("system"),
							value: "system"
						}, {
							label: dictionaryLang.formSet("auditForm"),
							value: "auditForm"
						}]
					}}
				/>
				<Field
					name="myKey"
					props={{
						placeholder: dictionaryLang.formSet("placeholderId") // 请输入字典标识
					}}
				/>
				<Field
					name="myValue"
					props={{
						placeholder: dictionaryLang.formSet("placeholderInfo") // 请输入字典详情
					}}
				/>
				<Field
					name="description"
					props={{
						placeholder: dictionaryLang.formSet("placeholderDes") // 请输入字典描述
					}}
				/>
			</QueryForm>

			<QueryList
				columns={columns}
				rowKey="uuid"
				scroll={{
					x: 1100
				}}
			/>

			<Modal
				width="600px"
				title={dictionaryLang.valueDialog("title")}
				footer={null}
				visible={dialogShow.myValueDialog}
				className="pre-code-modal"
				onCancel={() => {
					dispatch({
						type: "dictionary/setAttrValue",
						payload: {
							dialogShow: {
								myValueDialog: false
							}
						}
					});
					setTimeout(() => {
						dispatch({
							type: "dictionary/setAttrValue",
							payload: {
								dialogData: {
									myValueData: {
										myValue: null
									}
								}
							}
						});
					}, 300);
				}}
			>
                <>
                    {/* lang: 字典配置值说明 */}
                    <Alert
                    	message={dictionaryLang.valueDialog("alertInfo")}
                    	description={dictionaryLang.valueDialog("alertInfoContent")}
                    	type="info"
                    	style={{ marginBottom: "10px" }}
                    />
                    <pre className="pre-code-view">{isJSON(myValueData.myValue) ? JSON.stringify(JSON.parse(myValueData.myValue), null, 4) : myValueData.myValue}</pre>
                </>
			</Modal>
			{
				(addDict || modifyDict) &&
                <AddDictionary
                	refreshList={actions.search}
                />
			}
		</QueryListScene>
	);
});
