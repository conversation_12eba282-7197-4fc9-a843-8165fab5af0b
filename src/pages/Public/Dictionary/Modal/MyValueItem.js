import { PureComponent } from "react";
import { findDOMNode } from "react-dom";
import { Input, Row, Col, Icon } from "antd";

import {
	DragSource,
	DropTarget,
	ConnectDropTarget,
	ConnectDragSource,
	DropTargetMonitor,
	DropTargetConnector,
	DragSourceConnector,
	DragSourceMonitor
} from "react-dnd";

const style = {
    cursor: 'move',
    borderBottom:"1px dashed #ededed"
}

const cardSource = {
	beginDrag(props) {
		return {
			id: props.id,
			index: props.index,
		}
	},
}

const cardTarget = {
	hover(props, monitor, component) {
		if (!component) {
			return null
		}
		const dragIndex = monitor.getItem().index
		const hoverIndex = props.index

		// Don't replace items with themselves
		if (dragIndex === hoverIndex) {
			return
		}

		// Determine rectangle on screen
		const hoverBoundingRect = (findDOMNode(
			component,
		)).getBoundingClientRect()

		// Get vertical middle
		const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2

		// Determine mouse position
		const clientOffset = monitor.getClientOffset()

		// Get pixels to the top
		const hoverClientY = (clientOffset).y - hoverBoundingRect.top

		// Only perform the move when the mouse has crossed half of the items height
		// When dragging downwards, only move when the cursor is below 50%
		// When dragging upwards, only move when the cursor is above 50%

		// Dragging downwards
		if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
			return
		}

		// Dragging upwards
		if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
			return
		}

		// Time to actually perform the action
		props.moveMyValue(dragIndex, hoverIndex)

		// Note: we're mutating the monitor item here!
		// Generally it's better to avoid mutations,
		// but it's good here for the sake of performance
		// to avoid expensive index searches.
		monitor.getItem().index = hoverIndex
	},
}

@DropTarget("card", cardTarget, (connect, monitor) => ({
	connectDropTarget: connect.dropTarget(),
}))
@DragSource(
	"card",
	cardSource,
	(connect, monitor) => ({
		connectDragSource: connect.dragSource(),
		isDragging: monitor.isDragging(),
	})
)
export default class MyValueItem extends PureComponent {
    render(){
        const {
            myValue,
            myValueListLen,
            infoSpan,
            changeMyValue,
            modifyDictionary,
            showEn,
            index,
            isDragging,
            connectDragSource,
            connectDropTarget,
        } = this.props
        const opacity = isDragging ? 0 : 1
        return (
            connectDragSource &&
            connectDropTarget &&
            connectDragSource(
                connectDropTarget(
                    <div style={{ ...style, opacity }}>
                       <Row gutter={16}>
                            <Col span={infoSpan}>
                                <Input
                                    placeholder="请输入标识值"
                                    value={myValue.name || ""}
                                    onChange={(e)=>changeMyValue(e, index, "name")}
                                />
                            </Col>
                            <Col span={infoSpan}>
                                <Input
                                    placeholder="请输入显示名称"
                                    value={myValue.dName || ""}
                                    onChange={(e)=>changeMyValue(e, index, "dName")}
                                />
                            </Col>
                            {
                                showEn &&
                                <Col span={infoSpan}>
                                    <Input
                                        placeholder="请输入英文显示名称"
                                        value={myValue.enDName || ""}
                                        onChange={(e)=>changeMyValue(e, index, "enDName")}
                                    />
                                </Col>
                            }
                            <Col span={3} className="tr">
                                {
                                    (myValueListLen - 1) === index &&
                                    <Icon type="plus-circle" onClick={(e)=>modifyDictionary("add",e)}/>
                                }
                                {
                                    myValueListLen !== 1 &&
                                    <Icon type="delete" onClick={(e)=>modifyDictionary("remove", index,e)}/>
                                }
                            </Col>
                        </Row>
                    </div>
                ),
            )
        )
    }
};

