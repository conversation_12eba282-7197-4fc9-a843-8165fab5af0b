import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, Form, message, Select, Row, Col, Icon, Checkbox } from "antd";
import { dictionaryAPI } from "@/services";
import { dictionaryLang } from "@/constants/lang";
import AceEditor from "react-ace";
import { isJSON } from "@/utils/isJSON";
import { supportKey, initObj } from "@/constants/dicitionary";
import MyValueList from "./MyValueList";
import "./AddDictionary.less";

const FormItem = Form.Item;
const TextArea = Input.TextArea;
const Option = Select.Option;

class AddDictionary extends PureComponent {
	constructor(props) {
    	super(props);
    	const { dictionaryStore } = props;
    	const { dialogData, dialogShow } = dictionaryStore;
    	let { modifyDict } = dialogShow;
    	const { normalMyValue } = dialogData.dictData;
		let hasEnName = true;
		// 编辑的场景筛选是否有英文名称，有则默认勾选显示英文名称
    	if (modifyDict) {
    		normalMyValue && normalMyValue.length > 0 && normalMyValue.map((v)=>{
    			if (!v.enDName) {
    				hasEnName = false;
    			}
    		});
		}
		const showEn = modifyDict ? hasEnName : false;
		this.state = {
			standMode: true, // 标准模式
			showEn, // 显示英文显示名称
			enNameTemp: {} // 暂存英文名称
		};
	}
	componentWillMount() {
		const { showEn } = this.state;
		this.changeEnName(showEn);// 初始化
	}
    // 提交时验证字典详情
    checkJsonArray = (parseMyValue) => {
    	const { showEn } = this.state;
    	let hasNullArrJson = false; // 判断字典详情中是否存在未填写字段
    	let hasEnName = []; // 有英文名 判断英文名
    	parseMyValue && parseMyValue.length > 0 && parseMyValue.map((v)=>{
    		// json对象中存在值，，如果没有name或者dName的情况
    		if (Object.keys(v).length > 0) {
    			if (!v.name || !v.dName) {
    				hasNullArrJson = true;
    			}
    			// 如果展示英文显示名称
    			if (showEn && v.enDName) {
    				hasEnName.push(v);
    			}
    		}
    	});
    	if (parseMyValue && parseMyValue.length < 1) {
    		/* lang: 请填写完整的数组JSON格式的数据 */
    		message.warning(dictionaryLang.message("completeJson"));
    		return false;
    	}
    	if (hasNullArrJson) {
    		/* lang: 请填写完整的数组JSON格式的数据,包含键值name,dName */
    		message.warning(dictionaryLang.message("completeJson"));
    		return false;
    	}

    	if (showEn && (hasEnName.length !== parseMyValue.length) && hasEnName.length > 0) {
    		/* lang: 请填写完整的数组JSON格式的数据,存在部分英文显示名称缺失 */
    		message.warning(dictionaryLang.message("completeEnJson"));
    		return false;
    	}

    	return true;
    }
    submitModal = () => {
    	const transferRes = this.transferData(); // 数据格式转换 键值，类型为数组等基础校验
    	if (!transferRes) {
    		return ;
    	}
    	let { dictionaryStore } = this.props;
    	let { dialogShow, dialogData } = dictionaryStore;
    	let { addDict, modifyDict } = dialogShow;
    	let { uuid, myKey, myValue, groups, description } = {...dialogData.dictData};
    	if (!myKey || myKey === "" || !myValue || myValue === "") {
    		/* lang: 请填写必填项 */
    		message.warning(dictionaryLang.message("fillRequired"));
    		return;
    	}
    	if (myKey && myKey.length > 32) {
    		/* lang: 最长32个汉字(字符)长度 */
    		message.warning(dictionaryLang.message("max32"));
    		return;
    	}

    	const checkJson = this.checkJsonArray(JSON.parse(myValue)); // 判断是否存在空值
    	if (!checkJson) {
    		return false;
    	}

    	if (!groups || groups.length === 0) {
    		/* lang: 请选择分组 */
    		message.warning(dictionaryLang.message("selectGroup"));
    		return;
    	}

    	if (description && description.length > 200) {
    		/* lang: 描述不能超过200字 */
    		message.warning(dictionaryLang.message("max200"));
    		return;
    	}

    	const { showEn } = this.state;
    	if (!showEn) {
    		myValue = JSON.parse(myValue);
    		console.log(myValue);
    		myValue = [...myValue].map(v=>{
    			delete v.enDName;
    			return v;
    		});
    		myValue = JSON.stringify(myValue);
    	}

    	// 去除换行
    	// myValue = myValue.replace(/[\r\n]/g, "");
    	// 去除空格
    	// myValue = myValue.replace(/\s*/g, "");
    	if (addDict) {
    		let params = {
    			myKey,
    			myValue,
    			groups: JSON.stringify(groups),
    			description
    		};
    		dictionaryAPI.addDictionary(params).then(res => {
    			if (res.success) {
    				message.success(res.message);
    				this.closeModal();
    			} else {
    				message.error(res.message);
    			}
    		}).catch(e => {
    			console.log(e);
    		});
    	}
    	if (modifyDict) {
    		const params = {
    			uuid,
    			myValue,
    			groups: JSON.stringify(groups),
    			description
    		};
    		dictionaryAPI.modifyDictionary(params).then(res => {
    			if (res.success) {
    				message.success(res.message);
    				// 关闭弹窗
    				this.closeModal();
    			} else {
    				message.error(res.message);
    			}
    		}).catch(e => {
    			console.log(e);
    		});
    	}
    }

    closeModal = () => {
    	const { dispatch, refreshList } = this.props;
    	// 刷新列表
    	// dispatch({
    	// 	type: "dictionary/getDictionaryList"
    	// });
    	refreshList && refreshList({current: 1});
    	dispatch({
    		type: "dictionary/setAttrValue",
    		payload: {
    			dialogShow: {
    				addDict: false,
    				modifyDict: false
    			}
    		}
    	});
    	// 延迟300毫秒清除数据
    	setTimeout(() => {
    		dispatch({
    			type: "dictionary/setAttrValue",
    			payload: {
    				dialogData: {
    					dictData: {
    						uuid: null,
    						myKey: null,
    						myValue: null,
    						normalMyValue: [initObj],
    						groups: [],
    						description: null
    					}
    				}
    			}
    		});
    	}, 300);
    }

    changeFiled = (field, type, e) => {
    	const { dictionaryStore, dispatch } = this.props;
    	const { dictData } = dictionaryStore.dialogData;
    	if (type === "aceEditor") {
    		dictData[field] = e;
    	} else if (type === "select") {
    		dictData[field] = e;
    	} else {
    		dictData[field] = e.target.value;
    	}
    	dispatch({
    		type: "dictionary/setAttrValue",
    		payload: {
    			dialogData: {
    				dictData
    			}
    		}
    	});
    }

    modifyDictionary = (type, index) => {
    	const { dictionaryStore } = this.props;
    	const { dialogData } = dictionaryStore;
    	let { normalMyValue: normalMyValueOld } = dialogData.dictData;
    	let normalMyValue = [...normalMyValueOld];
    	if (type === "add") {
    		normalMyValue.push({...initObj});
    	} else {
    		normalMyValue.splice(index, 1);
    	};
    	this.changeFiled("normalMyValue", "select", normalMyValue);
    }

    changeMyValue = (e, index, field) => {
    	const { dictionaryStore } = this.props;
    	const { dialogData } = dictionaryStore;
    	let { normalMyValue: normalMyValueOld } = dialogData.dictData;
    	let normalMyValue = [...normalMyValueOld];
    	const obj = {...normalMyValue[index]};
    	const {value} = e.target;
    	obj[field] = value;
    	normalMyValue[index] = obj;
    	this.changeFiled("normalMyValue", "select", normalMyValue);
    }

    transferData = () => {
    	const { standMode } = this.state;
    	const { dictionaryStore } = this.props;
    	const { dialogData } = dictionaryStore;
    	let { normalMyValue, myValue } = dialogData.dictData;
    	let transferMyValue = "";
    	let transferNormalMyValue = [];
    	// 标准转json
    	if (standMode) {
    		transferMyValue = normalMyValue.filter((v)=>{
    			return JSON.stringify(v) !== "{}";
    		});
    		if (transferMyValue.length === 0) {
    			transferMyValue = undefined;
    		}
    		transferMyValue = JSON.stringify(transferMyValue);
    		this.changeFiled("myValue", "select", transferMyValue);
    	} else { // json转标准
    		let errJson = false;
    		if (typeof myValue === "string") {
    			try {
    				transferNormalMyValue = JSON.parse(myValue);
    			} catch (e) {
    				console.log(e);
    				message.error("不规范的数组json格式：" + e.message);
    				errJson = true;
    			}
    		}
    		if (errJson) {
    			return false;
    		}
    		if (transferNormalMyValue && !Array.isArray(transferNormalMyValue)) {
    			/* lang: 字典详情请填写数组JSON格式的数据 */
    		    message.warning(dictionaryLang.message("arrayFormat"));
    			return false;
    		}

    		let keyNotContain = ""; // 键值不匹配
    		transferNormalMyValue && transferNormalMyValue.length > 0 && transferNormalMyValue.map((v)=>{
    			// json对象中存在值，，如果没有name或者dName的情况
    			if (Object.keys(v).length > 0) {
    				Object.keys(v).map(key=>{
    					if (!supportKey.includes(key) && key && !keyNotContain) {
    						keyNotContain = key;
    					}
    				});
    			}
    		});
    		if (keyNotContain) {
    			/* lang: json键值只支持name，dName，enDName */
    			message.warning(dictionaryLang.message("currentKey") + keyNotContain + "。" + dictionaryLang.message("jsonKey"));
    			return false;
    		}

    		if (!(transferNormalMyValue && transferNormalMyValue.length > 0)) {
    			transferNormalMyValue = [{}];
    		}

    		this.changeFiled("normalMyValue", "select", transferNormalMyValue);
    	}
    	return true;
    }
    changeMode = () => {
    	const { standMode, showEn } = this.state;
    	const checkJsonResult = this.transferData();
    	const { dictionaryStore } = this.props;
    	const { dialogData } = dictionaryStore;
    	const { myValue } = dialogData.dictData;
    	if (checkJsonResult) {
    		this.setState({
    			standMode: !standMode
    		});
    		// 匹配字符串中是否有enDName
    		if (!standMode) {
    			if (String(myValue).indexOf("enDName") > -1) {
    				if (!showEn) {
    					this.setState({
    						showEn: true
    					});
    				}
    			} else {
    				if (showEn) {
    					this.setState({
    						showEn: false
    					});
    				}
    			}
    		}
    	}
    }
    changeEnName = (showEn, cb)=>{
    	const { enNameTemp = {} } = this.state;
    	const { dictionaryStore } = this.props;
    	const { dialogData } = dictionaryStore;
    	const { normalMyValue } = dialogData.dictData;
    	// 标准模式
    	const newNormalMyValue = [...normalMyValue];
    	if (!showEn) {
    	    // 将英文名称记录到enNameTemp中在勾选中可以自动填充上一次的enName
    	    const enNameTempCurrent = {};
    		newNormalMyValue.map((v)=>{
    			if (v.enDName) {
    				enNameTempCurrent[v.name] = v.enDName;
    			}
    			if (v.hasOwnProperty("enDName")) {
    				delete v.enDName;
    			}
    			return v;
    		});
    		this.setState({
    			enNameTemp: enNameTempCurrent
    		});
    	} else {
    		newNormalMyValue.map((v)=>{
    			if (!v.hasOwnProperty("enDName")) {
    				v.enDName = enNameTemp[v.name] || "";
    			}
    			return v;
    		});
    	}
    	this.changeFiled("normalMyValue", "select", newNormalMyValue);
    	cb && cb();
    }
    render() {
    	const { dictionaryStore } = this.props;
    	const { dialogShow, dialogData } = dictionaryStore;
    	const { addDict, modifyDict } = dialogShow;
    	let { myKey, myValue, normalMyValue, groups, description } = dialogData.dictData;
    	const { standMode, showEn } = this.state;
    	let formItemLayout = {
    		labelCol: { span: 4 },
    		wrapperCol: { span: 19 }
    	};
    	const infoSpan = showEn ? 7 : 10;
    	return (
    		<Modal
    			title={modifyDict ? dictionaryLang.addDict("title1") : dictionaryLang.addDict("title")}
    			visible={modifyDict || addDict}
    			maskClosable={false}
    			width={800}
    			onCancel={this.closeModal}
    			onOk={this.submitModal}
    		>
    			<Form layout="horizontal">
    				{/* lang: 字典分组 */}
    				<FormItem
    					label={dictionaryLang.addDict("dictionaryGroup")}
    					{...formItemLayout}
    				>
    					<Select
    						mode="multiple"
    						value={groups || undefined}
    						placeholder={dictionaryLang.addDict("dictionaryGroupPlaceholder")}
    						onChange={(value) => {
    							this.changeFiled("groups", "select", value);
    						}}
    					>
    						{/* 系统 */}
    						<Option value="system">{dictionaryLang.formSet("system")} </Option>
    						{/* 表单 */}
    						<Option value="auditForm">{dictionaryLang.formSet("auditForm")} </Option>
    					</Select>
    					<span className="require">*</span>
    				</FormItem>
    				{/* lang: 字典标识 */}
    				<FormItem
    					label={dictionaryLang.addDict("dictionaryId")}
    					{...formItemLayout}
    				>
    					<Input
    						disabled={modifyDict}
    						value={myKey || undefined}
    						onChange={(e) => {
    							this.changeFiled("myKey", "input", e);
    						}}
    						placeholder={dictionaryLang.addDict("dictionaryIdPlaceholder")}
    					/>
    					<span className="require">*</span>
    				</FormItem>
    				{/* lang: 配置值 */}
    				<FormItem
    					label={dictionaryLang.addDict("value")}
    					{...formItemLayout}
    				>
    					<Row className="tr swap-wrap" align="justify-between">
    						{
    							standMode &&
                                <Checkbox
                                	className="fl"
                                	onChange={(e)=>{
                                		this.changeEnName(e.target.checked, ()=>{
                                			this.setState({showEn: e.target.checked});
                                		});
                                	}}
                                	checked={showEn}
                                >
                                	{/* 英文显示名称 */}
                                	{dictionaryLang.addDict("showEnName")}
                                </Checkbox>
    						}
    						<div>
    							{/* "标准模式" : "json模式" */}
    							{
    								standMode ? dictionaryLang.addDict("standMode") : dictionaryLang.addDict("jsonMode")
    							}
    							<Icon type="swap" onClick={()=>{this.changeMode();}}/>
    						</div>
    					</Row>
    					{/* lang: 请填写JSON格式的数据 */}
    					{
    						standMode &&
                            <div className="obj-enter">
                            	<Row gutter={16} className="line-hei-28">
                            		<Col span={infoSpan}>标识值</Col>
                            		<Col span={infoSpan}>显示名称</Col>
                            		{
                            			showEn &&
                                        <Col span={infoSpan}>英文显示名称</Col>
                            		}
                            	</Row>
                            	<MyValueList
                            		normalMyValue={normalMyValue}
                            		infoSpan={infoSpan}
                            		changeFiled={this.changeFiled.bind(this)}
                            		changeMyValue={this.changeMyValue.bind(this)}
                            		modifyDictionary={this.modifyDictionary.bind(this)}
                            		showEn={showEn}
                            	/>
                            </div>
    					}
    					{
    						!standMode &&
                            	<AceEditor
                            		placeholder={dictionaryLang.addDict("valuePlaceholder")}
                            		mode="json"
                            		theme="textmate"
                            		name="myValue"
                            		onChange={(e) => {
                            			this.changeFiled("myValue", "aceEditor", e);
                            		}}
                            		fontSize={14}
                            		height="200px"
                            		width="100%"
                            		style={{
                            			border: "1px solid #D9D9D9",
                            			borderRadius: 5,
                            			display: "inline-block"
                            		}}
                            		editorProps={{ $blockScrolling: true }}
                            		showPrintMargin={true}
                            		showGutter={true}
                            		highlightActiveLine={true}
                            		value={myValue ? (isJSON(myValue) ? JSON.stringify(JSON.parse(myValue), null, 4) : myValue) : undefined}
                            		enableSnippets={false}
                            		setOptions={{
                            			showLineNumbers: true,
                            			tabSize: 2
                            		}}
                            	/>
    					}

    					<span className="require v-c">*</span>
    				</FormItem>

    				{/* lang: 描述 */}
    				<FormItem
    					label={dictionaryLang.addDict("description")}
    					{...formItemLayout}
    				>
    					<TextArea
    						value={description || undefined}
    						onChange={(e) => {
    							this.changeFiled("description", "textArea", e);
    						}}
    						placeholder={dictionaryLang.addDict("descriptionPlaceholder")}
    						rows={4}
    					/>
    				</FormItem>
    			</Form>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	dictionaryStore: state.dictionary
}))(AddDictionary);
