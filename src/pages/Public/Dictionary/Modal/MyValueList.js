import { PureComponent } from "react";
import { DragDropContext } from 'react-dnd'
import HTML5Backend from 'react-dnd-html5-backend'
import MyValueItem from "./MyValueItem";

@DragDropContext(HTML5Backend)
export default class MyValueList extends PureComponent {
	moveMyValue = (dragIndex, hoverIndex) => {
        const { normalMyValue:oldNormalMyValue, changeFiled } = this.props;
        const normalMyValue = [...oldNormalMyValue];

        const dragMyValue = normalMyValue[dragIndex];
        const hoverMyValue = normalMyValue[hoverIndex];

        normalMyValue[hoverIndex] = dragMyValue;
        normalMyValue[dragIndex] = hoverMyValue;

        changeFiled("normalMyValue", "select", normalMyValue);
	};
	render() {
		const { normalMyValue, infoSpan, changeMyValue, modifyDictionary, showEn } = this.props;
		return (
			<div>
                {
                    normalMyValue.map((v, i) => (
                        <MyValueItem
                            key={i}
                            index={i}
                            id={i}
                            moveMyValue={this.moveMyValue}
                            myValue={v}
                            myValueListLen={normalMyValue && normalMyValue.length}
                            infoSpan={infoSpan}
                            changeMyValue={changeMyValue}
                            modifyDictionary={modifyDictionary}
                            showEn={showEn}
                        />
                    ))
                }
			</div>
		);
	}
};
