import { connect } from "dva";
import { Tooltip, Tag, Popover } from "antd";
import { QueryListScene, Handle } from "tntd";
import NoPermission from "@/components/NoPermission";
import { systemConfigLang } from "@/constants/lang";
import { systemConfigAPI } from "@/services";
import OperatorConfigModal from "./Modal/OperatorConfigModal";
import ValueDetailModal from "./Modal/ValueDetailModal";

const { QueryList, QueryForm, Field, createActions } = QueryListScene;
const actions = createActions();

const typeMap = {
	"String": {
		name: "String",
		dName: "String",
		color: "blue" // "#2db7f5"
	},
	"Boolean": {
		name: "Boolean",
		dName: "Boolean",
		color: "green" // "#87d068"
	},
	"Json": {
		name: "<PERSON><PERSON>",
		dName: "Json",
		color: "orange" // "#108ee9"
	}
};

export default connect(state => ({
	globalStore: state.global,
	systemConfigStore: state.systemConfig
}))((props)=>{
	const { dispatch, globalStore } = props;
	const { personalMode = {} } = globalStore;
	const lang = personalMode || {};

	// 查询
	const query = (params = {}) => {
		const { current } = params;
		delete params.current;
		return systemConfigAPI.getConfigList({
			...params,
			curPage: current || 1,
			pageSize: params.pageSize || 10
		})
			.then((data) => {
				data = data?.data;
				return {
					pageSize: Number(data?.pageSize),
					current: Number(data?.curPage),
					total: Number(data?.total),
					data: data?.configList || []
				};
			});
	};

	// 修改查询表单
	const onFormChange = (values, changeInfo) => {
		if (["type"].includes(changeInfo.name)) {
			actions.search({
				...values,
				current: 1
			});
		}
	};

	// 编辑
	const modifyConfigHandle = (item) => {
    	dispatch({
    		type: "systemConfig/setAttrValue",
    		payload: {
    			dialogShow: {
    				addConfig: false,
    				modifyConfig: true
    			},
    			dialogData: {
    				configData: {
    					uuid: item.uuid,
    					configKey: item.configKey,
    					configValue: item.configValue,
    					description: item.description,
    					type: item.type,
    					status: item.status
    				}
    			}
    		}
    	});
	};

	// 查看
	const viewValueDetail = (record) => {
    	dispatch({
    		type: "systemConfig/setAttrValue",
    		payload: {
    			dialogShow: {
    				valueDetail: true
    			},
    			dialogData: {
    				valueDetailData: record.configValue
    			}
    		}
    	});
	};

	const columns = [
		{
			title: systemConfigLang.table("configName"), // 配置名称
			dataIndex: "configKey",
			key: "configKey",
			width: 210,
			render: (text) => {
				return (
					<div>
						{text}
					</div>
				);
			}
		},
		{
			title: systemConfigLang.table("configType"), // 配置类型
			dataIndex: "configType",
			key: "configType",
			width: 120,
			render: (text, record) => {
				return (
					<Tag
						color={typeMap[record.type] ? typeMap[record.type].color : null}
						style={{ width: "60px", textAlign: "center" }}
					>
						{record.type}
					</Tag>
				);
			}
		},
		{
			title: systemConfigLang.table("configValue"), // 配置详情
			dataIndex: "configValue",
			key: "configValue",
			ellipsis: true,
			width: 180,
			render: (text, record) => {
				let tempText = text ? text.replace(/\s+/g, "") : ""; // 去掉空格
				tempText = tempText ? tempText.replace(/[\{\[\]\}]/g, "") : ""; // 去掉[]{}
				let textArr = tempText.split(",");
				return (
					<>
						{
							record.type === "Json" &&
                            <Tooltip
                            	placement="top"
                            	title={systemConfigLang.tip("clickViewConfigDetail")} // lang:点击查看详情
                            >
                            	<a
                            		// className={window.auth("QX0402", "viewSystemConfigDetail") ? "" : "a-disabled"}
                            		onClick={() => {
                            			// if (window.auth("QX0402", "viewSystemConfigDetail")) {
                            			viewValueDetail(record);
                            			// }
                            		}}
                            	>
                            		{textArr[0]}
                            	</a>
                            </Tooltip>
						}
						{
							record.type !== "Json" &&
                            (text
                            	? <Popover
                            		content={
                            			<div style={{
                            				"maxWidth": "300px",
                            				"whiteSpace": "wrap",
                            				"maxHeight": "400px",
                            				"overflow": "scroll"
                            			}}>{text}</div>
                            		}
                            		title={systemConfigLang.tip("configDetail")} // lang:配置详情
                            	>
                            		{text}
                            	</Popover>
                            	: "--"
                            )
						}
					</>
				);
			}
		},
		{
			title: systemConfigLang.table("description"), // 配置说明
			dataIndex: "description",
			ellipsis: true,
			key: "description",
			render: (text)=>{
				return (
					text
						? <Popover
							content={
								<div style={{
									"maxWidth": "300px",
									"whiteSpace": "wrap",
									"maxHeight": "400px",
									"overflow": "scroll"
								}}>{text}</div>
							}
							title={systemConfigLang.table("description")} // lang:配置详情
						>
							{text}
						</Popover> : "--"
				);
			}
		},
		{
			title: systemConfigLang.table("operator"), // 操作
			dataIndex: "operation",
			key: "operation",
			width: 100,
			render: (text, record) => {
				return (
					<Handle>
						<a
							className={window.auth("QX0402", "modifySystemConfig") ? "" : "a-disabled"}
							onClick={() => {
								if (window.auth("QX0402", "modifySystemConfig")) {
									modifyConfigHandle(record);
								}
							}}>
							{/* lang:编辑 */}
							{systemConfigLang.table("edit")}
						</a>
					</Handle>
				);
			}
		}
	];

	if (!window.auth("QX0402", "getSystemConfigList")) {
		return <NoPermission/>;
	}

	return (
		<QueryListScene title={systemConfigLang.common("pageTitle")} query={query} actions={actions}>
			<QueryForm
				onChange={onFormChange}
			>
				<Field
					name="configKey"
					props={{
						placeholder: systemConfigLang.formSet("configKey"), // 请输入配置名称
						allowClear: true
					}}
				/>
				<Field
					name="type"
					type="select"
					props={{
						allowClear: true,
						placeholder: systemConfigLang.formSet("type"), // 请选择配置类型
						options: Object.values(typeMap).map(v=>({label: lang === "cn" ? v.dName : v.name, value: v.name}))
					}}
				/>
				<Field
					name="configValue"
					props={{
						placeholder: systemConfigLang.formSet("configValue"), // 请输入配置详情
						allowClear: true
					}}
				/>
				<Field
					name="description"
					props={{
						placeholder: systemConfigLang.formSet("description"), // 请输入配置说明
						allowClear: true
					}}
				/>
			</QueryForm>

			<QueryList
				columns={columns}
				rowKey="uuid"
			/>

			<OperatorConfigModal
				refresh={actions.search}
			/>

			<ValueDetailModal />

		</QueryListScene>
	);
});
