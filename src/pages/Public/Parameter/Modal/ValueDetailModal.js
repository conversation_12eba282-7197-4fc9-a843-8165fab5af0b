import { PureComponent } from "react";
import { connect } from "dva";
import { Modal } from "antd";
import { systemConfigLang } from "@/constants/lang";
import { isJSON } from "@/utils/isJSON";

class ValueDetailModal extends PureComponent {

	constructor(props) {
		super(props);
	}

    closeModal = () => {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "systemConfig/setAttrValue",
    		payload: {
    			dialogShow: {
    				valueDetail: false
    			}
    		}
    	});
    	setTimeout(() => {
    		dispatch({
    			type: "systemConfig/setAttrValue",
    			payload: {
    				dialogData: {
    					valueDetailData: null
    				}
    			}
    		});
    	}, 300);
    }

    render() {
    	let { systemConfigStore } = this.props;
    	let { dialogShow, dialogData } = systemConfigStore;
    	let { valueDetail } = dialogShow;
    	let { valueDetailData } = dialogData;

    	return (
    		<Modal
    			title={systemConfigLang.modal("configDetail")}
    			visible={valueDetail}
    			onCancel={this.closeModal}
    			footer={null}
    			maskClosable={true}
    			className="pre-code-modal"
    		>
    			<div style={{ maxHeight: 400, overflow: "scroll" }}>
    				<pre className="pre-code-view">{isJSON(valueDetailData) ? JSON.stringify(JSON.parse(valueDetailData), null, 4) : valueDetailData}</pre>
    			</div>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	systemConfigStore: state.systemConfig
}))(ValueDetailModal);
