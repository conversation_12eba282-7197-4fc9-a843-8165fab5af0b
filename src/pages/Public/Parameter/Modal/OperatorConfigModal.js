import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, Form, message, Row, Col, Radio, Switch, Alert } from "antd";
import { systemConfigAPI } from "@/services";
import AceEditor from "react-ace";
import { isJSON } from "@/utils/isJSON";
import { systemConfigLang } from "@/constants/lang";

const TextArea = Input.TextArea;

class OperatorConfigModal extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    }

    submitModal = () => {
    	let { systemConfigStore, dispatch, refresh } = this.props;
    	let { dialogData, dialogShow } = systemConfigStore;
    	let { configData } = dialogData;
    	let { addConfig, modifyConfig } = dialogShow;

    	if (!configData.configKey) {
    		// lang:配置名称不能为空
    		message.warning(systemConfigLang.tip("configNameEmptyTip"));
    		return;
    	} else if (!configData.configValue) {
    		// lang:配置详情不能为空
    		if (configData.type !== "Boolean") {
    			message.warning(systemConfigLang.tip("configValueEmptyTip"));
    			return;
    		}
    	} else if (!configData.description) {
    		// lang:配置描述不能为空
    		message.warning(systemConfigLang.tip("configDescriptionEmptyTip"));
    		return;
    	}
    	// lang:参数值不是标准的Json格式 无法判断有效的json只能去除
    	// if (configData.type === "Json") {
    	// 	if (!isJSON(configData.configValue)) {
    	// 		message.warning(systemConfigLang.tip("isNotJson"));
    	// 		return;
    	// 	}
    	// }
    	if (!configData.configValue) {
    		configData.configValue = false;
    	}

    	if (addConfig) {
    		systemConfigAPI.addConfig(configData).then(res => {
    			if (res.success) {
    				message.success(res.message);

    				// 刷新系统配置列表
    				// dispatch({
    				// 	type: "systemConfig/getConfigList"
    				// });
    				refresh && refresh({current: 1});
    			} else {
    				message.error(res.message);
    			}
    		}).catch(e => {
    			console.log(e);
    		});
    	}
    	if (modifyConfig) {
    		systemConfigAPI.modifyConfig(configData).then(res => {
    			if (res.success) {
    				message.success(res.message);

    				// 刷新系统配置列表
    				// dispatch({
    				// 	type: "systemConfig/getConfigList"
    				// });
    				refresh && refresh();
    			} else {
    				message.error(res.message);
    			}
    		}).catch(e => {
    			console.log(e);
    		});
    	}

    	this.closeModal();
    }

    closeModal = () => {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "systemConfig/setAttrValue",
    		payload: {
    			dialogShow: {
    				addConfig: false,
    				modifyConfig: false
    			}
    		}
    	});
    	setTimeout(() => {
    		dispatch({
    			type: "systemConfig/setAttrValue",
    			payload: {
    				dialogData: {
    					configData: {
    						uuid: null,
    						configValue: null,
    						description: null,
    						type: null,
    						status: null
    					}
    				}
    			}
    		});
    	}, 300);
    }

    changeFiledValue = (field, type, e) => {
    	let { systemConfigStore, dispatch } = this.props;
    	let { configData } = systemConfigStore.dialogData;
    	let value;

    	if (type === "select") {
    		value = e;
    	} else if (type === "input") {
    		value = e.target.value;
    	} else if (type === "format") {
    		if (isJSON(configData["configValue"])) {
    			value = JSON.stringify(JSON.parse(configData["configValue"]));
    		} else {
    			// lang:配置详情不是标准化json
    			message.warning(systemConfigLang.modal("configNotJson"));
    			return;
    		}
    	} else if (type === "switch") {
    		value = e.toString();
    	}

    	console.log(value);
    	configData[field] = value;

    	if (field === "type") {
    		if (value === "String") {
    			configData["configValue"] = null;
    		} else if (value === "Boolean") {
    			configData["configValue"] = true;
    		} else if (value === "Json") {
    			configData["configValue"] = null;
    		}
    	}

    	dispatch({
    		type: "systemConfig/setAttrValue",
    		payload: {
    			dialogData: {
    				configData
    			}
    		}
    	});
    }

    render() {
    	const { systemConfigStore } = this.props;
    	const { dialogShow, dialogData } = systemConfigStore;
    	const { addConfig, modifyConfig } = dialogShow;
    	const { configData } = dialogData;
    	const { configKey, configValue, description, type } = configData;

    	return (
    		<Modal
    			title={addConfig ? systemConfigLang.modal("addModalTitle") : systemConfigLang.modal("modifyModalTitle")} // lang:新增系统配置or修改系统配置
    			visible={addConfig || modifyConfig}
    			maskClosable={false}
    			width={750}
    			onCancel={this.closeModal}
    			onOk={this.submitModal}
    		>
    			<Form className="basic-form">
    				<Row gutter={10}>
    					<Col span={5} className="basic-info-title">
    						{systemConfigLang.modal("configName")}
    					</Col>
    					<Col span={18}>
    						<Input
    							type="text"
    							placeholder={systemConfigLang.modal("configNamePlaceholder")} // 请输入配置名称
    							value={configKey || undefined}
    							onChange={(e) => {
    								this.changeFiledValue("configKey", "input", e);
    							}}
    							disabled={!!modifyConfig}
    						/>
    					</Col>
    				</Row>
    				<Row gutter={10}>
    					<Col span={5} className="basic-info-title">
    						{/* lang:配置名称 */}
    						{systemConfigLang.modal("configType")}
    					</Col>
    					<Col span={18}>
    						<Radio.Group
    							value={type}
    							buttonStyle="solid"
    							onChange={(e) => {
    								this.changeFiledValue("type", "input", e);
    							}}
    							disabled={!!modifyConfig}
    						>
    							<Radio.Button value="String">String</Radio.Button>
    							<Radio.Button value="Boolean">Boolean</Radio.Button>
    							<Radio.Button value="Json">Json</Radio.Button>
    						</Radio.Group>
    						{/* {
    							type === "Json" &&
                                <a
                                	style={{ float: "right", lineHeight: "32px" }}
                                	onClick={(e) => {
                                		this.changeFiledValue("configValue", "format", e);
                                	}}
                                >
                                    格式化JSON
                                </a>
    						} */}
    						{
    							addConfig &&
                                <Alert
                                	message={systemConfigLang.modal("switchTypeTip")} // 切换类型，参数配置会被清空。
                                	type="warning"
                                	style={{ marginTop: "10px" }}
                                />
    						}
    					</Col>
    				</Row>
    				<Row gutter={10} style={{"height": "auto", "overflow": "hidden"}}>
    					<Col span={5} className="basic-info-title">
    						{/* lang:配置详情 */}
    						{systemConfigLang.modal("configDetail")}
    					</Col>
    					<Col span={18}>
    						{
    							type === "String" &&
                                <Input
                                	type="text"
                                	placeholder={systemConfigLang.modal("configValuePlaceholder")} // lang:请输入配置参数
                                	value={configValue || undefined}
                                	onChange={(e) => {
                                		this.changeFiledValue("configValue", "input", e);
                                	}}
                                />
    						}
    						{
    							type === "Boolean" &&
                                <Switch
                                	checkedChildren={systemConfigLang.modal("on")}
                                	unCheckedChildren={systemConfigLang.modal("off")}
                                	checked={configValue && configValue.toString() === "true"}
                                	onChange={(e) => {
                                		this.changeFiledValue("configValue", "switch", e);
                                	}}
                                />
    						}
    						{
    							type === "Json" &&
                                <AceEditor
                                	mode="json"
                                	theme="textmate"
                                	name="myValue"
                                	onChange={(e) => {
                                		this.changeFiledValue("configValue", "select", e);
                                	}}
                                	fontSize={14}
                                	height="200px"
                                	width="100%"
                                	style={{ border: "1px solid #D9D9D9", borderRadius: 4, display: "inline-block" }}
                                	editorProps={{ $blockScrolling: true }}
                                	showPrintMargin={true}
                                	showGutter={true}
                                	highlightActiveLine={true}
                                	value={configValue ? (isJSON(configValue) ? JSON.stringify(JSON.parse(configValue), null, 4) : configValue) : undefined}
                                	enableSnippets={false}
                                	setOptions={{
                                		// enableSnippets: false,
                                		showLineNumbers: true,
                                		tabSize: 2
                                	}}
                                />
    						}
    					</Col>
    				</Row>
    				<Row gutter={10} style={{ height: "auto" }}>
    					<Col span={5} className="basic-info-title">
    						{systemConfigLang.modal("description")}
    					</Col>
    					<Col span={18}>
    						<TextArea
    							placeholder={systemConfigLang.modal("descriptionPlaceholder")} // lang:请输入描述内容
    							rows={4}
    							value={description || undefined}
    							onChange={(e) => {
    								this.changeFiledValue("description", "input", e);
    							}}
    						/>
    					</Col>
    				</Row>
    			</Form>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	systemConfigStore: state.systemConfig
}))(OperatorConfigModal);
