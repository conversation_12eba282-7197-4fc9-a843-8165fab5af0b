import { useEffect } from "react";
import { connect } from "dva";
import { Popover, Cascader, Tooltip, Icon } from "antd";
import { QueryListScene } from "tntd";
import NoPermission from "@/components/NoPermission";
import { operationLogLang } from "@/constants/lang";
import moment from "moment";
import { logAPI, solutionAPI } from "@/services";
const { QueryList, QueryForm, Field, createActions } = QueryListScene;
const actions = createActions();

export default connect(state => ({
	globalStore: state.global,
	logStore: state.log
}))((props)=>{
	const { logStore, dispatch } = props;
	const { menuMap } = logStore;

	useEffect(()=>{
		getMenuMap();
	}, []);

	// 查询
	const query = (params = {}) => {
		const { current, dateRange, menuUuid: menuUuidArr = [], ...rest } = params;
		const startTime = moment(dateRange[0]).format("YYYY-MM-DD HH:mm:ss");
		const endTime = moment(dateRange[1]).format("YYYY-MM-DD HH:mm:ss");
		const menuUuid = menuUuidArr && menuUuidArr.length > 0 ? menuUuidArr[menuUuidArr.length - 1] : null;
		return logAPI.getLogList({
			...rest,
			startTime,
			endTime,
			menuUuid,
			curPage: current || 1,
			pageSize: params.pageSize || 10,
			orderType: "desc",
			orderBy: "operationTime"
		})
			.then((data) => {
				data = data?.data;
				return {
					pageSize: Number(data?.pageSize),
					current: Number(data?.curPage),
					total: Number(data?.total),
					data: data?.userOperationList || []
				};
			});
	};

	const onFormChange = (values, changeInfo) => {
		if (["menuUuid"].includes(changeInfo.name)) {
			actions.search({
				...values,
				current: 1
			});
		}
	};

	const getMenuMap = () => {
		const menuMap = [];
		solutionAPI.getSolution().then(res => {
			if (res.success) {
				let groups = res.data[0].children;
				groups.forEach((group, groupIndex) => {
					menuMap.push({
						value: group.uuid,
						label: group.name,
						children: []
					});
					if (group.children && group.children.length > 0) {
						group.children.forEach((menu) => {
							menuMap[groupIndex].children.push({
								value: menu.uuid,
								label: menu.name
							});
						});
					}
				});
				dispatch({
					type: "log/setAttrValue",
					payload: {
						menuMap
					}
				});
			}
		}).catch(e => {
			console.log(e);
		});
	};

	const columns = [
		{
			title: operationLogLang.table("menuName"), // 菜单名称
			dataIndex: "menu",
			key: "menu"
		},
		{
			title: operationLogLang.table("operationType"), // 操作类型
			dataIndex: "function",
			key: "function",
			render: (text) => {
				if (text) {
					return text;
				} else {
					return "-";
				}
			}
		},
		// 暂时去掉，不可删除！！
		// {
		// 	title: operationLogLang.table("businessDescription"),     //业务描述
		// 	dataIndex: "description",
		//     key: "description",
		//     render: (text) => {
		//         if (text) {
		//             return (
		//                text.length > 25 ?
		//                 <Popover
		//                     placement="top"
		//                     title={operationLogLang.table("businessDescriptionContent")}
		//                     content={
		//                         <div style={{maxWidth:350, maxHeight:200, overflow:"scroll"}}>{text}</div>
		//                     }
		//                     trigger="hover"
		//                 >
		//                     <a>{`${text.substring(0, 25)}...`}</a>
		//                 </Popover> :
		//                 text
		//             );
		//         } else {
		//             return "-";
		//         }
		//     }
		// },
		{
			title: operationLogLang.table("operator"), // 操作人
			dataIndex: "account",
			key: "account"
		},
		{
			title: operationLogLang.table("operatorTime"), // 操作时间
			dataIndex: "operationTime",
			key: "operationTime",
			width: 190,
			render: (record) => {
				return moment(record).format("YYYY-MM-DD HH:mm:ss");
			}
		},
		{
			title: operationLogLang.table("detail"), // 详情
			dataIndex: "detail",
			key: "detail",
			width: 100,
			render: (text, record) => {
				if (record.detail) {
					return (
						<Popover
							placement="left"
							title={operationLogLang.table("detailTipInfo")}
							content={
								<div style={{ width: 500, maxHeight: 300, overflow: "scroll" }}>
									<pre className="pre-code-view">
										{JSON.stringify(JSON.parse(record.detail), null, 2)}
									</pre>
								</div>
							}
							trigger="hover"
						>
							{/* <a><Icon type="profile" className="option" /></a> */}
							<a>{operationLogLang.table("logDetail")}</a>
						</Popover>
					);
				} else {
					// lang: 暂无详情
					return <span>{operationLogLang.table("noDetail")}</span>;
				}
			}
		}
	];

	let [startTime, endTime] = [null, null];
	endTime = moment().format("YYYY-MM-DD HH:mm:ss");
	startTime = moment().subtract(1, "weeks").format("YYYY-MM-DD HH:mm:ss");

	if (!window.auth("QX0301", "viewOperationLog")) {
		return <NoPermission/>;
	}
	return (
		<QueryListScene title={operationLogLang.common("title")} query={query} actions={actions}>
			<QueryForm
				onChange={onFormChange}
				initialValues={{
					dateRange: [moment(startTime), moment(endTime)]
				}}
			>
				<Field
					name="dateRange"
					type="dateRange"
					props={{
						allowClear: false,
						showTime: { format: "YYYY-MM-DD HH:mm" },
						format: "YYYY-MM-DD HH:mm"
					}}
				/>
				<Field
                    component={()=>(
                        <Tooltip title={operationLogLang.table("operatorTime")}
                        >
                            <Icon
                                type="info-circle"
                                style={{ marginTop: 7 }}
                            />
                        </Tooltip>
                    )}
				/>
				<Field
					name="menuUuid"
					component={Cascader}
					props={{
						placeholder: operationLogLang.common("searchPlaceholder"), // 请选择菜单名称
						allowClear: true,
						options: menuMap || [],
						style: {
							width: 220
						}
					}}
				/>
				<Field
					name="account"
					props={{
						placeholder: operationLogLang.common("inputPlaceholder1"), // 操作人
						allowClear: true,
						options: menuMap || []
					}}
				/>
			</QueryForm>

			<QueryList
				columns={columns}
				rowKey="uuid"
			/>
		</QueryListScene>
	);
});
