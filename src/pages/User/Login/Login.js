import { PureComponent } from "react";
import { connect } from "dva";
import CountUp from "react-countup";
import { loginLang } from "@/constants/lang";
import { userAPI } from "@/services";
import LoginBox from "./Inner/LoginBox";
import ModifyPasswordBox from "./Inner/ModifyPasswordBox";
import EarthPng from "@/sources/images/login/earth.png";
import "./Login.less";

const weeks = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
const cnWeeks = ["日", "一", "二", "三", "四", "五", "六"];

class Login extends PureComponent {
    state = {
    	imgH: 0,
    	lang: "cn"
    };

    constructor(props) {
    	super(props);
    	this.onWindowResize = this.onWindowResize.bind(this);
    }

    componentWillMount() {
    	this.onWindowResize();
    	const lang = localStorage.lang;
    	if (!lang) return;
    	this.setState({
    		lang
    	});
    }

    componentDidMount() {
    	window.addEventListener("resize", this.onWindowResize);
    }

    /**
	 * 改变验证码图片
	 */
    modifyAuthCode = (code) => {
    	let params = {
    		account: code
    	};
    	userAPI.getAuthCode(params).then((res) => {
    		if (res && res.success) {
    			this.setState({
    				authCode: res.data.authCode,
    				authCodeAes: res.data.authCodeAes
    			});
    		}
    	});
    }

    changeAuthCode = (params) => {
    	this.setState({
    		authCode: params.authCode,
    		authCodeAes: params.authCodeAes
    	});
    }

    componentWillUnmount() {
    	const { dispatch } = this.props;
    	window.removeEventListener("resize", this.onWindowResize);

    	dispatch({
    		type: "login/setAttrValue",
    		payload: {
    			status: null,
    			submitting: false,
    			changePasswordShow: false,
    			userInfo: null,
    			loginData: {
    				account: null,
    				password: null
    			},
    			modifyPasswordData: {
    				oldPwd: null,
    				newPwd: null,
    				confirmNewPwd: null
    			}
    		}
    	});
    }

    onWindowResize() {
    	let wh = document.body.clientHeight;
    	this.setState({
    		imgH: wh
    	});
    }

    render() {
    	const { lang, imgH } = this.state;
    	const { loginStore } = this.props;
    	const { changePasswordShow } = loginStore;
    	const dateObj = new Date();
    	const Y = dateObj.getFullYear() + "." + (dateObj.getMonth() + 1);
    	const D = dateObj.getUTCDate();
    	const W = dateObj.getUTCDay();
    	return (
    		<div className="login-wrapper">
    			<div className="login-circle">
    				<img src={EarthPng} style={{ "height": imgH + "px" }}/>
    			</div>
    			<div className="login-wrap">
    				<div className="login-form-wrap">
    					<div className="login-form-left">
    						<h2>
    							{/* 智能网络 诚信互联 */}
    							{
    								lang === "en"
    									? <span>
    										{loginLang.common("title", lang)}<br />
    										{loginLang.common("title1", lang)}
    									</span>
    									: <span>
    										{loginLang.common("title", lang)}
    										{loginLang.common("title1", lang)}
    									</span>
    							}
    						</h2>
    						<div className="time-show">
    							<span className="day">
    								<CountUp
    									end={D}
    								/>
    								<label>th</label>
    							</span>
    							<span className="year">
    								{Y}
    							</span>
    							<span className="week">
                                    周{cnWeeks[W]} <span>{weeks[W]}</span>
    							</span>
    						</div>
    					</div>
    					<div className="login-form-right">
    						<div className="login-logo">
    							<span style={{ fontSize: lang === "en" ? "18px" : "24px" }}>
    								{
    									changePasswordShow
    										? loginLang.common("changePwd", lang) // 修改密码
    										: loginLang.common("unifiedPlatform", lang) // 统一登录平台
    								}
    							</span>
    						</div>
    						{
    							!changePasswordShow &&
                                <LoginBox
                                	lang={lang}
                                	authCode={this.state.authCode}
                                	authCodeAes={this.state.authCodeAes}
                                	changeAuthCode={this.changeAuthCode}
                                	modifyAuthCode={this.modifyAuthCode}
                                />
    						}
    						{
    							changePasswordShow &&
                                <ModifyPasswordBox lang={lang} />
    						}
    					</div>
    				</div>
    			</div>
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	loginStore: state.login
}))(Login);

