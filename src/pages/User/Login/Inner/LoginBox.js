import { PureComponent } from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { Input, Button, Icon, message, Modal } from "antd";
import { userAPI } from "@/services";
import { rsaPwd, checkRegPwd } from "@/utils/user";
import { clearStorage } from "@/utils";
import { loginLang } from "@/constants/lang";

function getQueryString(name) {
	const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
	const r = window.location.search.substr(1).match(reg);
	if (r != null) return decodeURI(r[2]);
	return null;
}

function addQueryString(url, query) {
	if (url.indexOf("?") >= 0) {
		return `${url}&${query}`;
	}

	return `${url}?${query}`;
}

class LoginBox extends PureComponent {
    state = {
    	nameFocus: false,
    	pwdFocus: false
    };

    constructor(props) {
    	super(props);
    	this.changeFieldValue = this.changeFieldValue.bind(this);
    }

    changeFieldValue(field, type, e) {
    	let { dispatch, loginStore } = this.props;
    	let { loginData } = loginStore;
    	let value;
    	if (type === "input") {
    		value = e.target.value;
    	} else if (type === "select") {
    		value = e;
    	}
    	loginData[field] = value;

    	dispatch({
    		type: "login/setAttrValue",
    		payload: {
    			loginData
    		}
    	});
    }

    componentDidMount() {
    	this.checkLogin();
    }

    // 判断是否已经登录过了
    checkLogin = async () => {
    	const logout = getQueryString("logout");
    	if (logout) {
    		sessionStorage.setItem("_csrf_", "");
    		// sessionStorage.clear('');
    		// localStorage.clear();
    		clearStorage();
    		return;
    	}
    	const client_id = getQueryString("client_id");
    	if (client_id) {
    		return;
    	}
    	const state = getQueryString("state");
    	const clientId = getQueryString("clientId");
    	let callbackUrl = getQueryString("callbackUrl");
    	if (callbackUrl) {
    		callbackUrl = decodeURIComponent(callbackUrl);
    	}
    	// 如果存在session则判断
    	if (sessionStorage.getItem("_csrf_")) {
    		const res = await userAPI.getUserMenuTree();
    		if (res && res.success && res.code === 200) {
    			const { user = {} } = res.data || {};
    			// 密码过期不做处理
    			if (user && user.updatePwdStatus) {
    				return ;
    			}
    			// 有clientId则为公司内部系统登录方式
    			if (clientId) {
    				let token = "";
    				// 通过clientId换取token
    				const res2 = await userAPI.getTokenByClientId();
    				if (res.success) {
    					token = res2.data.token;
    				}
    				window.location.href = addQueryString(
    					callbackUrl,
    					`token=${token}${state ? `&state=${state}` : ""}`
    				);
    				return;
    			}
    			window.location.href = callbackUrl || "/bridge/userCenter";
    		}
    	}
    };

    async handleSubmit() {
    	const { dispatch, loginStore, lang, changeAuthCode, authCodeAes } = this.props;
    	let { loginData } = loginStore;
    	let { password, account } = loginData || {};

    	if (!account) {
    		message.error(loginLang.common("enterName", lang)); // 请输入账户名
    		return;
    	}
    	if (!password) {
    		message.error(loginLang.common("enterPwd", lang)); // 请输入密码！
    		return;
    	}
    	// if (!checkRegPwd(password)) {
    	// 	message.error(loginLang.tipInfo("tip7", lang)); // 必须包含大小写字母、数字、特殊字符，长度8~18位
    	// 	return;
    	// }

    	password = rsaPwd(password);
    	loginData = {
    		...loginData,
    		authCodeEnc: authCodeAes,
    		password
    	};

    	// authCodeAes 输入验证码
    	if (authCodeAes && !loginData.authCode) {
    		message.error(loginLang.tipInfo("tip8", lang));
    		return;
    	}

    	dispatch({
    		type: "login/setAttrValue",
    		payload: {
    			submitting: true,
    			modifyPasswordData: {
    				oldPwd: password
    			}
    		}
    	});

    	let callbackUrl = getQueryString("callbackUrl");
    	if (callbackUrl) {
    		callbackUrl = decodeURIComponent(callbackUrl);
    	}

    	let redirect_uri = getQueryString("redirect_uri");
    	if (redirect_uri) {
    		redirect_uri = decodeURIComponent(redirect_uri);
    	}
    	// 有clientId为公司内部登录方式
    	const clientId = getQueryString("clientId");
    	if (clientId) {
    		loginData.clientId = clientId;
    	}
    	// 有client_id为oAuth登录方式
    	const client_id = getQueryString("client_id");
    	if (client_id) {
    		loginData.client_id = client_id;
    	}
    	const state = getQueryString("state");

    	let {tempRandom, authSuccess, authMessage} = ["", false, ""];
    	try {
    		// 获取加盐随机数
    		const authResult = await userAPI.auth(loginData) || {};
    		tempRandom = authResult.data;
    		authSuccess = authResult.success;
    		authMessage = authResult.message;

    		if (authSuccess) {
    			userAPI.signIn({...loginData, tempRandom}).then(res => {
    				dispatch({
    					type: "login/setAttrValue",
    					payload: {
    						submitting: false
    					}
    				});
    				if (res.success) {
    					if (res && res.data) {
    						// 获取登录接口返回的 loginTime 和 loginStatus
    						const { loginTime = '',loginStatus = 'fail' } = res?.data;

    						// // 显示自定义登录成功消息
    						if (loginStatus === 'success' && loginTime) {
    							message.success(`上次登录时间: ${loginTime}，上次登录状态: ${loginStatus}`);
    						}

    						// 将_csrf_存入session同步其他tab页
    						this.saveDateToBrowser(res);

    						// 如果本地存储的当前应用不在应用列表里，则删除本地存储
    						if (res.data.user && res.data.user.appName) {
    							// 获取本地存储的当前应用
    							const localCurrentApp = localStorage.currentApp;
    							if (localCurrentApp) {
    								const toJson = JSON.parse(localCurrentApp);
    								const appList = JSON.parse(
    									res.data.user.appName
    								);
    								const obj = appList.find(
    									item => item === toJson.name
    								);
    								if (!obj) localStorage.removeItem("currentApp");
    							}
    						}

    						if (res.data.needAuthCode) {
    							changeAuthCode({
    								authCode: res.data.authCode,
    								authCodeAes: res.data.authCodeAes
    							});
    							return;
    						}

    						// 如果不是首次登录
    						if (
    							res.data.license &&
                                res.data.license &&
                                res.data.license.code === 100404
    						) {
    							// this.saveDateToBrowser(res);
    							// license不存在或者已经过期
    							dispatch(
    								routerRedux.push(
    									"/user/startup?currentTab=licence"
    								)
    							);
    							return;
    						}

    						if (!clientId && !client_id && !res.data.hasSolution) {
    							// this.saveDateToBrowser(res);
    							// 是否有解决方案
    							dispatch(
    								routerRedux.push(
    									"/user/startup?currentTab=solution"
    								)
    							);
    							return;
    						}
    						// res.data.first 以前用的fisrt，2.1.0之后更改为needResetPwd
    						if (res.data.needResetPwd) {
    							// 如果是首次登陆
    							// this.saveDateToBrowser(res);
    							const { user = {} } = res.data || {};
    							dispatch({
    								type: "login/setAttrValue",
    								payload: {
    									changePasswordShow: true,
    									userInfo: res.data
    								}
    							});
    							if (user.firstLogin === "1") {
    								message.error(loginLang.tipInfo("pwdExpired", lang)); // 密码已过期，请修改密码
    							} else {
    								message.warn(loginLang.tipInfo("tip1", lang)); // 请修改初始密码
    							}
    							return;
    						}

    						if (res.data.license.leftDay < 30) {
    							localStorage.setItem(
    								"licenseMsg",
    								res.data.license.message
    							);
    						}

    						let indexPath = "/bridge/userCenter";
    						let licenseLeftDay = res.data.license.leftDay;

    						indexPath =
                                callbackUrl ||
                                redirect_uri ||
                                res.data.indexPath ||
                                indexPath;
    						if (clientId && res.data.token) {
    							indexPath = addQueryString(
    								indexPath,
    								`token=${res.data.token}&expires=${
    									res.data.expires
    								}${state ? `&state=${state}` : ""}`
    							);
    						}
    						if (client_id && res.data.code) {
    							indexPath = addQueryString(
    								indexPath,
    								`code=${res.data.code}&expires_in=${
    									res.data.expires
    								}${state ? `&state=${state}` : ""}`
    							);
    						}
    						if (res.data.license.leftDay < 30) {
    							// 如果License少于30天，那就弹窗提醒用户
    							this.countDownForLessDays(
    								licenseLeftDay,
    								indexPath
    							);
    						} else {
    							window.location.href = indexPath;
    						}
    					}
    				} else {
    					message.error(res.message);
    				}
    			}).catch(err => {
    				console.log(err);
    				dispatch({
    					type: "login/setAttrValue",
    					payload: {
    						submitting: false
    					}
    				});
    			});
    		} else {
    			message.error(authMessage);
    			dispatch({
    				type: "login/setAttrValue",
    				payload: {
    					submitting: false
    				}
    			});
    		}
    	} catch (e) {
    		message.error(e.message);
    		dispatch({
    			type: "login/setAttrValue",
    			payload: {
    				submitting: false
    			}
    		});
    	}
    }

    /**
     * 将数据保存在浏览器
     */
    saveDateToBrowser(res) {
    	const { csrfToken } = res.data;
    	sessionStorage.setItem("_csrf_", csrfToken);
    	localStorage.setItem("_sync_qjt_csrf_", csrfToken); // 同步到其他页面
    }

    countDownForLessDays(licenseLeftDay, indexPath) {
    	let secondsToGo = 4;
    	let content;
    	if (licenseLeftDay < 0) {
    		content = `您的License已经过期${licenseLeftDay *
                -1} 天，请及时续费。 ${secondsToGo} 秒后关闭。`;
    	} else if (licenseLeftDay === 0) {
    		content = `您的License已经过期，请及时续费。 ${secondsToGo} 秒后关闭。`;
    	} else if (licenseLeftDay > 0) {
    		content = `您的License即将在${licenseLeftDay} 天后过期，请及时续费。 ${secondsToGo} 秒后关闭。`;
    	}
    	const modal = Modal.warning({
    		title: "License即将过期提醒",
    		content: content,
    		onOk: () => {
    			clearInterval(timer);
    			modal.destroy();
    			window.location.href = indexPath;
    		}
    	});
    	const timer = setInterval(() => {
    		if (licenseLeftDay < 0) {
    			content = `您的License已经过期${licenseLeftDay *
                    -1} 天，请及时续费。 ${secondsToGo} 秒后关闭。`;
    		} else if (licenseLeftDay === 0) {
    			content = `您的License已经过期，请及时续费。 ${secondsToGo} 秒后关闭。`;
    		} else if (licenseLeftDay > 0) {
    			content = `您的License即将在${licenseLeftDay} 天后过期，请及时续费。 ${secondsToGo} 秒后关闭。`;
    		}
    		secondsToGo -= 1;
    		modal.update({
    			content: content
    		});
    	}, 1000);
    	setTimeout(() => {
    		clearInterval(timer);
    		modal.destroy();
    		window.location.href = indexPath;
    	}, secondsToGo * 1000);
    }

    render() {
    	const { loginStore, lang, authCode, modifyAuthCode } = this.props;
    	const { submitting } = loginStore;
    	const { nameFocus, pwdFocus } = this.state;
    	return (
    		<div className="login-form">
    			<Input
    				size="large"
    				className={`login-form-item ${nameFocus ? "focus-input" : ""}`}
    				prefix={<Icon type="user" className="prefixIcon" />}
    				placeholder={loginLang.common("enterName", lang)} // 请输入账户名
    				onFocus={()=>{
    					this.setState({
    						nameFocus: true
    					});
    				}}
    				onBlur={()=>{
    					this.setState({
    						nameFocus: false
    					});
    				}}
    				onChange={this.changeFieldValue.bind(
    					this,
    					"account",
    					"input"
    				)}
    			/>
    			<Input
    				size="large"
    				className={`login-form-item ${pwdFocus ? "focus-input" : ""}`}
    				prefix={<Icon type="lock" className="prefixIcon" />}
    				type="password"
    				placeholder={loginLang.common("enterPwd", lang)} // 请输入密码
    				onChange={this.changeFieldValue.bind(this, "password", "input")}
    				onFocus={()=>{
    					this.setState({
    						pwdFocus: true
    					});
    				}}
    				onBlur={()=>{
    					this.setState({
    						pwdFocus: false
    					});
    				}}
    				onPressEnter={() => {
    					this.handleSubmit();
    				}}
    			/>
    			{
    				authCode &&
                    <Input
                    	size="large"
                    	className="input-auth-code"
                    	addonBefore={
                    		<img
                    			onClick={(e) => {
                    				e.preventDefault();
                    				modifyAuthCode();
                    				this.changeFieldValue("authCode", "select", "");
                    			}}
                    			src={`data:image/jpg+xml;base64,${authCode}`}
                    		/>
                    	}
                    	placeholder={loginLang.common("authCodePlaceholder", lang)} //  请输入验证码
                    	onChange={this.changeFieldValue.bind(this, "authCode", "input")}
                    	onPressEnter={() => {
                    		this.handleSubmit();
                    	}}
                    />
    			}
    			<Button
    				loading={submitting}
    				size="large"
    				className="submit"
    				type="primary"
    				onClick={() => {
    					this.handleSubmit();
    				}}
    			>
    				{/* 登录 */}
    				{loginLang.common("logIn", lang)}
    			</Button>
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	loginStore: state.login
}))(LoginBox);
