import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Button, message } from "antd";
import { userAPI } from "@/services";
import { loginLang } from "@/constants/lang";
import { rsaPwd, checkRegPwd } from "@/utils/user";

class ModifyPasswordBox extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.changeFieldValue = this.changeFieldValue.bind(this);
    }

    changeFieldValue(field, type, e) {
    	let { dispatch, loginStore } = this.props;
    	let { modifyPasswordData } = loginStore;
    	let value;
    	if (type === "input") {
    		value = e.target.value;
    	} else if (type === "select") {
    		value = e;
    	}
    	modifyPasswordData[field] = value;

    	dispatch({
    		type: "login/setAttrValue",
    		payload: {
    			modifyPasswordData
    		}
    	});
    }

    modifyPassword() {
    	const { loginStore, lang } = this.props;
    	let { modifyPasswordData } = loginStore;
    	const { newPwd, confirmNewPwd, oldPwd } = modifyPasswordData || {};
    	if (!newPwd) {
    		message.error(loginLang.tipInfo("tip4", lang)); // 密码输入不能为空
    		return;
    	}
    	if (!checkRegPwd(newPwd)) {
    		message.error(loginLang.tipInfo("tip9", lang)); // 密码必须包含大小写字母、数字、特殊字符，长度8~18位
    		return;
    	}
    	if (!checkRegPwd(confirmNewPwd)) {
    		message.error(loginLang.tipInfo("tip10", lang)); // 确认密码必须包含大小写字母、数字、特殊字符，长度8~18位
    		return;
    	}
    	if (newPwd !== confirmNewPwd) {
    		message.error(loginLang.tipInfo("tip5", lang)); // 两次输入的密码不一致
    		return;
    	}

    	let params = {
    		oldPwd,
    		newPwd: rsaPwd(newPwd)
    	};

    	userAPI.changePwd(params).then((res) => {
    		if (res.success) {
    			// 修改密码成功后，原csrfToken失效，需要重新获取csrfToken
    			const { csrfToken } = res.data || {};
    			sessionStorage.setItem("_csrf_", csrfToken);
    			localStorage.setItem("_sync_qjt_csrf_", csrfToken); // 同步到其他页面

    			message.success(loginLang.tipInfo("tip6", lang)); // 修改密码成功
    			this.props.dispatch({
    				type: "login/setAttrValue",
    				payload: {
    					changePasswordShow: false
    				}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    render() {
    	const { lang } = this.props;

    	return (
    		<div className="login-form modify-password">
    			<Input
    				size="large"
    				type="password"
    				className="login-form-item"
    				onChange={this.changeFieldValue.bind(this, "newPwd", "input")}
    				placeholder={loginLang.common("newPwd", lang)} // 请输入新密码
    			/>
    			<Input
    				size="large"
    				type="password"
    				className="login-form-item"
    				onChange={this.changeFieldValue.bind(this, "confirmNewPwd", "input")}
    				placeholder={loginLang.common("sureNewPwd", lang)} // 请确认新密码
    				onPressEnter={() => {
    					this.modifyPassword();
    				}}
    			/>
    			<span className="modify-pwd-warn">{loginLang.tipInfo("tip9", lang)}</span>
    			<Button
    				size="large"
    				className="submit"
    				type="primary"
    				onClick={() => {
    					this.modifyPassword();
    				}}
    			>
    				{/* 修改 */}
    				{loginLang.common("modify", lang)}
    			</Button>
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	loginStore: state.login
}))(ModifyPasswordBox);
