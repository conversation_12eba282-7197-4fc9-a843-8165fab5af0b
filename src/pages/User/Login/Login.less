.login-wrapper {
    & {
        position: relative;
        width: 100%;
        min-width: 960px;
        height: 100vh !important;
        background-image: linear-gradient(135deg, #1752C3 0%, #0E265A 100%);
    }
    .login-circle {
        position: absolute;
        top: 0;
        left:0;
        margin-left: auto;
        margin-right: auto;
    }
    .login-wrap {
        & {
            position: relative;
            height: 100vh;
            margin-left: auto;
            margin-right: auto;
        }

        .login-form-wrap {
            & {
                position: absolute;
                left: 0;
                top: 50%;
                width: 100%;
                margin-left:auto;
                margin-right:auto;
                box-shadow: 0 0 20px rgba(0, 0, 0, .6);
            }
            .login-form-left {
                & {
                    float: left;
                    position: relative;
                    height: 100%;
                    background-image: url(../../../sources/images/login/login.png);
                    background-size: cover;
                    background-repeat: no-repeat;
                }
                h2{
                    font-size: 48px;
                    font-weight: normal;
                    font-family: PingFangSC-Thin;
                    color: #FFFFFF;
                    letter-spacing: 0;
                }
                .time-show{
                    font-family: PingFangSC-Regular;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    position: absolute;
                    font-size: 16px;
                    >span{
                        display: block;
                    }
                    .day{
                        letter-spacing: 0;
                        position:relative;
                        font-family: PingFangSC-Thin;
                        text-align: center;
                        font-size: 80px;
                        width: 94px;
                        label{
                            font-size: 20px;
                            position: absolute;
                            top: 14px;
                            right: -18px;
                            font-family: PingFangSC-Regular;
                        }
                    }
                    .year{
                        margin:-20px 0 0 4px;
                    }
                    .week{
                        margin: 4px 0 0 4px;
                        span{
                            margin-left: 10px;
                        }
                    }
                }
            }
            .login-form-right {
                & {
                    float: right;
                    height: 100%;
                    background-color: #fff;
                }

                &:hover {
                    box-shadow: 8px 8px 0px rgba(0, 0, 0, .1);
                }

                .login-logo {
                    & {
                        text-align: center;
                        cursor: pointer;
                        height: 24px;
                        line-height: 36px;
                        margin-bottom: 40px;
                    }

                    span {
                        text-transform: uppercase;
                        display: inline-block;
                        font-family: HiraginoSansGB-W3;
                        color: #17233D;
                        letter-spacing: 0;
                    }
                }
                .login-form{
                    &.modify-password{
                        input{
                            padding-left: 20px;
                        }
                    }
                    .login-form-item {
                        position: relative;
                        &:not(:last-of-type){
                            margin-bottom: 20px;
                        }
                        .prefixIcon{
                            font-size: 16px;
                            color: #777e8d;
                            position: relative;
                            margin-left: 3px;
                        }
                        &::before{
                            content:'';
                            position: absolute;
                            height: 40px;
                            width: 1px;
                            left:40px;
                            background: #B2BECD;
                            z-index: 10;
                        }
                        &.focus-input{
                            span.ant-input-prefix{
                                >i{
                                    color: #126BFB;
                                }
                           }
                        }
                    }
                    input{
                        font-family: PingFangSC-Regular;
                        color:#17233D;
                        font-size: 14px;
                        height: 40px;
                        box-sizing: border-box;
                        background: #FFFFFF;
                        border: 1px solid #b2becd;
                        border-radius: 2px;
                        position:relative;
                        padding-left: 60px;
                        &:focus{
                           border-color: #126BFB;
                        }
                    }
                    input::-webkit-input-placeholder{
                        font-family: PingFangSC-Regular;
                        color:rgba(23,35,61,0.5);
                    }
                    input::-moz-placeholder{
                        font-family: PingFangSC-Regular;
                        color:rgba(23,35,61,0.5);
                    }
                    input:-moz-placeholder{
                        font-family: PingFangSC-Regular;
                        color:rgba(23,35,61,0.5);
                    }
                    input:-ms-input-placeholder{
                        font-family: PingFangSC-Regular;
                        color:rgba(23,35,61,0.5);
                    }

                    .input-auth-code{
                        border: 1px solid #b2becd;
                        img{
                            height: 38px;
                        }
                        input, .ant-input-group-addon{
                            padding:0;
                            border:none;
                        }
                        input{
                            padding-left: 18px;
                        }
                    }
                }
                button {
                    width: 100%;
                    background: #126BFB;
                    border-radius: 2px;
                    border-radius: 2px;
                    height: 40px;
                    &.submit {
                        margin-top: 30px;
                        font-size: 14px;
                        span{
                            letter-spacing: -2px;
                        }
                    }
                }

            }
        }
        .modify-pwd-warn{
            color:#ee5335;
            font-size:12px;
            display: block;
            margin-top:12px;
        }
    }

    @media screen and (min-width: 1441px) {
        .login-wrap {
            width: 1120px;
        }
        .login-form-wrap {
            height: 600px;
            margin-top:-300px;
        }
        .login-form-left {
            width: 720px;
            h2{
                margin:110px 90px;
            }
            .time-show{
                bottom: 120px;
                left: 90px;
            }
        }
        .login-form-right{
            width: 400px;
            padding: 40px;
            .login-logo{
                margin-top: 100px;
            }
        }
    }
    @media screen and (max-width: 1440px) {
        .login-wrap {
            width: 960px;
        }
        .login-form-wrap {
            height: 500px;
            margin-top:-250px;
        }
        .login-form-left {
            width: 610px;
            h2{
                margin:60px 90px 110px 50px;
            }
            .time-show{
                bottom: 60px;
                left: 50px;
            }
        }
        .login-form-right{
            width: 350px;
            padding: 25px;
            .login-logo{
                margin-top: 92px;
            }
        }
    }
}
