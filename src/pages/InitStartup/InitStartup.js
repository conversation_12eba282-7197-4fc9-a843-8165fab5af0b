import { PureComponent } from "react";
import { connect } from "dva";
import Startup from "@/components/Startup";

class InitStartup extends PureComponent {
	constructor(props) {
		super(props);
	}

	render() {
		let { location } = this.props;

		return (
			<div className="startup-layout">
				<Startup
					isInit={true}
					location={location}
				/>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	startupStore: state.startup
}))(InitStartup);
