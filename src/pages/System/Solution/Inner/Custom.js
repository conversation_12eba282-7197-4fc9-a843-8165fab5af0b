import { PureComponent } from "react";
import { connect } from "dva";
import { Button, message, Icon, Tooltip } from "antd";
import AddGroupModal from "../Modal/AddGroupModal";
import ModifyGroupModal from "../Modal/ModifyGroupModal";
import BinItem from "./Inner/BinItem";
import { solutionAPI } from "@/services";
import solutionLang from "@/constants/lang/solution";

class Custom extends PureComponent {

	constructor(props) {
		super(props);
		this.state = {
			showDtPanel: {
				"0": true
			}
		};
	}

	addSolutionGroup = (uuid) => {
		let { dispatch } = this.props;
		dispatch({
			type: "solution/setDialogData",
			payload: {
				addGroupData: {
					parentUuid: uuid
				}
			}
		});
		dispatch({
			type: "solution/setDialogShow",
			payload: {
				addSolutionGroup: true
			}
		});
	}

	saveSolution = () => {
		let { solutionStore, dispatch } = this.props;
		let { customTree, currentSolutionIndex } = solutionStore;
		let newList = [];

		customTree[currentSolutionIndex]["children"].map((groupItem) => {
			let newObj = {};
			newObj["customTreeUuid"] = groupItem.uuid;
			newObj["menuList"] = [];

			groupItem.children && groupItem.children.map((menuItem) => {
				newObj["menuList"].push(menuItem.uuid);
			});
			newList.push(newObj);
		}
		);
		let params = {
			params: JSON.stringify(newList)
		};

		solutionAPI.updateSolutionMenu(params).then(res => {
			if (res.success) {
				message.success(solutionLang.common("success"));

				dispatch({
					type: "solution/getSolution"
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	// 折叠
	collapseDt = (groupIndex) => {
		const { showDtPanel } = this.state;
		let hasOpen = false;
		if (showDtPanel[groupIndex]) {
			hasOpen = true;
		}
		this.setState({
			showDtPanel: {
				[groupIndex]: !hasOpen
			}
		});
	}
	render() {
		let { solutionStore, dispatch, globalStore: { personalMode: { lang } } } = this.props;
		let { customTree, currentSolutionIndex } = solutionStore;
		let currentSolution = customTree && customTree.length ? customTree[currentSolutionIndex] : {};
		const { showDtPanel } = this.state;
		return (
			<div className="fix-height">
				<div className="register-box-header">
					<h3 className="action">
						{
							lang === "cn" ? currentSolution.name : currentSolution.enName
						}
						<Tooltip title={solutionLang.common("watchSolution")}>
							<Icon
								type="info-circle"
								className="register-info-circle"
								onClick={() => {
									dispatch({
										type: "solution/setDialogShow",
										payload: {
											modifySolution: true
										}
									});
									dispatch({
										type: "solution/setDialogData",
										payload: {
											modifySolutionData: {
												name: currentSolution.name,
												enName: currentSolution.enName,
												icon: currentSolution.icon,
												path: currentSolution.path,
												uuid: currentSolution.uuid,
												code: currentSolution.code
											}
										}
									});
								}}
							/>
						</Tooltip>
					</h3>
					<div className="btns">
						<Button
							size="small"
							icon="plus"
							type="primary"
							onClick={() => {
								this.addSolutionGroup(currentSolution.uuid);
							}}
						>
							{solutionLang.common("addGroup")}
						</Button>
						<Button
							size="small"
							onClick={this.saveSolution}
						>
							{solutionLang.common("save")}
						</Button>
					</div>
				</div>
				<div className="register-box-body">
					<div className="solution-custom-wrap">
						{
							currentSolution.children &&
							currentSolution.children.map((item, index) => {
								return (
									<BinItem
										showDtPanel={showDtPanel[index]}
										collapseDt={this.collapseDt}
										groupData={item}
										groupIndex={index}
										key={index}
									/>
								);
							})
						}
					</div>
				</div>
				<AddGroupModal />
				<ModifyGroupModal />
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	solutionStore: state.solution
}))(Custom);
