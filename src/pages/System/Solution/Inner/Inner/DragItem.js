import { PureComponent } from "react";
import { connect } from "dva";
import { DragSource } from "react-dnd";

import solutionLang from "@/constants/lang/solution";
const dataSource = {
	beginDrag(props) {
		console.log(props);
		return { ...props };
	}
};

function collect(connect, monitor) {
	return {
		connectDragSource: connect.dragSource(),
		connectDragPreview: connect.dragPreview(),
		isDragging: monitor.isDragging()
	};
}

@DragSource("card", dataSource, collect)
class DragItem extends PureComponent {
	componentDidMount() { }

	render() {
		const { globalStore: { personalMode: { lang } } } = this.props;
		let { connectDragSource, solutionStore, node } = this.props;
		let { customTree } = solutionStore;
		let inGroupNameList = [];

		customTree &&
            customTree.length > 0 &&
            customTree[0]["children"] &&
            customTree[0]["children"].map((item, index) => {
            	item.children && item.children.map((subItem, subIndex) => {
            		if (subItem.code === node.code) {
            			inGroupNameList.push(
            				lang === "cn" ? item.name : item.enName
            			);
            		}
            	});
            });
		return connectDragSource(
			<dd>
				<span className="menu-title">
					{lang === "cn" ? node.name : node.enName}
				</span>
				{inGroupNameList.length > 0 && (
					<span className="has-in-group">
						{solutionLang.common("ingroup")} :{" "}
						{inGroupNameList.join("、")}
					</span>
				)}
			</dd>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	solutionStore: state.solution
}))(DragItem);
