import { PureComponent } from "react";
import { connect } from "dva";
import { DropTarget } from "react-dnd";
import { Popconfirm, message, Icon, Popover } from "antd";
import { solutionAPI } from "@/services";
import solutionLang from "@/constants/lang/solution";

const canvasTarget = {
	drop(props, monitor) {
		let { solutionStore, dispatch, collapseDt, showDtPanel, groupIndex } = props;
		console.log(props);
		let { customTree, currentSolutionIndex } = solutionStore;
		let item = monitor.getItem();
		let source = item.node;
		let targetGroupIndex = props["groupIndex"];
		// 校验是否已经存在此菜单
		let hasThisMenu = false;

		if (customTree[currentSolutionIndex]["children"]) {
			customTree[currentSolutionIndex]["children"].forEach(function(item) {
				if (item["children"]) {
					item["children"].forEach(function(item2) {
						if (item2.uuid === source.uuid) {
							hasThisMenu = true;
						}
					});
				}
			});
		}

		if (hasThisMenu) {
			message.error("此菜单已存在");
			return;
		}

		// 如果左侧未展开则展开
		if (!showDtPanel) {
			collapseDt(groupIndex);
		}
		if (customTree[currentSolutionIndex]["children"][targetGroupIndex]["children"]) {
			customTree[currentSolutionIndex]["children"][targetGroupIndex]["children"].push(source);
		} else {
			let children = [source];
			customTree[currentSolutionIndex]["children"][targetGroupIndex][
				"children"
			] = children;
		}

		dispatch({
			type: "solution/setAttrValue",
			payload: {
				customTree: customTree
			}
		});
	}
};

@DropTarget("card", canvasTarget, (connect, monitor) => ({
	connectDropTarget: connect.dropTarget(),
	canDrop: monitor.canDrop(),
	isOver: monitor.isOver()
}))
class BinItem extends PureComponent {
	constructor(props) {
		super(props);
	}

    modifySolutionGroup = (groupData) => {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "solution/setDialogData",
    		payload: {
    			modifyGroupData: {
    				name: groupData.name,
    				enName: groupData.enName,
    				code: groupData.code,
    				uuid: groupData.uuid,
    				flag: 1,
    				icon: groupData.icon
    			}
    		}
    	});
    	dispatch({
    		type: "solution/setDialogShow",
    		payload: {
    			modifySolutionGroup: true
    		}
    	});
    }

    deleteCustomMenu = (menuIndex) => {
    	let { solutionStore, dispatch, groupIndex } = this.props;
    	let { customTree, currentSolutionIndex } = solutionStore;

    	customTree[currentSolutionIndex]["children"][groupIndex]["children"].splice(menuIndex, 1);

    	dispatch({
    		type: "solution/setAttrValue",
    		payload: {
    			customTree: customTree
    		}
    	});
    }

    deleteSolutionGroup = (item) => {
    	let { dispatch } = this.props;

    	solutionAPI.deleteSolutionGroup(item.uuid).then(res => {
    		if (res.success) {
    			message.success(solutionLang.common("success"));
    			dispatch({
    				type: "solution/getSolution",
    				payload: {}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    sortGroupData = (index, sortType, listType) => {
    	let { groupData, dispatch, groupIndex, solutionStore } = this.props;
    	let { customTree, currentSolutionIndex } = solutionStore;

    	if (listType === "groupDataChildren") {
    		if (sortType === "down") {
    			groupData.children[index] = groupData.children.splice(index + 1, 1, groupData.children[index])[0];
    		} else if (sortType === "up") {
    			groupData.children[index] = groupData.children.splice(index - 1, 1, groupData.children[index])[0];
    		}
    		customTree[currentSolutionIndex]["children"][groupIndex]["children"] = groupData.children;
    	} else if (listType === "groupData") {
    		if (sortType === "down") {
    			customTree[currentSolutionIndex].children[index] = customTree[currentSolutionIndex].children.splice(index + 1, 1, customTree[currentSolutionIndex].children[index])[0];
    		} else if (sortType === "up") {
    			customTree[currentSolutionIndex].children[index] = customTree[currentSolutionIndex].children.splice(index - 1, 1, customTree[currentSolutionIndex].children[index])[0];
    		}
    		customTree[currentSolutionIndex]["children"] = customTree[currentSolutionIndex].children;
    	}
    	dispatch({
    		type: "solution/setAttrValue",
    		payload: {
    			customTree: customTree
    		}
    	});
    }

    render() {
    	let { groupData, groupIndex, solutionStore, showDtPanel, collapseDt } = this.props;
    	let { customTree, currentSolutionIndex } = solutionStore;
    	let { accepts, isOver, canDrop, connectDropTarget, globalStore: { personalMode: { lang } } } = this.props;
    	let isActive = isOver && canDrop;

    	let cStyle = " ";
    	cStyle = accepts ? cStyle + " accepts" : "";
    	cStyle = isOver ? cStyle + " isOver" : "";
    	cStyle = canDrop ? cStyle + " canDrop" : "";

    	let createByCustomer = true;
    	if (groupData.createBy && groupData.createBy === "system") {
    		createByCustomer = false;
    	}

    	let groupDataHei = 0;
    	if (!showDtPanel) {
    		cStyle = cStyle + " border-btm-none";
    		groupDataHei = 0;
    	} else {
    		if (groupData.children && groupData.children.length > 0) {
    			groupDataHei = groupData.children.length * 40;
    		}
    		if (!(groupData.children && groupData.children.length > 0)) {
    			groupDataHei = 60;
    		}
    	}
    	return (
    		connectDropTarget &&
            connectDropTarget(
            	<dl
            		className={"solution-custom-group" + cStyle}
            		key={groupIndex}
            	>
            		<dt onClick={() => collapseDt(groupIndex)}>
            			<Icon type={`caret-${showDtPanel ? "up" : "down"}`} className="icon-caret" />
            			<i
            				className={groupData.icon ? "iconfont iconfont-title icon-" + groupData.icon : "iconfont iconfont-title icon-pingfen"}
            			/>
            			<span>
            				{lang === "cn" ? groupData.name : groupData.enName}
            			</span>
            			<div className="oper-list">
            				{
            					groupIndex !== 0 &&
                                <Icon
                                	type="arrow-up"
                                	onClick={(e) => {
                                		e.stopPropagation();
                                		// collapseDt(groupIndex);
                                		this.sortGroupData(groupIndex, "up", "groupData");
                                	}}
                                />
            				}
            				{
            					groupIndex !== customTree[currentSolutionIndex]["children"].length - 1 &&
                                <Icon
                                	type="arrow-down"
                                	onClick={(e) => {
                                		e.stopPropagation();
                                		// collapseDt(groupIndex);
                                		this.sortGroupData(groupIndex, "down", "groupData");
                                	}}
                                />
            				}
            				{
            					createByCustomer &&
                                <Popconfirm
                                	title={solutionLang.common("deleteConfirm")}
                                	onClick={(e) => {
                                		e.stopPropagation();
                                	}}
                                	onConfirm={() => {
                                		this.deleteSolutionGroup(groupData);
                                	}}
                                	onCancel={() => { }}
                                	okText={solutionLang.common("delete")}
                                	cancelText={solutionLang.common("cancel")}
                                >
                                	<Icon type="delete" />
                                </Popconfirm>
            				}
            				<Popover
            					placement="left"
            					onClick={
            						(e) => {
            							e.stopPropagation();
            						}
            					}
            					content={
            						<div>
            							<p>
            								{solutionLang.common("viewName")}：
            								{lang === "cn" ? groupData.name : groupData.enName}
            							</p>
            							<p>
            								{solutionLang.common("uniqTag")}：
            								{groupData.code}
            							</p>
            						</div>
            					}
            					title={solutionLang.common("config")}>
            					<Icon type="profile" />
            				</Popover>
            				{
            					createByCustomer &&
                                <Icon
                                	type="edit"
                                	onClick={(e) => {
                                		e.stopPropagation();
                                		this.modifySolutionGroup(groupData);
                                	}}
                                />
            				}
            			</div>
            		</dt>
            		<div
            			className="group-data-wrap"
            			style={{ "height": groupDataHei + "px" }}
            		>
            			{
            				groupData.children &&
                            groupData.children.length > 0 &&
                            groupData.children.map((item, index) => {
                            	let menuCreateByCustomer = true;
                            	if (item.createBy && item.createBy === "system") {
                            		menuCreateByCustomer = false;
                            	}

                            	let dlObj = (
                            		<dd key={index}>
                            			<span>
                            				{lang === "cn" ? item.name : item.enName}
                            			</span>
                            			<div className="oper-list">
                            				{
                            					index !== 0 &&
                                                <Icon
                                                	type="arrow-up"
                                                	onClick={(e) => {
                                                		e.stopPropagation();
                                                		this.sortGroupData(index, "up", "groupDataChildren");
                                                	}}
                                                />
                            				}
                            				{
                            					index !==
                                                groupData.children.length - 1 &&
                                                <Icon
                                                	type="arrow-down"
                                                	onClick={(e) => {
                                                		e.stopPropagation();
                                                		this.sortGroupData(index, "down", "groupDataChildren");
                                                	}}
                                                />
                            				}
                            				{
                            					menuCreateByCustomer &&
                                                <Popconfirm
                                                	title={solutionLang.common("deleteConfirm")}
                                                	onConfirm={(e) => {
                                                		e.stopPropagation();
                                                		this.deleteCustomMenu(index);
                                                	}}
                                                	onCancel={() => { }}
                                                	okText={solutionLang.common("delete")}
                                                	cancelText={solutionLang.common("cancel")}
                                                >
                                                	<Icon type="delete" />
                                                </Popconfirm>
                            				}
                            				<Popover
                            					placement="left"
                            					content={
                            						<div>
                            							<p>
                            								{solutionLang.common("viewName")}{" "}
                                                            :{" "}
                            								{lang === "cn" ? item.name : item.enName}
                            							</p>
                            							<p>
                            								{solutionLang.common("uniqTag")}{" "}: {item.code}
                            							</p>
                            							<p>icon：{item.icon}</p>
                            							<p>path：{item.path}</p>
                            						</div>
                            					}
                            					title={solutionLang.common("config")}
                            				>
                            					<Icon type="profile" />
                            				</Popover>
                            			</div>
                            		</dd>
                            	);
                            	return dlObj;
                            })
            			}
            			{
            				!(groupData.children && groupData.children.length > 0) &&
                            <div className="none-data" style={{ marginTop: 0 }}>
                            	{solutionLang.common("noMenuGroup")}
                            </div>
            			}
            		</div>
            	</dl>
            )
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	solutionStore: state.solution
}))(BinItem);
