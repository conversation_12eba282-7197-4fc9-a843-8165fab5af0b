import { PureComponent } from "react";
import { connect } from "dva";
import { DragSource } from "react-dnd";
import { Tabs, Empty, Icon } from "antd";
import { cloneDeep } from "lodash";
import DragItem from "./Inner/DragItem";
import { commonImages } from "@/constants/images";
import solutionLang from "@/constants/lang/solution";

const TabPane = Tabs.TabPane;

const dataSource = {
	beginDrag(props) {
		console.log(props);
		return {
			id: 22
		};
	}
};

function collect(connect, monitor) {
	return {
		connectDragSource: connect.dragSource(),
		connectDragPreview: connect.dragPreview(),
		isDragging: monitor.isDragging()
	};
}

@DragSource("card", dataSource, collect)
class System extends PureComponent {
	constructor(props) {
		super(props);
		this.state = {
			showDtPanel: {
				"0": {
					"0": true
				}
			}
		};
	}

    toggleArrow = (tabIndex, groupIndex) => {
    	const { showDtPanel } = this.state;
    	let newShowDtPanel = cloneDeep(showDtPanel);
    	let hasOpen = false;
    	if (newShowDtPanel[tabIndex] && newShowDtPanel[tabIndex][groupIndex]) {
    		hasOpen = true;
    	}
    	newShowDtPanel = {
    		...newShowDtPanel,
    		[tabIndex]: {
    			[groupIndex]: !hasOpen
    		}
    	};
    	this.setState({
    		showDtPanel: newShowDtPanel
    	});
    }
    renderGroupMenu = (group, tabIndex) => {
    	let { globalStore: { personalMode: { lang } } } = this.props;
    	const { showDtPanel } = this.state;
    	return (
    		group.children &&
            group.children.map((groupItem, groupIndex) => {
            	const currentShowPanel = showDtPanel[tabIndex][groupIndex];
            	let [groupItemHei, dtTitle] = [0, ""];
            	if (currentShowPanel) {
            		if (groupItem.children && groupItem.children.length > 0) {
            			groupItemHei = groupItem.children.length * 40;
            		}
            	} else {
            		dtTitle = "no-btm-border";
            	}
            	let dlObj = (
            		<dl className="solution-register-group" key={groupIndex}>
            			<dt className={dtTitle} onClick={() => { this.toggleArrow(tabIndex, groupIndex); }}>
            				<Icon type={`caret-${currentShowPanel ? "up" : "down"}`} className="icon-caret" />
            				<i className={groupItem.icon ? "iconfont icon-" + groupItem.icon : "iconfont icon-pingfen"} />
            				{lang === "cn" ? groupItem.name : groupItem.enName}
            			</dt>
            			<div
            				className="solution-group-wrap"
            				style={{ "height": groupItemHei + "px" }}
            			>
            				{
            					groupItem.children &&
                                groupItem.children.map((menuItem, menuIndex) => {
                                	return (
                                		<DragItem node={menuItem} key={menuIndex} />
                                	);
                                })
            				}
            			</div>
            		</dl>
            	);
            	return dlObj;
            })
    	);
    }

    render() {
    	let { solutionStore, globalStore: { personalMode: { lang } } } = this.props;
    	let { registerList } = solutionStore;
    	return (
    		<div className="fix-height">
    			{
    				registerList &&
                    registerList.length === 0 && (
    					<Empty
    						image={commonImages.empty}
    						imageStyle={{
    							height: 60
    						}}
    						style={{ paddingTop: "100px" }}
    						description={
    							<span>{solutionLang.common("noCustomMenu")}</span>
    						}
    					/>
    				)
    			}
    			{
    				registerList &&
                    registerList.length > 0 && (
    					<Tabs
    						className="register-origin-tabs"
    						defaultActiveKey="0"
    						onChange={() => {

    						}}
    					>
    						{
    							registerList.map((item, index) => {
    								let oneTabPane = (
    									<TabPane
    										className="solution-register-main"
    										tab={lang === "cn" ? item.name : item.enName}
    										key={index}
    									>
    										{
    											item.children && this.renderGroupMenu(item, index)
    										}
    									</TabPane>
    								);
    								return oneTabPane;
    							})
    						}
    					</Tabs>
    				)
    			}
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	solutionStore: state.solution
}))(System);
