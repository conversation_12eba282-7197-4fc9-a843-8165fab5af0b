import { PureComponent } from "react";
import { connect } from "dva";
import { Form, Input, message, Modal } from "antd";
import { solutionAPI } from "@/services";
import string from "@/utils/string";
import iconConstants from "@/constants/icon";

const FormItem = Form.Item;

class AddGroupModal extends PureComponent {

	constructor(props) {
		super(props);
	}

    checkParams = (addGroupData) => {
    	if (string.isBlank(addGroupData.name)) {
    		message.error("中文名称不能为空！");
    		return false;
    	}
    	if (string.isBlank(addGroupData.enName)) {
    		message.error("英文名称不能为空！");
    		return false;
    	}
    	if (string.isBlank(addGroupData.code)) {
    		message.error("唯一标识不能为空！");
    		return false;
    	}

    	let rx = new RegExp("^[0-9a-zA-Z]+$");

    	if (!rx.test(addGroupData.code)) {
    		message.error("唯一标识必须为英文或数字");
    		return false;
    	}
    	return true;
    }

    submitModal = () => {
    	let { solutionStore, dispatch } = this.props;
    	let { dialogData } = solutionStore;
    	let { addGroupData } = dialogData;

    	if (!this.checkParams(addGroupData)) {
    		return false;
    	}

    	let params = {
    		code: addGroupData.code,
    		name: addGroupData.name,
    		enName: addGroupData.enName,
    		icon: addGroupData.icon,
    		type: 2,
    		flag: 1,
    		parentUuid: addGroupData.parentUuid
    	};
    	solutionAPI.addSolutionGroup(params).then((res) => {
    		if (res.success) {
    			dispatch({
    				type: "solution/getSolution",
    				payload: {}
    			});
    			message.success("新增分组成功");
    			dispatch({
    				type: "solution/setDialogShow",
    				payload: {
    					addSolutionGroup: false
    				}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    closeModal = () => {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "solution/setDialogShow",
    		payload: {
    			addSolutionGroup: false
    		}
    	});
    	dispatch({
    		type: "register/setDialogData",
    		payload: {
    			addGroupData: {
    				name: null,
    				code: null
    			}
    		}
    	});
    }

    changeFiled = (field, e) => {
    	let { solutionStore, dispatch } = this.props;
    	let { addGroupData } = solutionStore.dialogData;
    	let value = null;
    	if (field === "icon") {
    		value = e;
    	} else {
    		value = e.target.value;
    	}
    	addGroupData[field] = value;
    	dispatch({
    		type: "solution/setDialogData",
    		payload: {
    			addGroupData: addGroupData
    		}
    	});
    }

    render() {
    	let { solutionStore } = this.props;
    	let { dialogShow, dialogData } = solutionStore;
    	let { addGroupData } = dialogData;
    	let formItemLayout = {
    		labelCol: { span: 6 },
    		wrapperCol: { span: 16 }
    	};

    	return (
    		<Modal
    			title="新增解决方案分组"
    			wrapClassName="vertical-center-modal"
    			visible={dialogShow.addSolutionGroup}
    			maskClosable={false}
    			className="report-modal"
    			onOk={this.submitModal}
    			onCancel={this.closeModal}
    		>
    			<Form layout="horizontal">
    				<FormItem
    					label="分组中文名称"
    					{...formItemLayout}
    				>
    					<Input name="workflowName" value={addGroupData.name || undefined}
    						onChange={(e) => {
    							this.changeFiled("name", e);
    						}}
    						placeholder="请输入分组中文名称" />
    					<span className="require">*</span>
    				</FormItem>
    				<FormItem
    					label="分组英文名称"
    					{...formItemLayout}
    				>
    					<Input name="workflowName" value={addGroupData.enName || undefined}
    						onChange={(e) => {
    							this.changeFiled("enName", e);
    						}}
    						placeholder="请输入分组英文名称" />
    					<span className="require">*</span>
    				</FormItem>
    				<FormItem
    					label="唯一标识"
    					{...formItemLayout}
    				>
    					<Input name="workflowCode" value={addGroupData.code || undefined}
    						onChange={(e) => {
    							this.changeFiled("code", e);
    						}}
    						placeholder="请输入唯一标识" />
    					<span className="require">*</span>
    				</FormItem>
    				<FormItem
    					label="图标"
    					{...formItemLayout}
    				>
    					<div className="icon-show-wrap">
    						<ul className="icon-show-list">
    							{
    								iconConstants.groupIcons.map((item, index) => {
    									return (
    										<li
    											className={addGroupData.icon === item.name ? "icon-show-item active" : "icon-show-item"}
    											onClick={() => {
    												this.changeFiled("icon", item.name);
    											}}
    											key={index}
    										>
    											<i className={"iconfont icon-" + item.name}></i>
    										</li>
    									);
    								})
    							}
    						</ul>
    					</div>
    				</FormItem>
    			</Form>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	solutionStore: state.solution
}))(AddGroupModal);
