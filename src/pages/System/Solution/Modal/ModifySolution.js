import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, Form, message } from "antd";
import { solutionAPI } from "@/services";
import string from "@/utils/string";
import solutionLang from "@/constants/lang/solution";
const FormItem = Form.Item;

class ModifySolution extends PureComponent {

	constructor(props) {
		super(props);
	}

    checkParams = (modifySolutionData) => {
    	if (string.isBlank(modifySolutionData.name)) {
    		message.error(solutionLang.dialog("blankName"));
    		return false;
    	}
    	if (string.isBlank(modifySolutionData.enName)) {
    		message.error(solutionLang.dialog("blankEnName"));
    		return false;
    	}
    	if (string.isBlank(modifySolutionData.icon)) {
    		message.error(solutionLang.dialog("blankIcon"));
    		return false;
    	}
    	let rm = new RegExp("^[a-zA-Z]+$");
    	let reg = new RegExp("^[\u2E80-\u9FFF]+$");

    	if (!reg.test(modifySolutionData.name)) {
    		message.error(solutionLang.dialog("errorChinese"));
    		return false;
    	}
    	if (!rm.test(modifySolutionData.enName)) {
    		message.error(solutionLang.dialog("blankEnName"));
    		return false;
    	}
    	return true;
    }

    modifySolutionHandle = () => {
    	let { solutionStore, dispatch } = this.props;
    	let { dialogData } = solutionStore;
    	let { modifySolutionData } = dialogData;

    	if (!this.checkParams(modifySolutionData)) {
    		return;
    	}

    	let params = {
    		uuid: modifySolutionData.uuid,
    		name: modifySolutionData.name,
    		enName: modifySolutionData.enName,
    		icon: modifySolutionData.icon,
    		path: modifySolutionData.path,
    		code: modifySolutionData.code
    	};

    	solutionAPI.modifySolution(params).then(res => {
    		if (res.success) {
    			dispatch({
    				type: "solution/getSolution",
    				payload: {}
    			});
    			message.success(solutionLang.common("success"));
    			dispatch({
    				type: "solution/setDialogShow",
    				payload: {
    					modifySolution: false
    				}
    			});
    			dispatch({
    				type: "global/getUserMenuTree",
    				payload: {}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    changeFiled = (field, type, e) => {
    	let { solutionStore, dispatch } = this.props;
    	let { modifySolutionData } = solutionStore.dialogData;
    	let value;

    	if (type === "input") {
    		value = e.target.value;
    	} else if (type === "select") {
    		value = e;
    	}
    	modifySolutionData[field] = value;

    	dispatch({
    		type: "solution/setDialogData",
    		payload: {
    			modifySolutionData: modifySolutionData
    		}
    	});
    }

    render() {
    	let { solutionStore, dispatch } = this.props;
    	let { dialogShow, dialogData } = solutionStore;
    	let { modifySolutionData } = dialogData;
    	let formItemLayout = {
    		labelCol: { span: 6 },
    		wrapperCol: { span: 16 }
    	};

    	return (
    		<Modal
    			title={solutionLang.common("watchSolution")}
    			visible={dialogShow.modifySolution}
    			maskClosable={true}
    			onCancel={() => {
    				dispatch({
    					type: "solution/setDialogShow",
    					payload: {
    						modifySolution: false
    					}
    				});
    			}}
    			footer={null}
    			onOk={this.modifySolutionHandle}
    		>
    			<Form layout="horizontal">
    				<FormItem
    					label={solutionLang.common("chineseName")}
    					{...formItemLayout}
    				>
    					<Input
    						value={modifySolutionData.name || undefined}
    						onChange={(e) => {
    							this.changeFiled("name", "input", e);
    						}}
    						placeholder={solutionLang.common("chineseNamePlaceholder")}
    						disabled={true}
    					/>
    					<span className="require">*</span>
    				</FormItem>
    				<FormItem
    					label={solutionLang.common("englishName")}
    					{...formItemLayout}
    				>
    					<Input
    						value={modifySolutionData.enName || undefined}
    						onChange={(e) => {
    							this.changeFiled("enName", "input", e);
    						}}
    						placeholder={solutionLang.common("englishNamePlaceholder")}
    						disabled={true}
    					/>
    					<span className="require">*</span>
    				</FormItem>
    				<FormItem
    					label={solutionLang.common("jumpPath")}
    					{...formItemLayout}
    				>
    					<Input
    						value={modifySolutionData.path || undefined}
    						onChange={(e) => {
    							this.changeFiled("path", "input", e);
    						}}
    						placeholder={solutionLang.common("jumpPathPlaceholder")}
    						disabled={true}
    					/>
    				</FormItem>
    				<FormItem
    					label={solutionLang.common("logoName")}
    					{...formItemLayout}
    				>
    					<Input
    						value={modifySolutionData.icon || undefined}
    						onChange={(e) => {
    							this.changeFiled("icon", "input", e);
    						}}
    						placeholder={solutionLang.common("logoNamePlaceholder")}
    						disabled={true}
    					/>
    				</FormItem>
    			</Form>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	solutionStore: state.solution
}))(ModifySolution);
