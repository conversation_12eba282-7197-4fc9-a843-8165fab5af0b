import { PureComponent } from "react";
import { connect } from "dva";
import { Form, Input, message, Modal } from "antd";
import { solutionAPI } from "@/services";
import string from "@/utils/string";
import iconConstants from "@/constants/icon";

const FormItem = Form.Item;

class ModifyGroupModal extends PureComponent {
	constructor(props) {
		super(props);
	}

    checkParams = (modifyGroupData) => {
    	if (string.isBlank(modifyGroupData.name)) {
    		message.error("中文名称不能为空！");
    		return false;
    	}
    	if (string.isBlank(modifyGroupData.enName)) {
    		message.error("英文名称不能为空！");
    		return false;
    	}
    	if (string.isBlank(modifyGroupData.code)) {
    		message.error("唯一标识不能为空！");
    		return false;
    	}

    	let rm = new RegExp("^[a-zA-Z ]+$");
    	let reg = new RegExp("^[\u2E80-\u9FFF]+$");
    	let rx = new RegExp("^[0-9a-zA-Z]+$");

    	if (!reg.test(modifyGroupData.name)) {
    		message.error("中文名称必须为中文");
    		return false;
    	}
    	if (!rm.test(modifyGroupData.enName)) {
    		message.error("英文名称必须为英文");
    		return false;
    	}
    	if (!rx.test(modifyGroupData.code)) {
    		message.error("唯一标识必须为英文或数字");
    		return false;
    	}
    	return true;
    }

    submitModal = () => {
    	let { solutionStore, dispatch } = this.props;
    	let { dialogData } = solutionStore;
    	let { modifyGroupData } = dialogData;

    	if (!this.checkParams(modifyGroupData)) {
    		return false;
    	}

    	let params = {
    		code: modifyGroupData.code,
    		name: modifyGroupData.name,
    		enName: modifyGroupData.enName,
    		uuid: modifyGroupData.uuid,
    		flag: modifyGroupData.flag,
    		icon: modifyGroupData.icon
    	};

    	solutionAPI.modifySolutionGroup(params).then((res) => {
    		if (res.success) {
    			dispatch({
    				type: "solution/getSolution",
    				payload: {}
    			});
    			message.success("修改分组成功");
    			dispatch({
    				type: "solution/setDialogShow",
    				payload: {
    					modifySolutionGroup: false
    				}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    closeModal = () => {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "solution/setDialogShow",
    		payload: {
    			modifySolutionGroup: false
    		}
    	});
    	dispatch({
    		type: "solution/setDialogData",
    		payload: {
    			modifyGroupData: {
    				name: null,
    				code: null,
    				uuid: null,
    				flag: 1
    			}
    		}
    	});
    }

    changeFiled = (field, e) => {
    	let { solutionStore, dispatch } = this.props;
    	let { modifyGroupData } = solutionStore.dialogData;
    	let value = null;
    	if (field === "icon") {
    		value = e;
    	} else {
    		value = e.target.value;
    	}
    	modifyGroupData[field] = value;

    	dispatch({
    		type: "solution/setDialogData",
    		payload: {
    			modifyGroupData: modifyGroupData
    		}
    	});
    }

    render() {
    	let { solutionStore } = this.props;
    	let { dialogShow, dialogData } = solutionStore;
    	let { modifyGroupData } = dialogData;
    	let formItemLayout = {
    		labelCol: { span: 6 },
    		wrapperCol: { span: 16 }
    	};

    	return (
    		<Modal
    			title="修改解决方案分组"
    			wrapClassName="vertical-center-modal"
    			visible={dialogShow.modifySolutionGroup}
    			maskClosable={false}
    			className="report-modal"
    			onOk={this.submitModal}
    			onCancel={this.closeModal}
    		>
    			<Form layout="horizontal">
    				<FormItem
    					label="分组中文名称"
    					{...formItemLayout}
    				>
    					<Input value={modifyGroupData.name || undefined}
    						onChange={(e) => {
    							this.changeFiled("name", e);
    						}}
    						placeholder="请输入分组中文名称" />
    					<span className="require">*</span>
    				</FormItem>
    				<FormItem
    					label="分组英文名称"
    					{...formItemLayout}
    				>
    					<Input
    						value={modifyGroupData.enName || undefined}
    						onChange={(e) => {
    							this.changeFiled("enName", e);
    						}}
    						placeholder="请输入分组英文名称"
    					/>
    					<span className="require">*</span>
    				</FormItem>
    				<FormItem
    					label="唯一标识"
    					{...formItemLayout}
    				>
    					<Input value={modifyGroupData.code || undefined}
    						onChange={(e) => {
    							this.changeFiled("code", e);
    						}}
    						placeholder="请输入唯一标识" />
    					<span className="require">*</span>
    				</FormItem>
    				<FormItem
    					label="图标"
    					{...formItemLayout}
    				>
    					<div className="icon-show-wrap">
    						<ul className="icon-show-list">
    							{
    								iconConstants.groupIcons.map((item, index) => {
    									return (
    										<li
    											className={modifyGroupData.icon === item.name ? "icon-show-item active" : "icon-show-item"}
    											onClick={() => {
    												this.changeFiled("icon", item.name);
    											}}
    											key={index}
    										>
    											<i className={"iconfont icon-" + item.name}></i>
    										</li>
    									);
    								})
    							}
    						</ul>
    					</div>
    				</FormItem>
    			</Form>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	solutionStore: state.solution
}))(ModifyGroupModal);
