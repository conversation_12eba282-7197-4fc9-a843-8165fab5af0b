import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { message } from "antd";
import { systemAPI } from "@/services";
import { DragDropContext } from "react-dnd";
import HTML5Backend from "react-dnd-html5-backend";
import SolutionCustom from "./Inner/Custom";
import SolutionSystem from "./Inner/System";
import ModifySolution from "./Modal/ModifySolution";
import solutionLang from "@/constants/lang/solution";

@DragDropContext(HTML5Backend)
class Solution extends PureComponent {

	constructor(props) {
		super(props);
		this.getRegisterList = this.getRegisterList.bind(this);
	}

	componentDidMount() {
		let { dispatch } = this.props;
		dispatch({
			type: "solution/getSolution",
			payload: {}
		});
		this.getRegisterList();
	}

	getRegisterList() {
		let { dispatch } = this.props;

		systemAPI.getSystemRegister().then(res => {
			if (res.success) {
				dispatch({
					type: "solution/setAttrValue",
					payload: {
						registerList: res.data || []
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		return (
			<Fragment>
				<div className="page-global-header">
					<div className="left-info">
						<h2>{solutionLang.common("solution")}</h2>
					</div>
				</div>
				<div className="page-global-body">
					<div className="system-register">
						<div className="system-register-body">
							<div className="register-box register-box-5">
								<SolutionCustom />
							</div>
							<div className="register-box register-box-6">
								<SolutionSystem />
							</div>
						</div>
					</div>
				</div>
				<ModifySolution />
			</Fragment>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	solutionStore: state.solution
}))(Solution);
