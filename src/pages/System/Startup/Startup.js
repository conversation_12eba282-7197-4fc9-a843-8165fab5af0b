import { PureComponent } from "react";
import { connect } from "dva";
import Startup from "@/components/Startup";
import startUpLang from "@/constants/lang/startup";

class SysStartup extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    }

    render() {
    	let { location } = this.props;

    	return (
    		<div>
    			<div className="page-global-header">
    				<div className="left-info">
    					<h2>{startUpLang.common("systermConfig")}</h2>
    				</div>
    			</div>
    			<div className="page-global-body">
    				<Startup isInit={false} location={location} />
    			</div>
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	startupStore: state.startup
}))(SysStartup);
