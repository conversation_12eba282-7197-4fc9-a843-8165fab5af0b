import { PureComponent } from "react";
import { connect } from "dva";
import { Card } from "antd";
import SystemList from "./Inner/SystemList";

class WhiteApiList extends PureComponent {

	constructor(props) {
		super(props);
	}

	componentDidMount() {
		let { dispatch } = this.props;
		dispatch({
			type: "register/getSystemRegister",
			payload: {}
		});
	}

	render() {
		return (
			<div>
				<Card className="fix-card">
					<div className="system-register">
						<div className="system-register-header">
							<h2>API白名单</h2>
						</div>
						<div className="system-register-body">
							<div className="register-box register-box-1">
								<SystemList />
							</div>
							<div className="">

							</div>
						</div>
					</div>
				</Card>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	registerStore: state.register
}))(WhiteApiList);

