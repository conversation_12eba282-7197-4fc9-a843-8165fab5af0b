import { PureComponent } from "react";
import { connect } from "dva";

class SystemList extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    }
    render() {
    	let { registerStore, dispatch } = this.props;
    	let { systemRegister, whiteApiList } = registerStore;
    	let { registerList } = systemRegister;
    	let { activeSystemIndex } = whiteApiList;

    	return (
    		<div style={{ position: "relative", height: "100%" }}>
    			<div className="register-box-header">
    				<h3>系统列表</h3>
    			</div>
    			<div className="register-box-body">
    				<ul className="system-menu-list">
    					{
    						registerList.map((item, index) => {
    							return (
    								<li
    									className={activeSystemIndex === index ? "system-menu-item active" : "system-menu-item"}
    									onClick={() => {
    										if (activeSystemIndex !== index) {
    											dispatch({
    												type: "register/setAttrValue",
    												payload: {
    													whiteApiList: {
    														activeSystemIndex: index
    													}
    												}
    											});
    										}
    									}}
    									key={index}
    								>
    									<div className="system-menu-title">
    										<span>{item.name}</span>
    									</div>
    								</li>
    							);
    						})
    					}
    				</ul>
    			</div>

    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	registerStore: state.register
}))(SystemList);

