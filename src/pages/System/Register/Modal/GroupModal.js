import { PureComponent } from "react";
import { connect } from "dva";
import { Form, Input, message, Modal } from "antd";
import { iconConstants } from "@/constants";
import { systemAPI } from "@/services";
import string from "@/utils/string";
import registerLang from "@/constants/lang/register";
const FormItem = Form.Item;

message.config({
	maxCount: 1
});

class Function extends PureComponent {

	constructor(props) {
		super(props);
		this.changeFile = this.changeFiled.bind(this);
		this.addGroupHandle = this.addGroupHandle.bind(this);
		this.modifyGroupHandle = this.modifyGroupHandle.bind(this);
	}

	addGroupHandle() {
		let { registerStore, dispatch } = this.props;
		let { systemRegister, dialogData } = registerStore;
		let { groupData } = dialogData;
		let { registerList, activeSystemIndex } = systemRegister;
		let uuid = registerList[activeSystemIndex]["uuid"];

		if (!this.checkParams(groupData)) {
			return false;
		}

		this.checkParams(groupData);

		let params = {
			code: groupData.code,
			name: groupData.name,
			enName: groupData.enName,
			icon: groupData.icon,
			level: 2,
			parentUuid: uuid
		};
		systemAPI.addSystemRegisterMenu(params).then(res => {
			if (res.success) {
				dispatch({
					type: "register/getSystemRegister",
					payload: {}
				});
				message.success(registerLang.common("success"));
				dispatch({
					type: "register/setDialogShow",
					payload: {
						addGroup: false
					}
				});
				dispatch({
					type: "register/setDialogData",
					payload: {
						groupData: {
							name: null,
							enName: null,
							code: null
						}
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	checkParams(groupData) {
		if (string.isBlank(groupData.name)) {
			message.error(registerLang.dialog("blankName"));
			return false;
		}
		if (string.isBlank(groupData.enName)) {
			message.error(registerLang.dialog("blankEnName"));
			return false;
		}
		if (string.isBlank(groupData.code)) {
			message.error(registerLang.dialog("blankCode"));
			return false;
		}

		let rx = new RegExp("^[0-9a-zA-Z]+$");

		if (!rx.test(groupData.code)) {
			message.error(registerLang.dialog("codeError"));
			return false;
		}
		return true;
	}

	modifyGroupHandle() {
		let { registerStore, dispatch } = this.props;
		let { dialogData } = registerStore;
		let { groupData } = dialogData;

		if (!this.checkParams(groupData)) {
			return false;
		}

		let params = {
			code: groupData.code,
			name: groupData.name,
			enName: groupData.enName,
			icon: groupData.icon,
			uuid: groupData.uuid,
			level: 2
		};
		systemAPI
			.modifySystemRegisterMenu(params)
			.then(res => {
				if (res.success) {
					dispatch({
						type: "register/getSystemRegister",
						payload: {}
					});
					message.success(registerLang.common("success"));
					dispatch({
						type: "register/setDialogShow",
						payload: {
							modifyGroup: false
						}
					});
				} else {
					message.error(res.message);
				}
			})
			.catch(err => {
				console.log(err);
			});
	}

	changeFiled(field, type, e) {
		let { registerStore, dispatch } = this.props;
		let { groupData } = registerStore.dialogData;
		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		groupData[field] = value;
		dispatch({
			type: "register/setDialogData",
			payload: {
				groupData: groupData
			}
		});
	}

	render() {
		let { registerStore, dispatch } = this.props;
		let { dialogShow, dialogData } = registerStore;
		let { groupData } = dialogData;
		let formItemLayout = {
			labelCol: { span: 6 },
			wrapperCol: { span: 16 }
		};
		return (
			<div>
				<Modal
					title={registerLang.common("addMenu")}
					wrapClassName="vertical-center-modal"
					visible={dialogShow.addGroup}
					maskClosable={false}
					className="report-modal"
					onOk={this.addGroupHandle.bind(this)}
					onCancel={() => {
						dispatch({
							type: "register/setDialogShow",
							payload: {
								addGroup: false
							}
						});
						dispatch({
							type: "register/setDialogData",
							payload: {
								groupData: {
									name: null,
									enName: null,
									code: null
								}
							}
						});
					}}
				>
					<Form layout="horizontal">
						<FormItem
							label={registerLang.common("chineseName")}
							{...formItemLayout}
						>
							<Input
								value={groupData.name || undefined}
								onChange={this.changeFiled.bind(
									this,
									"name",
									"input"
								)}
								placeholder={registerLang.common(
									"chineseNamePlaceholder"
								)}
							/>
							<span className="require">*</span>
						</FormItem>
						<FormItem
							label={registerLang.common("englishName")}
							{...formItemLayout}
						>
							<Input
								value={groupData.enName || undefined}
								onChange={this.changeFiled.bind(
									this,
									"enName",
									"input"
								)}
								placeholder={registerLang.common(
									"englishNamePlaceholder"
								)}
							/>
							<span className="require">*</span>
						</FormItem>
						<FormItem
							label={registerLang.common("icon")}
							{...formItemLayout}
						>

							<div className="icon-show-wrap">
								<ul className="icon-show-list">
									{iconConstants.groupIcons.map(
										(item, index) => {
											return (
												<li
													className={
														groupData.icon ===
                                                            item.name
															? "icon-show-item active"
															: "icon-show-item"
													}
													onClick={this.changeFiled.bind(
														this,
														"icon",
														"select",
														item.name
													)}
													key={index}
												>
													<i
														className={
															"iconfont icon-" +
                                                            item.name
														}
													/>
												</li>
											);
										}
									)}
								</ul>
							</div>
						</FormItem>
						<FormItem
							label={registerLang.common("uniqTag")}
							{...formItemLayout}>
							<Input
								name="workflowCode"
								value={groupData.code || undefined}
								onChange={this.changeFiled.bind(
									this,
									"code",
									"input"
								)}
								placeholder={registerLang.common(
									"uniqTagPlaceholder"
								)}
							/>
							<span className="require">*</span>
						</FormItem>
					</Form>
				</Modal>
				<Modal
					title={registerLang.common("modifyGroup")}
					wrapClassName="vertical-center-modal"
					visible={dialogShow.modifyGroup}
					maskClosable={false}
					className="report-modal"
					onOk={this.modifyGroupHandle.bind(this)}
					onCancel={() => {
						dispatch({
							type: "register/setDialogShow",
							payload: {
								modifyGroup: false
							}
						});
						dispatch({
							type: "register/setDialogData",
							payload: {
								groupData: {
									name: null,
									enName: null,
									code: null,
									uuid: null
								}
							}
						});
					}}>
					<Form layout="horizontal">
						<FormItem
							label={registerLang.common("chineseName")}
							{...formItemLayout}>
							<Input
								value={groupData.name || undefined}
								onChange={this.changeFiled.bind(
									this,
									"name",
									"input"
								)}
								placeholder={registerLang.common("chineseName")}
							/>
							<span className="require">*</span>
						</FormItem>
						<FormItem
							label={registerLang.common("englishName")}
							{...formItemLayout}>
							<Input
								value={groupData.enName || undefined}
								onChange={this.changeFiled.bind(
									this,
									"enName",
									"input"
								)}
								placeholder={registerLang.common(
									"englishNamePlaceholder"
								)}
							/>
							<span className="require">*</span>
						</FormItem>
						<FormItem
							label={registerLang.common("icon")}
							{...formItemLayout}>
							{/* <Input value={groupData.icon || undefined}*/}
							{/* onChange={this.changeFiled.bind(this, "icon", "input")}*/}
							{/* placeholder="请输入图标名称"/>*/}
							{/* <span className="require">*</span>*/}
							<div className="icon-show-wrap">
								<ul className="icon-show-list">
									{iconConstants.groupIcons.map(
										(item, index) => {
											return (
												<li
													className={
														groupData.icon ===
                                                            item.name
															? "icon-show-item active"
															: "icon-show-item"
													}
													onClick={this.changeFiled.bind(
														this,
														"icon",
														"select",
														item.name
													)}
													key={index}
												>
													<i
														className={
															"iconfont icon-" +
                                                            item.name
														}
													/>
												</li>
											);
										}
									)}
								</ul>
							</div>
						</FormItem>
						<FormItem
							label={registerLang.common("uniqTag")}
							{...formItemLayout}>
							<Input
								name="workflowCode"
								value={groupData.code || undefined}
								onChange={this.changeFiled.bind(
									this,
									"code",
									"input"
								)}
								placeholder={registerLang.common(
									"uniqTagPlaceholder"
								)}
							/>
							<span className="require">*</span>
						</FormItem>
					</Form>
				</Modal>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	registerStore: state.register
}))(Function);
