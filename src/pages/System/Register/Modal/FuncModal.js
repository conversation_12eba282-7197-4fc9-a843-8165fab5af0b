import { PureComponent } from "react";
import { connect } from "dva";
import { Form, Select, Input, message, Modal } from "antd";
import { systemAPI } from "@/services";
import string from "@/utils/string";
import registerLang from "@/constants/lang/register";

const Option = Select.Option;
const FormItem = Form.Item;
message.config({
	maxCount: 1
});

class Function extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.changeFiled = this.changeFiled.bind(this);
    	this.addFuncHandle = this.addFuncHandle.bind(this);
    	this.modifyFuncHandle = this.modifyFuncHandle.bind(this);
    }

    checkParams(funcData) {
    	if (string.isBlank(funcData.name)) {
    		message.error(registerLang.dialog("blankName"));
    		return false;
    	}
    	if (string.isBlank(funcData.code)) {
    		message.error(registerLang.dialog("blankCode"));
    		return false;
    	}
    	if (string.isBlank(funcData.path)) {
    		message.error(registerLang.dialog("blankRoute"));
    		return false;
    	}
    	if (string.isBlank(funcData.method)) {
    		message.error(registerLang.dialog("blankMethod"));
    		return false;
    	}
    	let rx = new RegExp("^[0-9a-zA-Z]+$");

    	if (!rx.test(funcData.code)) {
    		message.error(registerLang.dialog("errorCode"));
    		return false;
    	}
    	return true;
    }

    addFuncHandle() {
    	let { registerStore, dispatch } = this.props;
    	let { systemRegister, dialogData } = registerStore;
    	let { funcData } = dialogData;
    	let {
    		registerList,
    		activeSystemIndex,
    		activeGroupIndex,
    		activeMenuIndex
    	} = systemRegister;
    	let uuid =
            registerList[activeSystemIndex].children[activeGroupIndex].children[
            	activeMenuIndex
            ]["uuid"];

    	if (!this.checkParams(funcData)) {
    		return false;
    	}

    	let params = {
    		code: funcData.code,
    		name: funcData.name,
    		enName: funcData.enName,
    		menuUuid: uuid,
    		method: funcData.method,
    		path: funcData.path
    	};
    	systemAPI.addSystemRegisterFunction(params).then(res => {
    		if (res.success) {
    			dispatch({
    				type: "register/getSystemRegister",
    				payload: {}
    			});
    			message.success(registerLang.common("success"));
    			dispatch({
    				type: "register/setDialogShow",
    				payload: {
    					addFunc: false
    				}
    			});
    			dispatch({
    				type: "register/setDialogData",
    				payload: {
    					funcData: {
    						name: null,
    						enName: null,
    						code: null,
    						method: null,
    						path: null
    					}
    				}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    modifyFuncHandle() {
    	let { registerStore, dispatch } = this.props;
    	let { dialogData } = registerStore;
    	let { funcData } = dialogData;

    	if (!this.checkParams(funcData)) {
    		return false;
    	}
    	let params = {
    		code: funcData.code,
    		name: funcData.name,
    		enName: funcData.enName,
    		method: funcData.method,
    		path: funcData.path,
    		uuid: funcData.uuid
    	};
    	systemAPI.modifySystemRegisterFunction(params).then(res => {
    		if (res.success) {
    			dispatch({
    				type: "register/getSystemRegister",
    				payload: {}
    			});
    			message.success(registerLang.common("success"));
    			dispatch({
    				type: "register/setDialogShow",
    				payload: {
    					modifyFunc: false
    				}
    			});
    			dispatch({
    				type: "register/setDialogData",
    				payload: {
    					funcData: {
    						name: null,
    						enName: null,
    						code: null,
    						method: null,
    						path: null
    					}
    				}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    changeFiled(field, e) {
    	let { registerStore, dispatch } = this.props;
    	let { funcData } = registerStore.dialogData;
    	let value;
    	if (e.target) {
    		value = e.target.value;
    	} else {
    		value = e;
    	}

    	if (value) {
    		value = value.trim();
    	}
    	funcData[field] = value;
    	dispatch({
    		type: "register/setDialogData",
    		payload: {
    			funcData: funcData
    		}
    	});
    }

    render() {
    	let { registerStore, dispatch } = this.props;
    	let { dialogShow, dialogData } = registerStore;
    	let { funcData } = dialogData;
    	let formItemLayout = {
    		labelCol: { span: 6 },
    		wrapperCol: { span: 16 }
    	};

    	return (
    		<div>
    			<Modal
    				title={registerLang.common("addFunction")}
    				wrapClassName="vertical-center-modal"
    				visible={dialogShow.addFunc}
    				maskClosable={false}
    				className="report-modal"
    				onOk={this.addFuncHandle.bind(this)}
    				onCancel={() => {
    					dispatch({
    						type: "register/setDialogShow",
    						payload: {
    							addFunc: false
    						}
    					});
    					dispatch({
    						type: "register/setDialogData",
    						payload: {
    							funcData: {
    								name: null,
    								enName: null,
    								code: null,
    								uuid: null,
    								method: null,
    								path: null
    							}
    						}
    					});
    				}}>
    				<Form layout="horizontal">
    					<FormItem
    						label={registerLang.common("chineseName")}
    						{...formItemLayout}>
    						<Input
    							name="workflowName"
    							value={funcData.name || undefined}
    							onChange={this.changeFiled.bind(this, "name")}
    							placeholder={registerLang.common(
    								"chineseNamePlaceholder"
    							)}
    						/>
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={registerLang.common("englishName")}
    						{...formItemLayout}>
    						<Input
    							name="workflowName"
    							value={funcData.enName || undefined}
    							onChange={this.changeFiled.bind(this, "enName")}
    							placeholder={registerLang.common(
    								"englishNamePlaceholder"
    							)}
    						/>
    					</FormItem>
    					<FormItem
    						label={registerLang.common("uniqTag")}
    						{...formItemLayout}>
    						<Input
    							name="workflowCode"
    							value={funcData.code || undefined}
    							onChange={this.changeFiled.bind(this, "code")}
    							placeholder={registerLang.common(
    								"uniqTagPlaceholder"
    							)}
    						/>
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={registerLang.common("path")}
    						{...formItemLayout}>
    						<Input
    							value={funcData.path || undefined}
    							onChange={this.changeFiled.bind(this, "path")}
    							placeholder={registerLang.common(
    								"pathPlaceholder"
    							)}
    						/>
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={registerLang.common("requestMethod")}
    						{...formItemLayout}>
    						<Select
    							placeholder={registerLang.common(
    								"requestMethodPlaceholder"
    							)}
    							value={funcData.method || undefined}
    							onChange={this.changeFiled.bind(
    								this,
    								"method"
    							)}>
    							<Option value="GET">GET</Option>
    							<Option value="POST">POST</Option>
    							<Option value="PUT">PUT</Option>
    							<Option value="DELETE">DELETE</Option>
    						</Select>
    						<span className="require">*</span>
    					</FormItem>
    				</Form>
    			</Modal>
    			<Modal
    				title="修改功能"
    				wrapClassName="vertical-center-modal"
    				visible={dialogShow.modifyFunc}
    				maskClosable={false}
    				className="report-modal"
    				onOk={this.modifyFuncHandle.bind(this)}
    				onCancel={() => {
    					dispatch({
    						type: "register/setDialogShow",
    						payload: {
    							modifyFunc: false
    						}
    					});
    					dispatch({
    						type: "register/setDialogData",
    						payload: {
    							funcData: {
    								name: null,
    								enName: null,
    								code: null,
    								uuid: null,
    								method: null,
    								path: null
    							}
    						}
    					});
    				}}>
    				<Form layout="horizontal">
    					<FormItem
    						label={registerLang.common("chineseName")}
    						{...formItemLayout}>
    						<Input
    							name="workflowName"
    							value={funcData.name || undefined}
    							onChange={this.changeFiled.bind(this, "name")}
    							placeholder={registerLang.common(
    								"chineseNamePlaceholder"
    							)}
    						/>
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={registerLang.common("englishName")}
    						{...formItemLayout}>
    						<Input
    							name="workflowName"
    							value={funcData.enName || undefined}
    							onChange={this.changeFiled.bind(this, "enName")}
    							placeholder={registerLang.common(
    								"englishNamePlaceholder"
    							)}
    						/>
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={registerLang.common("uniqTag")}
    						{...formItemLayout}>
    						<Input
    							name="workflowCode"
    							value={funcData.code || undefined}
    							onChange={this.changeFiled.bind(this, "code")}
    							placeholder={registerLang.common(
    								"uniqTagPlaceholder"
    							)}
    						/>
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={registerLang.common("path")}
    						{...formItemLayout}>
    						<Input
    							name="path"
    							value={funcData.path || undefined}
    							onChange={this.changeFiled.bind(this, "path")}
    							placeholder={registerLang.common(
    								"pathPlaceholder"
    							)}
    						/>
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={registerLang.common("requestMethod")}
    						{...formItemLayout}>
    						<Select
    							name="method"
    							placeholder={registerLang.common(
    								"requestMethodPlaceholder"
    							)}
    							value={funcData.method || undefined}
    							onChange={this.changeFiled.bind(
    								this,
    								"method"
    							)}>
    							<Option value="GET">GET</Option>
    							<Option value="POST">POST</Option>
    							<Option value="PUT">PUT</Option>
    							<Option value="DELETE">DELETE</Option>
    						</Select>
    						<span className="require">*</span>
    					</FormItem>
    				</Form>
    			</Modal>
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	registerStore: state.register
}))(Function);
