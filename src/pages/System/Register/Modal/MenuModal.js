import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Form, Select, Input, message, Modal } from "antd";
import { systemAPI } from "@/services";
import string from "@/utils/string";
import registerLang from "@/constants/lang/register";

const Option = Select.Option;
const FormItem = Form.Item;

message.config({
	maxCount: 1
});

class Function extends PureComponent {

	constructor(props) {
		super(props);
		this.changeFiledValue = this.changeFiledValue.bind(this);
		this.addMenuHandle = this.addMenuHandle.bind(this);
		this.modifyMenuHandle = this.modifyMenuHandle.bind(this);
	}

	checkParams(menuData) {
		if (string.isBlank(menuData.name)) {
			message.error(registerLang.dialog("blankName"));
			return false;
		}
		if (string.isBlank(menuData.enName)) {
			message.error(registerLang.dialog("blankEnName"));
			return false;
		}
		if (string.isBlank(menuData.icon)) {
			message.error(registerLang.dialog("blankIcon"));
			return false;
		}
		if (string.isBlank(menuData.path)) {
			message.error(registerLang.dialog("blankRoute"));
			return false;
		}
		if (string.isBlank(menuData.code)) {
			message.error(registerLang.dialog("blankCode"));
			return false;
		}

		let rx = new RegExp("^[0-9a-zA-Z]+$");

		if (!rx.test(menuData.code)) {
			message.error(registerLang.dialog("errorCode"));
			return false;
		}
		return true;
	}

	addMenuHandle() {
		let { registerStore, dispatch } = this.props;
		let { systemRegister, dialogData } = registerStore;
		let { menuData } = dialogData;
		let { registerList, activeSystemIndex, activeGroupIndex } = systemRegister;
		let uuid =
            registerList[activeSystemIndex].children[activeGroupIndex]["uuid"];

		if (!this.checkParams(menuData)) {
			return false;
		}

		let params = {
			code: menuData.code,
			name: menuData.name,
			enName: menuData.enName,
			icon: "document",
			path: menuData.path,
			target: menuData.target,
			level: 3,
			parentUuid: uuid
		};
		systemAPI.addSystemRegisterMenu(params).then(res => {
			if (res.success) {
				dispatch({
					type: "register/getSystemRegister",
					payload: {}
				});
				message.success(registerLang.common("success"));
				dispatch({
					type: "register/setDialogShow",
					payload: {
						addMenu: false
					}
				});
				dispatch({
					type: "register/setDialogData",
					payload: {
						menuData: {
							name: null,
							enName: null,
							code: null,
							// icon: null,
							icon: "document",
							path: null,
							target: "_self"
						}
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	modifyMenuHandle() {
		let { registerStore, dispatch } = this.props;
		let { dialogData } = registerStore;
		let { menuData } = dialogData;

		if (!this.checkParams(menuData)) {
			return false;
		}
		let params = {
			code: menuData.code,
			name: menuData.name,
			enName: menuData.enName,
			icon: "document",
			path: menuData.path,
			uuid: menuData.uuid,
			target: menuData.target
		};

		systemAPI.modifySystemRegisterMenu(params).then(res => {
			if (res.success) {
				dispatch({
					type: "register/getSystemRegister",
					payload: {}
				});
				message.success(registerLang.common("success"));
				dispatch({
					type: "register/setDialogShow",
					payload: {
						modifyMenu: false
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	changeFiledValue(field, type, e) {
		let { registerStore, dispatch } = this.props;
		let { menuData } = registerStore.dialogData;
		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		menuData[field] = value;

		dispatch({
			type: "register/setDialogData",
			payload: {
				MenuData: menuData
			}
		});
	}

	render() {
		let { registerStore, dispatch } = this.props;
		let { dialogShow, dialogData } = registerStore;
		let { menuData } = dialogData;
		let formItemLayout = {
			labelCol: { span: 6 },
			wrapperCol: { span: 16 }
		};

		return (
			<Fragment>
				<Modal
					title={registerLang.common("addMenu")}
					wrapClassName="vertical-center-modal"
					visible={dialogShow.addMenu}
					maskClosable={false}
					className="report-modal"
					onOk={this.addMenuHandle.bind(this)}
					onCancel={() => {
						dispatch({
							type: "register/setDialogShow",
							payload: {
								addMenu: false
							}
						});
						dispatch({
							type: "register/setDialogData",
							payload: {
								menuData: {
									name: null,
									enName: null,
									code: null,
									icon: "document",
									path: null,
									target: "_self"
								}
							}
						});
					}}>
					<Form layout="horizontal">
						<FormItem
							label={registerLang.common("chineseName")}
							{...formItemLayout}
						>
							<Input
								value={menuData.name || undefined}
								onChange={this.changeFiledValue.bind(this, "name", "input")}
								placeholder={registerLang.common("chineseNamePlaceholder")}
							/>
							<span className="require">*</span>
						</FormItem>
						<FormItem
							label={registerLang.common("englishName")}
							{...formItemLayout}
						>
							<Input
								value={menuData.enName || undefined}
								onChange={this.changeFiledValue.bind(this, "enName", "input")}
								placeholder={registerLang.common("englishNamePlaceholder")}
							/>
							<span className="require">*</span>
						</FormItem>
						<FormItem
							label={registerLang.common("uniqTag")}
							{...formItemLayout}
						>
							<Input
								value={menuData.code || undefined}
								onChange={this.changeFiledValue.bind(this, "code", "input")}
								placeholder={registerLang.common("uniqTagPlaceholder")}
							/>
							<span className="require">*</span>
						</FormItem>
						<FormItem
							label={registerLang.common("path")}
							{...formItemLayout}
						>
							<Input
								value={menuData.path || undefined}
								onChange={this.changeFiledValue.bind(this, "path", "input")}
								placeholder={registerLang.common("pathPlaceholder")}
							/>
							<span className="require">*</span>
						</FormItem>
						<FormItem
							label={registerLang.common("jumpMode")}
							{...formItemLayout}
						>
							<Select
								value={menuData.target || undefined}
								onChange={this.changeFiledValue.bind(this, "target", "select")}
								Placeholder={registerLang.common("selectJumpType")}
							>
								<Option value="_self">
									{registerLang.common("singleWindow")}
								</Option>
								<Option value="_blank">
									{registerLang.common("newWindow")}
								</Option>
							</Select>
						</FormItem>
					</Form>
				</Modal>
				<Modal
					title={registerLang.common("modify")}
					wrapClassName="vertical-center-modal"
					visible={dialogShow.modifyMenu}
					maskClosable={false}
					className="report-modal"
					onOk={this.modifyMenuHandle.bind(this)}
					onCancel={() => {
						dispatch({
							type: "register/setDialogShow",
							payload: {
								modifyMenu: false
							}
						});
						dispatch({
							type: "register/setDialogData",
							payload: {
								menuData: {
									name: null,
									enName: null,
									code: null,
									icon: "document",
									path: null,
									target: "_self"
								}
							}
						});
					}}>
					<Form layout="horizontal">
						<FormItem
							label={registerLang.common("chineseName")}
							{...formItemLayout}
						>
							<Input
								value={menuData.name || undefined}
								onChange={this.changeFiledValue.bind(this, "name", "input")}
								placeholder={registerLang.common("chineseNamePlaceholder")}
							/>
							<span className="require">*</span>
						</FormItem>
						<FormItem
							label={registerLang.common("englishName")}
							{...formItemLayout}
						>
							<Input
								value={menuData.enName || undefined}
								onChange={this.changeFiledValue.bind(this, "enName", "input")}
								placeholder={registerLang.common("englishNamePlaceholder")}
							/>
							<span className="require">*</span>
						</FormItem>
						<FormItem
							label={registerLang.common("uniqTag")}
							{...formItemLayout}
						>
							<Input
								value={menuData.code || undefined}
								onChange={this.changeFiledValue.bind(this, "code", "input")}
								placeholder={registerLang.common("uniqTagPlaceholder")}
							/>
							<span className="require">*</span>
						</FormItem>
						<FormItem
							label={registerLang.common("path")}
							{...formItemLayout}
						>
							<Input
								value={menuData.path || undefined}
								onChange={this.changeFiledValue.bind(this, "path", "input")}
								placeholder={registerLang.common("pathPlaceholder")}
							/>
							<span className="require">*</span>
						</FormItem>
						<FormItem
							label={registerLang.common("jumpMode")}
							{...formItemLayout}>
							<Select
								value={menuData.target || undefined}
								onChange={this.changeFiledValue.bind(this, "target", "select")}
								Placeholder={registerLang.common("selectJumpType")}
							>
								<Option value="_self">
									{registerLang.common("singleWindow")}
								</Option>
								<Option value="_blank">
									{registerLang.common("newWindow")}
								</Option>
							</Select>
						</FormItem>
					</Form>
				</Modal>
			</Fragment>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	registerStore: state.register
}))(Function);
