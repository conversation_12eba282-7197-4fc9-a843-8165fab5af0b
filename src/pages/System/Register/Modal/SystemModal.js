import { PureComponent } from "react";
import { connect } from "dva";
import { Form, Input, message, Modal } from "antd";
import { systemAPI } from "@/services";
import string from "@/utils/string";

import registerLang from "@/constants/lang/register";

const FormItem = Form.Item;

message.config({
	maxCount: 1
});

class Function extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.addSystemHandle = this.addSystemHandle.bind(this);
    	this.modifySystemHandle = this.modifySystemHandle.bind(this);
    	this.changeFiled = this.changeFiled.bind(this);
    }

    checkParams(systemData) {
    	if (string.isBlank(systemData.name)) {
    		message.error(registerLang.dialog("blankName"));
    		return false;
    	}
    	if (string.isBlank(systemData.enName)) {
    		message.error(registerLang.dialog("blankEnName"));
    		return false;
    	}
    	if (string.isBlank(systemData.code)) {
    		message.error(registerLang.dialog("blankEnName"));
    		return false;
    	}

    	let rx = new RegExp("^[0-9a-zA-Z]+$");

    	if (!rx.test(systemData.code)) {
    		message.error(registerLang.dialog("errorCode"));
    		return false;
    	}
    	return true;
    }

    addSystemHandle() {
    	let { registerStore, dispatch } = this.props;
    	let { dialogData } = registerStore;
    	let { systemData } = dialogData;

    	if (!this.checkParams(systemData)) {
    		return false;
    	}
    	let params = {
    		code: systemData.code,
    		enName: systemData.enName,
    		name: systemData.name,
    		level: 1
    	};
    	systemAPI.addSystemRegisterMenu(params).then(res => {
    		if (res.success) {
    			dispatch({
    				type: "register/getSystemRegister",
    				payload: {}
    			});
    			message.success(registerLang.common("success"));
    			dispatch({
    				type: "register/setDialogShow",
    				payload: {
    					addSystem: false
    				}
    			});
    			dispatch({
    				type: "register/setDialogData",
    				payload: {
    					systemData: {
    						name: null,
    						code: null
    					}
    				}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    modifySystemHandle() {
    	let { registerStore, dispatch } = this.props;
    	let { dialogData } = registerStore;
    	let { systemData } = dialogData;

    	if (!this.checkParams(systemData)) {
    		return false;
    	}

    	let params = {
    		code: systemData.code,
    		name: systemData.name,
    		enName: systemData.enName,
    		uuid: systemData.uuid,
    		level: 1
    	};
    	systemAPI
    		.modifySystemRegisterMenu(params)
    		.then(res => {
    			if (res.success) {
    				dispatch({
    					type: "register/getSystemRegister",
    					payload: {}
    				});
    				message.success(registerLang.common("success"));
    				dispatch({
    					type: "register/setDialogShow",
    					payload: {
    						modifySystem: false
    					}
    				});
    			} else {
    				message.error(res.message);
    			}
    		})
    		.catch(err => {
    			console.log(err);
    		});
    }

    changeFiled(field, e) {
    	let { registerStore, dispatch } = this.props;
    	let { systemData } = registerStore.dialogData;
    	let value = e.target.value;
    	systemData[field] = value;
    	dispatch({
    		type: "register/setDialogData",
    		payload: {
    			systemData: systemData
    		}
    	});
    }

    render() {
    	let { registerStore, dispatch } = this.props;
    	let { dialogShow, dialogData } = registerStore;
    	let { systemData } = dialogData;
    	let formItemLayout = {
    		labelCol: { span: 6 },
    		wrapperCol: { span: 16 }
    	};
    	return (
    		<div>
    			<Modal
    				title={registerLang.common("addSysterm")}
    				visible={dialogShow.addSystem}
    				maskClosable={false}
    				className="report-modal"
    				onOk={this.addSystemHandle.bind(this)}
    				onCancel={() => {
    					dispatch({
    						type: "register/setDialogShow",
    						payload: {
    							addSystem: false
    						}
    					});
    					dispatch({
    						type: "register/setDialogData",
    						payload: {
    							systemData: {
    								name: null,
    								enName: null,
    								code: null
    							}
    						}
    					});
    				}}>
    				<Form layout="horizontal">
    					<FormItem
    						label={registerLang.common("chineseName")}
    						{...formItemLayout}>
    						<Input
    							name="workflowName"
    							value={systemData.name || undefined}
    							onChange={this.changeFiled.bind(this, "name")}
    							placeholder={registerLang.common(
    								"chineseNamePlaceholder"
    							)}
    						/>
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={registerLang.common("englishName")}
    						{...formItemLayout}>
    						<Input
    							name="workflowName"
    							value={systemData.enName || undefined}
    							onChange={this.changeFiled.bind(this, "enName")}
    							placeholder={registerLang.common(
    								"englishNamePlaceholder"
    							)}
    						/>
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={registerLang.common("uniqTag")}
    						{...formItemLayout}>
    						<Input
    							name="workflowCode"
    							value={systemData.code || undefined}
    							onChange={this.changeFiled.bind(this, "code")}
    							placeholder={registerLang.common("uniqTag")}
    						/>
    						<span className="require">*</span>
    					</FormItem>
    				</Form>
    			</Modal>
    			<Modal
    				title={registerLang.common("modify")}
    				visible={dialogShow.modifySystem}
    				maskClosable={false}
    				className="report-modal"
    				onOk={this.modifySystemHandle.bind(this)}
    				onCancel={() => {
    					dispatch({
    						type: "register/setDialogShow",
    						payload: {
    							modifySystem: false
    						}
    					});
    					dispatch({
    						type: "register/setDialogData",
    						payload: {
    							systemData: {
    								name: null,
    								code: null,
    								uuid: null
    							}
    						}
    					});
    				}}>
    				<Form layout="horizontal">
    					<FormItem
    						label={registerLang.common("chineseName")}
    						{...formItemLayout}>
    						<Input
    							name="workflowName"
    							value={systemData.name || undefined}
    							onChange={this.changeFiled.bind(this, "name")}
    							placeholder={registerLang.common(
    								"chineseNamePlaceholder"
    							)}
    						/>
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={registerLang.common("englishName")}
    						{...formItemLayout}>
    						<Input
    							name="workflowName"
    							value={systemData.enName || undefined}
    							onChange={this.changeFiled.bind(this, "enName")}
    							placeholder={registerLang.common(
    								"englishNamePlaceholder"
    							)}
    						/>
    						<span className="require">*</span>
    					</FormItem>
    					<FormItem
    						label={registerLang.common("uniqTag")}
    						{...formItemLayout}>
    						<Input
    							name="workflowCode"
    							value={systemData.code || undefined}
    							onChange={this.changeFiled.bind(this, "code")}
    							placeholder={registerLang.common(
    								"uniqTagPlaceholder"
    							)}
    						/>
    						<span className="require">*</span>
    					</FormItem>
    				</Form>
    			</Modal>
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	registerStore: state.register
}))(Function);
