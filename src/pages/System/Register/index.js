import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import SystemList from "./Inner/SystemList";
import GroupList from "./Inner/GroupList";
import MenuList from "./Inner/MenuList";
import FuncList from "./Inner/FuncList";
import registerLang from "@/constants/lang/register";

class Register extends PureComponent {

	constructor(props) {
		super(props);
	}

	componentDidMount() {
		let { dispatch } = this.props;
		dispatch({
			type: "register/getSystemRegister"
		});
	}

	render() {
		return (
			<Fragment>
				<div className="page-global-header">
					<div className="left-info">
						<h2>{registerLang.common("registrationTitle")}</h2>
					</div>
				</div>
				<div className="page-global-body">
					<div className="system-register">
						<div className="system-register-body white-bg">
							<div className="register-box register-box-1">
								<SystemList />
							</div>
							<div className="register-box register-box-2">
								<GroupList />
							</div>
							<div className="register-box register-box-3">
								<MenuList />
							</div>
							<div className="register-box register-box-4">
								<FuncList />
							</div>
						</div>
					</div>
				</div>
			</Fragment>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	registerStore: state.register
}))(Register);
