import { PureComponent } from "react";
import { connect } from "dva";
import { <PERSON><PERSON>, Popconfirm, message, Icon, Popover, Empty } from "antd";
import RightOperaWrap from "@/components/RightOperaWrap";
import MenuModal from "../Modal/MenuModal";
import { systemAPI } from "@/services";
import registerLang from "@/constants/lang/register";
import { commonLang } from "@/constants/lang";
import { emptyImages } from "@/constants/images";

const ButtonGroup = Button.Group;

class Function extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.addMenu = this.addMenu.bind(this);
    	this.modifyMenu = this.modifyMenu.bind(this);
    	this.deleteMenu = this.deleteMenu.bind(this);
    }

    addMenu() {
    	let { dispatch } = this.props;
    	dispatch({
    		type: "register/setDialogShow",
    		payload: {
    			addMenu: true
    		}
    	});
    }

    modifyMenu(item) {
    	let { dispatch } = this.props;
    	dispatch({
    		type: "register/setDialogShow",
    		payload: {
    			modifyMenu: true
    		}
    	});

    	dispatch({
    		type: "register/setDialogData",
    		payload: {
    			menuData: {
    				name: item.name,
    				enName: item.enName,
    				code: item.code,
    				icon: item.icon,
    				path: item.path,
    				uuid: item.uuid
    			}
    		}
    	});
    }

    deleteMenu(uuid) {
    	let { dispatch } = this.props;
    	let params = {
    		uuid: uuid
    	};
    	systemAPI
    		.deleteSystemRegisterMenu(params)
    		.then(res => {
    			if (res.success) {
    				dispatch({
    					type: "register/getSystemRegister",
    					payload: {}
    				});
    				message.success(registerLang.common("success"));
    			} else {
    				message.error(res.message);
    			}
    		})
    		.catch(err => {
    			console.log(err);
    		});
    }

    render() {
    	let {
    		registerStore,
    		dispatch,
    		globalStore: {
    			personalMode: { lang }
    		}
    	} = this.props;
    	let { systemRegister } = registerStore;
    	let {
    		registerList,
    		activeSystemIndex,
    		activeGroupIndex,
    		activeMenuIndex
    	} = systemRegister;
    	let activeSystemObj =
            activeSystemIndex !== undefined
            	? registerList[activeSystemIndex]
            	: null;
    	let activeGroupObj =
            activeGroupIndex !== undefined &&
                activeSystemObj &&
                activeSystemObj.children
            	? activeSystemObj.children[activeGroupIndex]
            	: null;

    	let menuList = [];
    	if (activeGroupObj && activeGroupObj.children) {
    		menuList = activeGroupObj.children;
    	}

    	return (
    		<div style={{ position: "relative", height: "100%" }}>
    			<div className="register-box-header">
    				<h3>{registerLang.common("menuList")}</h3>
    				{
    					(activeGroupObj && (activeGroupIndex || activeGroupIndex === 0)) &&
                        <ButtonGroup className="btns">
                        	<Button
                        		type="primary"
                        		size="small"
                        		icon="plus"
                        		disabled={
                        			!(activeGroupObj &&
                                        (activeGroupIndex || activeGroupIndex === 0))
                        		}
                        		onClick={this.addMenu.bind(this)}>
                        		{registerLang.common("addMenu")}
                        	</Button>
                        </ButtonGroup>
    				}
    			</div>
    			<div className="register-box-body">
    				<ul className="system-menu-list">
    					{menuList.map((item, index) => {
    						let itemDom = [];
    						itemDom.push(
    							<li
    								className={
    									activeMenuIndex === index
    										? "system-menu-item active"
    										: "system-menu-item"
    								}
    								onClick={() => {
    									if (activeMenuIndex !== index) {
    										dispatch({
    											type:
                                                    "register/setSystemRegister",
    											payload: {
    												activeMenuIndex: index,
    												activeFuncIndex: null
    											}
    										});
    									}
    								}}
    								key={index}>
    								<div className="system-menu-title">
    									<span className="system-menu-title-span">
    										{lang === "cn"
    											? item.name
    											: item.enName}
    									</span>

    									<RightOperaWrap>
    										<Popconfirm
    											title={registerLang.common(
    												"deleteConfirm"
    											)}
    											onConfirm={this.deleteMenu.bind(
    												this,
    												item.uuid
    											)}
    											onCancel={() => { }}
    											okText={registerLang.common(
    												"delete"
    											)}
    											cancelText={registerLang.common(
    												"cancel"
    											)}>
    											<div
    												className="oper-list-opera-wrap"
    											>
    												<Icon type="delete" />
    												<span className="oper-list-opera-desc">{commonLang.opera("delete")}</span>
    											</div>
    										</Popconfirm>
    										<Popover
    											placement="left"
    											content={
    												<div>
    													<p>
    														{registerLang.common(
    															"chineseName"
    														)}
                                                            :
    														{lang === "cn"
    															? item.name
    															: item.enName}
    													</p>
    													<p>
    														{registerLang.common(
    															"englishName"
    														)}
                                                            :{item.enName}
    													</p>
    													<p>
    														{registerLang.common(
    															"uniqTag"
    														)}
                                                            :{item.code}
    													</p>
    													<p>icon：{item.icon}</p>
    													<p>Url：{item.path}</p>
    													<p>
                                                            target：
    														{item.target}
    													</p>
    												</div>
    											}
    											title={
    												lang === "cn"
    													? item.name
    													: item.enName +
                                                        registerLang.common(
                                                        	"config"
                                                        )
    											}>
    											<div
    												className="oper-list-opera-wrap"
    											>
    												<Icon type="profile" />
    												<span className="oper-list-opera-desc">{commonLang.opera("view")}</span>
    											</div>
    										</Popover>
    										<div
    											className="oper-list-opera-wrap"
    											onClick={this.modifyMenu.bind(
    												this,
    												item
    											)}
    										>
    											<Icon
    												type="edit"
    											/>
    											<span className="oper-list-opera-desc">{commonLang.opera("edit")}</span>
    										</div>
    									</RightOperaWrap>
    								</div>
    							</li>
    						);
    						return itemDom;
    					})}
    					{menuList.length === 0 && (
    						<Empty
    							style={{ marginTop: "80px" }}
    							image={emptyImages.emptyPage}
    						/>
    					)}
    				</ul>
    			</div>
    			<MenuModal />
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	registerStore: state.register
}))(Function);
