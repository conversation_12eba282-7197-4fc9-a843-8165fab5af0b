import { PureComponent } from "react";
import { connect } from "dva";
import { <PERSON><PERSON>, Popconfirm, message, Icon, Popover, Empty } from "antd";
import FuncModal from "../Modal/FuncModal";
import RightOperaWrap from "@/components/RightOperaWrap";
import { systemAPI } from "@/services";
import registerLang from "@/constants/lang/register";
import { commonLang } from "@/constants/lang";
import { emptyImages } from "@/constants/images";

const ButtonGroup = Button.Group;

class Function extends PureComponent {
	constructor(props) {
		super(props);
		this.addFunc = this.addFunc.bind(this);
		this.modifyFunc = this.modifyFunc.bind(this);
		this.deleteFunc = this.deleteFunc.bind(this);
	}

	addFunc() {
		let { dispatch } = this.props;

		dispatch({
			type: "register/setDialogShow",
			payload: {
				addFunc: true
			}
		});
	}

	modifyFunc(item) {
		let { dispatch } = this.props;

		dispatch({
			type: "register/setDialogShow",
			payload: {
				modifyFunc: true
			}
		});

		dispatch({
			type: "register/setDialogData",
			payload: {
				funcData: {
					name: item.name,
					enName: item.enName,
					code: item.code,
					uuid: item.uuid,
					method: item.method,
					path: item.path
				}
			}
		});
	}

	deleteFunc(index, uuid) {
		let { registerStore, dispatch } = this.props;
		let { systemRegister } = registerStore;
		let { registerList, activeSystemIndex, activeGroupIndex, activeMenuIndex } = systemRegister;
		let params = {
			uuid: uuid
		};
		systemAPI.deleteSystemRegisterFunction(params).then(res => {
			if (res.success) {
				registerList[activeSystemIndex].children[
					activeGroupIndex
				].children[activeMenuIndex].children.splice(index, 1);
				dispatch({
					type: "register/setSystemRegister",
					payload: {
						registerList: registerList
					}
				});
				message.success(registerLang.common("success"));
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		let {
			registerStore,
			dispatch,
			globalStore: {
				personalMode: { lang }
			}
		} = this.props;
		let { systemRegister } = registerStore;
		let {
			registerList,
			activeSystemIndex,
			activeGroupIndex,
			activeMenuIndex,
			activeFuncIndex
		} = systemRegister;
		let activeSystemObj =
            activeSystemIndex !== undefined
            	? registerList[activeSystemIndex]
            	: null;
		let activeGroupObj =
            activeGroupIndex !== undefined &&
                activeSystemObj &&
                activeSystemObj.children
            	? activeSystemObj.children[activeGroupIndex]
            	: null;
		let activeMenuObj =
            activeMenuIndex !== undefined &&
                activeGroupObj &&
                activeGroupObj.children
            	? activeGroupObj.children[activeMenuIndex]
            	: null;

		let funcList = [];
		if (activeMenuObj && activeGroupObj.children) {
			funcList = activeMenuObj.children;
		}

		return (
			<div style={{ position: "relative", height: "100%" }}>
				<div className="register-box-header">
					<h3>{registerLang.common("registrationTitle")}</h3>
					{
						(activeMenuObj && (activeMenuIndex || activeMenuIndex === 0)) &&
                        <ButtonGroup className="btns">
                        	<Button
                        		type="primary"
                        		size="small"
                        		icon="plus"
                        		disabled={
                        			!(activeMenuObj &&
                                                            (activeMenuIndex || activeMenuIndex === 0))
                        		}
                        		onClick={this.addFunc.bind(this)}>
                        		{registerLang.common("addFunction")}
                        	</Button>
                        </ButtonGroup>
					}
				</div>
				<div className="register-box-body">
					<ul className="system-menu-list">
						{funcList.map((item, index) => {
							let itemDom = [];
							itemDom.push(
								<li
									className={
										activeFuncIndex === index
											? "system-menu-item active"
											: "system-menu-item"
									}
									onClick={() => {
										dispatch({
											type: "register/setSystemRegister",
											payload: {
												activeFuncIndex: index
											}
										});
									}}
									key={index}>
									<div className="system-menu-title">
										<span className="system-menu-title-span">
											{lang === "cn"
												? item.name
												: item.enName}
										</span>
										<RightOperaWrap>
											<Popconfirm
												title={registerLang.common(
													"deleteConfirm"
												)}
												onConfirm={this.deleteFunc.bind(
													this,
													index,
													item.uuid
												)}
												onCancel={() => { }}
												okText={registerLang.common(
													"delete"
												)}
												cancelText={registerLang.common(
													"cancel"
												)}>
												<div
													className="oper-list-opera-wrap"
												>
													<Icon type="delete" />
													<span className="oper-list-opera-desc">{commonLang.opera("delete")}</span>
												</div>
											</Popconfirm>
											<Popover
												placement="left"
												content={
													<div>
														<p>
															{registerLang.common(
																"chineseName"
															)}
                                                                ：
															{lang === "cn"
																? item.name
																: item.enName}
														</p>
														<p>
															{registerLang.common(
																"englishName"
															)}
                                                                ：{item.enName}
														</p>
														<p>
															{registerLang.common(
																"uniqTag"
															)}
                                                                ：{item.code}
														</p>
														<p>path：{item.path}</p>
														<p>
                                                                method：
															{item.method}
														</p>
													</div>
												}
												title={
													lang === "cn"
														? item.name
														: item.enName +
                                                            registerLang.common(
                                                            	"config"
                                                            )
												}>
												<div
													className="oper-list-opera-wrap"
												>
													<Icon type="profile" />
													<span className="oper-list-opera-desc">{commonLang.opera("view")}</span>
												</div>
											</Popover>
											<div
												className="oper-list-opera-wrap"
												onClick={this.modifyFunc.bind(
													this,
													item
												)}
											>
												<Icon
													type="edit"
												/>
												<span className="oper-list-opera-desc">{commonLang.opera("edit")}</span>
											</div>
										</RightOperaWrap>
									</div>
								</li>
							);
							return itemDom;
						})}
						{funcList.length === 0 && (
							<Empty
								style={{ marginTop: "80px" }}
								image={emptyImages.emptyPage}
							/>
						)}
					</ul>
				</div>
				<FuncModal />
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	registerStore: state.register
}))(Function);
