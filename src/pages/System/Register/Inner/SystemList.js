import { PureComponent } from "react";
import { connect } from "dva";
import { <PERSON><PERSON>, Popconfirm, message, Icon, Popover, Empty } from "antd";
import RightOperaWrap from "@/components/RightOperaWrap";
import SystemModal from "../Modal/SystemModal";
import { systemAPI } from "@/services";
import registerLang from "@/constants/lang/register";
import { commonLang } from "@/constants/lang";
import { emptyImages } from "@/constants/images";

const ButtonGroup = Button.Group;

class Function extends PureComponent {

	constructor(props) {
		super(props);
		this.addSystem = this.addSystem.bind(this);
		this.deleteSystem = this.deleteSystem.bind(this);
		this.modifySystem = this.modifySystem.bind(this);
	}

	addSystem() {
		let { dispatch } = this.props;
		dispatch({
			type: "register/setDialogShow",
			payload: {
				addSystem: true
			}
		});
	}

	deleteSystem(uuid) {
		let { registerStore, dispatch } = this.props;
		let params = {
			uuid: uuid
		};
		systemAPI
			.deleteSystemRegisterMenu(params)
			.then(res => {
				if (res.success) {
					dispatch({
						type: "register/getSystemRegister",
						payload: {}
					});
					message.success(registerLang.common("success"));
				} else {
					message.error(res.message);
				}
			})
			.catch(err => {
				console.log(err);
			});
	}

	modifySystem(item) {
		let { registerStore, dispatch } = this.props;
		dispatch({
			type: "register/setDialogShow",
			payload: {
				modifySystem: true
			}
		});
		console.log(item);
		dispatch({
			type: "register/setDialogData",
			payload: {
				systemData: {
					name: item.name,
					enName: item.enName,
					code: item.code,
					// enName: item.enName,
					uuid: item.uuid
				}
			}
		});
	}

	render() {
		let {
			registerStore,
			dispatch,
			globalStore: {
				personalMode: { lang }
			}
		} = this.props;
		let { systemRegister } = registerStore;
		let { registerList, activeSystemIndex } = systemRegister;

		return (
			<div style={{ position: "relative", height: "100%" }}>
				<div className="register-box-header">
					<h3>{registerLang.common("systermList")}</h3>
					<ButtonGroup className="btns">
						<Button
							type="primary"
							size="small"
							icon="plus"
							onClick={this.addSystem.bind(this)}>
							{registerLang.common("addSysterm")}
						</Button>
					</ButtonGroup>
				</div>
				<div className="register-box-body">
					<ul className="system-menu-list">
						{registerList.map((item, index) => {
							return (
								<li
									className={
										activeSystemIndex === index
											? "system-menu-item active"
											: "system-menu-item"
									}
									onClick={() => {
										if (activeSystemIndex !== index) {
											dispatch({
												type:
                                                    "register/setSystemRegister",
												payload: {
													activeSystemIndex: index,
													activeGroupIndex: null,
													activeMenuIndex: null,
													activeFuncIndex: null
												}
											});
										}
									}}
									key={index}>
									<div className="system-menu-title">
										<span className="system-menu-title-span">
											{lang === "cn"
												? item.name
												: item.enName}
										</span>
										<RightOperaWrap>
											<Popconfirm
												title={registerLang.common(
													"deleteConfirm"
												)}
												onConfirm={this.deleteSystem.bind(
													this,
													item.uuid
												)}
												onCancel={() => { }}
												okText={registerLang.common(
													"delete"
												)}
												cancelText={registerLang.common(
													"cancel"
												)}>
												<div
													className="oper-list-opera-wrap"
												>
													<Icon type="delete" />
													<span className="oper-list-opera-desc">{commonLang.opera("delete")}</span>
												</div>
											</Popconfirm>
											<Popover
												placement="left"
												content={
													<div>
														<p>
															{registerLang.common(
																"chineseName"
															)}
                                                                :
    														    {lang === "cn"
																? item.name
																: item.enName}
														</p>
														<p>
															{registerLang.common(
																"englishName"
															)}
                                                                :{item.enName}
														</p>
														<p>
															{registerLang.common(
																"uniqTag"
															)}
                                                                :{item.code}
														</p>
													</div>
												}
												title={
													lang === "cn"
														? item.name
														: item.enName +
                                                            registerLang.common(
                                                            	"config"
                                                            )
												}>
												<div
													className="oper-list-opera-wrap"
												>
													<Icon type="profile" />
													<span className="oper-list-opera-desc">{commonLang.opera("view")}</span>
												</div>
											</Popover>
											<div
												className="oper-list-opera-wrap"
												onClick={this.modifySystem.bind(
													this,
													item
												)}
											>
												<Icon type="edit" />
												<span className="oper-list-opera-desc">{commonLang.opera("edit")}</span>
											</div>
										</RightOperaWrap>
									</div>
								</li>
							);
						})}
						{registerList.length === 0 && (
							<Empty
								style={{ marginTop: "80px" }}
								image={emptyImages.emptyPage}
							/>
						)}
					</ul>
				</div>
				<SystemModal />
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	registerStore: state.register
}))(Function);
