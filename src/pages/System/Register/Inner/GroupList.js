import { PureComponent } from "react";
import { connect } from "dva";
import { <PERSON><PERSON>, Popconfirm, message, Icon, Popover, Empty } from "antd";
import GroupModal from "../Modal/GroupModal";
import RightOperaWrap from "@/components/RightOperaWrap";
import { systemAPI } from "@/services";
import registerLang from "@/constants/lang/register";
import { commonLang } from "@/constants/lang";
import { emptyImages } from "@/constants/images";

const ButtonGroup = Button.Group;

class Function extends PureComponent {
	constructor(props) {
		super(props);
		this.addGroup = this.addGroup.bind(this);
		this.modifyGroup = this.modifyGroup.bind(this);
		this.deleteGroup = this.deleteGroup.bind(this);
	}

	addGroup() {
		let { registerStore, dispatch } = this.props;
		dispatch({
			type: "register/setDialogShow",
			payload: {
				addGroup: true
			}
		});
	}

	modifyGroup(item) {
		let { registerStore, dispatch } = this.props;
		dispatch({
			type: "register/setDialogShow",
			payload: {
				modifyGroup: true
			}
		});
		dispatch({
			type: "register/setDialogData",
			payload: {
				groupData: {
					name: item.name,
					enName: item.enName,
					icon: item.icon,
					code: item.code,
					uuid: item.uuid
				}
			}
		});
	}

	deleteGroup(uuid) {
		let { dispatch } = this.props;
		let params = {
			uuid: uuid
		};
		systemAPI
			.deleteSystemRegisterMenu(params)
			.then(res => {
				if (res.success) {
					dispatch({
						type: "register/getSystemRegister",
						payload: {}
					});
					message.success(registerLang.common("success"));
				} else {
					message.error(res.message);
				}
			})
			.catch(err => {
				console.log(err);
			});
	}

	render() {
		let {
			registerStore,
			dispatch,
			globalStore: {
				personalMode: { lang }
			}
		} = this.props;
		let { systemRegister } = registerStore;
		let {
			registerList,
			activeSystemIndex,
			activeGroupIndex
		} = systemRegister;
		let activeSystemObj =
            activeSystemIndex !== null ? registerList[activeSystemIndex] : null;

		let groupList = [];
		if (activeSystemObj && activeSystemObj.children) {
			groupList = activeSystemObj.children;
		}

		return (
			<div style={{ position: "relative", height: "100%" }}>
				<div className="register-box-header">
					<h3>{registerLang.common("groupList")}</h3>
					{
						(activeSystemObj && (activeSystemIndex || activeSystemIndex === 0)) &&
                        <ButtonGroup className="btns">
                        	<Button
                        		type="primary"
                        		size="small"
                        		icon="plus"
                        		disabled={
                        			!(activeSystemObj &&
                                        (activeSystemIndex || activeSystemIndex === 0))
                        		}
                        		onClick={this.addGroup.bind(this)}>
                        		{registerLang.common("addGroup")}
                        	</Button>
                        </ButtonGroup>
					}
				</div>
				<div className="register-box-body">
					<ul className="system-menu-list">
						{groupList.map((item, index) => {
							let itemDom = [];
							itemDom.push(
								<li
									className={
										activeGroupIndex === index
											? "system-menu-item active"
											: "system-menu-item"
									}
									onClick={() => {
										if (activeGroupIndex !== index) {
											dispatch({
												type:
                                                    "register/setSystemRegister",
												payload: {
													activeGroupIndex: index,
													activeMenuIndex: null,
													activeFuncIndex: null
												}
											});
										}
									}}
									key={index}>
									<div className="system-menu-title">
										<span class="system-menu-title-span">
											<i
												className={
													item.icon
														? "iconfont icon-" +
                                                        item.icon
														: "iconfont icon-pingfen"
												}
											/>
											{lang === "cn"
												? item.name
												: item.enName}
										</span>
										<RightOperaWrap>
											<Popconfirm
												title={registerLang.common(
													"deleteConfirm"
												)}
												onConfirm={this.deleteGroup.bind(
													this,
													item.uuid
												)}
												onCancel={() => { }}
												okText={registerLang.common(
													"delete"
												)}
												cancelText={registerLang.common(
													"cancel"
												)}>
												<div
													className="oper-list-opera-wrap"
												>
													<Icon type="delete" />
													<span className="oper-list-opera-desc">{commonLang.opera("delete")}</span>
												</div>
											</Popconfirm>
											<Popover
												placement="left"
												content={
													<div>
														<p>
															{registerLang.common(
																"chineseName"
															)}
                                                                :
															{lang === "cn"
																? item.name
																: item.enName}
														</p>
														<p>
															{registerLang.common(
																"englishName"
															)}
                                                                :{item.enName}
														</p>
														<p>
															{registerLang.common(
																"uniqTag"
															)}
                                                                :{item.code}
														</p>
													</div>
												}
												title={
													lang === "cn"
														? item.name
														: item.enName +
                                                            registerLang.common(
                                                            	"config"
                                                            )
												}>
												<div
													className="oper-list-opera-wrap"
												>
													<Icon type="profile" />
													<span className="oper-list-opera-desc">{commonLang.opera("view")}</span>
												</div>
											</Popover>
											<div
												className="oper-list-opera-wrap"
												onClick={this.modifyGroup.bind(
													this,
													item
												)}>
												<Icon type="edit" />
												<span className="oper-list-opera-desc">{commonLang.opera("edit")}</span>
											</div>
										</RightOperaWrap>
									</div>
								</li>
							);
							return itemDom;
						})}
						{groupList.length === 0 && (
							<Empty
								style={{ marginTop: "80px" }}
								image={emptyImages.emptyPage}
							/>
						)}
					</ul>
				</div>
				<GroupModal />
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	registerStore: state.register
}))(Function);
