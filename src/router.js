import { Router, Route, Switch, Redirect } from "dva/router";
import dynamic from "dva/dynamic";
import { get } from "lodash";
import { Spin } from "antd";
import config from "./common/config";
import Layout from "./Layout";
import { flatten } from "@/utils/utils";

const { routerPrefix } = config;

// 设置默认的加载组件
dynamic.setDefaultLoadingComponent(() => {
	return <Spin className="globalSpin" />;
});

const getExceptionRouters = app => [
	{
		name: "403",
		path: "/exception/403",
		component: dynamic({
			app,
			component: () => import("./pages/Exception/403")
		})
	},
	{
		name: "404",
		path: "/exception/404",
		component: dynamic({
			app,
			component: () => import("./pages/Exception/404")
		})
	},
	{
		name: "500",
		path: "/exception/500",
		component: dynamic({
			app,
			component: () => import("./pages/Exception/500")
		})
	}
];

// 抽象化菜单配置
const getNavList = (app) => {
	let navList = {};
	navList.SystemChildren = [
		{
			name: "功能注册",
			enName: "Function Register",
			icon: "pingfen",
			path: "/system/register",
			component: dynamic({
				app,
				component: () => import("./pages/System/Register")
			})
		},
		{
			name: "解决方案",
			enName: "Solution",
			icon: "pingfen",
			path: "/system/solution",
			component: dynamic({
				app,
				component: () => import("./pages/System/Solution")
			})
		},
		{
			name: "系统设置",
			enName: "Solution",
			icon: "pingfen",
			path: "/system/startup",
			component: dynamic({
				app,
				component: () => import("./pages/System/Startup")
			})
		}
	];
	navList.PermissionChildren = [
		{
			name: "机构管理",
			enName: "Organization",
			icon: "org",
			path: "/permission/organization",
			component: dynamic({
				app,
				component: () => import("./pages/Permission/Organization")
			})
		},
		{
			name: "角色管理",
			enName: "Role",
			icon: "role",
			path: "/permission/role",
			component: dynamic({
				app,
				component: () => import("./pages/Permission/Role")
			})
		},
		{
			name: "用户管理",
			enName: "User",
			icon: "user-group",
			path: "/permission/user",
			component: dynamic({
				app,
				component: () => import("./pages/Permission/User")
			})
		}
	];
	navList.PublicChildren = [
		{
			name: "字典配置",
			enName: "Codebook Setting",
			icon: "setting",
			path: "/dictionary",
			component: dynamic({
				app,
				component: () => import("./pages/Public/Dictionary")
			})
		},
		{
			name: "参数配置",
			enName: "Parameter Setting",
			icon: "setting",
			path: "/parameter",
			component: dynamic({
				app,
				component: () => import("./pages/Public/Parameter")
			})
		},
		{
			name: "应用管理",
			enName: "application config",
			icon: "setting",
			path: "/application",
			component: dynamic({
				app,
				component: () => import("./pages/Public/Application")
			})
		}
	];
	navList.LogChildren = [
		{
			name: "操作日志",
			enName: "Operation Log",
			icon: "log",
			path: "/operatorLog",
			component: dynamic({
				app,
				component: () => import("./pages/LogManage/OperationLog")
			})
		}
	];
	navList.UserCenterChildren = [
		{
			name: "用户中心",
			enName: "User Center",
			icon: "org",
			path: "/userCenter",
			exact: true,
			component: dynamic({
				app,
				component: () => import("./pages/UserCenter")
			})
		}
	];
	navList.IframeChildren = [
		{
			name: "页面查看",
			enName: "IFrame Preview",
			icon: "org",
			path: "/iframeView",
			exact: true,
			component: dynamic({
				app,
				component: () => import("./pages/IframeView")
			})
		}
	];
	navList.user = [
		{
			type: "user",
			name: "登录",
			enName: "Login",
			path: "/user/login",
			component: dynamic({
				app,
				component: () => import("./pages/User/Login")
			})
		},
		{
			type: "user",
			name: "系统设置",
			enName: "System Setting",
			path: "/user/startup",
			component: dynamic({
				app,
				component: () => import("./pages/InitStartup")
			})
		}
	];
	return navList;
};

export default ({ history, app, actions }) => {
	const normalRoutes = flatten(Object.values(getNavList(app)));
	const navs = [
		...normalRoutes,
		...getExceptionRouters(app)
	].map(item => {
		if (item.type !== "user") {
			return ({
				...item,
				path: `${routerPrefix}${item.path}`
			});
		} else {
			return item;
		}
	});
	return (
		<Router history={history}>
			<Layout history={history} navs={navs} actions={actions}>
				<Switch>
					{
						navs.map(({ exact = false, path, component }) => {
							return (
								<Route
									exact={exact}
									key={path}
									path={path}
									component={component}
								/>
							);
						})
					}
					<Redirect exact from="/" to={`${routerPrefix}`} />
					<Redirect exact from={`${routerPrefix}`} to={get(navs, "0.path")} />
					<Redirect exact to={"/bridge/exception/404"} />
				</Switch>
			</Layout>
		</Router>
	);
};
