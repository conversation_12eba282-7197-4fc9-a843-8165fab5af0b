@import "../../base/variables";
@import "./variables";

:global {
	.themeS2.basic-layout {
		.logo-shadow-mask {
			background: @themeBgColor;
		}
		.logo {
			& {
				background: @themeBgColor;
				color: @linkIcon;
			}
			.logo-icon {
				& {
					height: @header-height;
				}
				i {
					color: @linkIcon;
				}
			}
			span {
				color: @linkText;
			}
		}
		.ant-layout-sider {
			& {
				background: #213F83 !important;
				background: linear-gradient(#213F83, #0F75D6) !important;
			}
			.ant-menu-root {
				.ant-menu-submenu {
					& {
						background: none !important;
					}
					&.ant-menu-submenu-selected {
						background: @titleMenuLinkBg !important;
					}
					.ant-menu-sub {
						background: @bgColorDark2 !important;
					}
				}
				.group-item h3 {
					& {
						color: @linkText;
					}
					.icon1 {
						color: @linkIcon;
					}
				}
			}
		}
		.ant-menu {
			& {
				background: none;
				border: none;
			}
			&:not(.ant-menu-horizontal) .ant-menu-item-selected {
				background-color: @subMenuLinkBg;
				a {
					color: #fff;
				}
			}
			.ant-menu-item > a {
				color: @linkText;
			}
			.menu-arrow-right {
				color: @linkIcon;
			}
		}

		.ant-layout-sider-children .ant-menu {
			.ant-menu-submenu {
				.ant-menu {
					.ant-menu-item {
						&:hover {
							a {
								color: #fff;
							}
						}
					}
				}
			}
		}
	}
}
