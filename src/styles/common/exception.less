@import "../base/variables";

:global {
	.exception {
		& {
			min-height: 500px;
			height: 80%;
			margin-top: 100px;
		}
		.img-block {
			& {
				-ms-flex: 0 0 62.5%;
				flex: 0 0 62.5%;
				width: 62.5%;
				padding-right: 152px;
				zoom: 1;
				float: left;
			}
			&:before {
				content: " ";
				display: table;
			}
			&:after {
				clear: both;
				visibility: hidden;
				font-size: 0;
				height: 0;
			}
			.img-ele {
				& {
					height: 360px;
					width: 100%;
					max-width: 430px;
					float: right;
					background-repeat: no-repeat;
					background-position: 50% 50%;
					background-size: contain;
				}
				&.exp500 {
					background-image: url("../../sources/images/common/ex500.svg");
				}
				&.exp404 {
					background-image: url("../../sources/images/common/ex404.svg");
				}
				&.exp403 {
					background-image: url("../../sources/images/common/ex403.svg");
				}
			}
		}
		.content {
			& {
				flex: auto;
				margin-top: 80px;
				float: left;
			}
			h1 {
				color: #434e59;
				font-size: 72px;
				font-weight: 600;
				line-height: 72px;
				margin-bottom: 24px;
			}
			.desc {
				color: rgba(0, 0, 0, .45);
				font-size: 20px;
				line-height: 28px;
				margin-bottom: 16px;
			}
		}
	}
}
