body,
html {
    width: 100%;
    height: 100%;
}

body {
    height: 100%;
    overflow-y: hidden;
    background-color: #f0f2f5;
    text-rendering: optimizeLegibility;
    text-rendering: initial;
    -webkit-font-smoothing: initial;
    font-family: "Chinese Quote", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

#root{
    height: 100vh;
    position: relative;
    // 兼容当前布局样式 针对引入tntd的样式覆盖
    .ant-layout-content {
        min-width: 1024px;
    }
    .page-global-body{
		height: ~"calc(100vh - 90px)";
    }
    // 面包屑
	.page-global-header,
    .tnt-qls-title,
    .tnt-querylistscene-title{
		height: 40px;
		line-height:40px;
		border:none;
		.left-info h2{
			font-size: 14px;
		}
		.c-breadcrumb{
			height: 40px;
			line-height:40px;
			> span{
				font-size: 14px;
			}
		}
    }
}
.globalSpin {
	width: 100%;
	margin: 40px auto !important;
	text-align: center;
 }
.w100p{
    width: 100%;
}
