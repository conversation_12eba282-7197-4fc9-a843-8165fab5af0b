@border-color: #e6e6e6;
@import "../base/variables";

:global {
	.permission-config-list {
		& {
            padding: 20px;
            margin:0;
            height: 100%;
		}
		.permission-config-item {
			& {
				list-style: none;
				border: 1px solid #E1E6EE;
				-webkit-border-radius: 5px;
				-moz-border-radius: 5px;
				border-radius: 5px;
				margin-bottom: 10px;
				background: #fff;
			}
			&:last-child {
				margin-bottom: 0;
			}
			&.active {
				.config-item-body {
					display: block;
				}
			}
			.config-item-header {
				& {
					height: 40px;
					line-height: 40px;
					cursor: pointer;
					padding: 0 20px 0 18px;
				}
				&:hover {
					background: #f0f0f0;
				}
				.info {
					& {
						float: left;
					}
					h3 {
						float: left;
						margin-right: 12px;
						margin-bottom: 0;
						font-size: 15px;
						font-weight: normal;
					}
				}
				.right {
					& {
						float: right;
					}
					a {

					}
				}
			}
			.config-item-body {
				& {
					display: none;
				}
				.ant-checkbox-wrapper {
					float: left;
				}
			}
		}
	}
}
