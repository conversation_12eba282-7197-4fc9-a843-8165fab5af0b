@import "../base/variables";
@border-color: #eef1f5;

:global {
    .system-register {
        & {
            position: relative;
        }

        .system-register-header {
            & {
                height: 40px;
                line-height: 40px;
                margin-bottom: 10px;
                clear: both;
            }

            h2 {
                margin: 0;
                float: left;
            }
        }

        .system-register-body {
            & {
                height: ~"calc(100vh - 122px)";
                display: flex;
                // border: 1px solid @border-color;
                overflow-y: hidden;
                position: relative;
            }

            &.white-bg {
                .register-box .register-box-body {
                    background: #fff;
                }
            }
        }

        .register-box {
            & {
                height: 100%;
                transition: all 0.3s;
                background: #fff;
                background: #FCFCFC;
                box-shadow: 0 0 10px 0 rgba(213,213,213,0.50);
            }
            &:first-of-type{
                .register-box-header{
                    background:#fff;
                }
            }

            &.register-box-1 {
                width: 25%;
                z-index: 4;
            }

            &.register-box-2 {
                width: 25%;
                z-index: 3;
            }

            &.register-box-3 {
                width: 25%;
                z-index: 2;
            }

            &.register-box-4 {
                z-index: 1;
                width: 25%;
            }

            &.register-box-5 {
                position: absolute;
                left:0;
                width: 480px;
            }

            &.register-box-6 {
                width: 100%;
                margin-left: 490px;
            }

            &.register-box-6-no-margin {
                width: 100%;
                margin-left: 480px;
            }

            &:last-child:hover {
                border-right: none;
            }

            .register-box-header {
                & {
                    position: relative;
                    border-bottom: 1px solid #eef1f5;
                    height: 40px;
                    line-height: 40px;
                    padding: 0 20px;
                }

                .register-header-select {
                    width: 200px;
                }

                h3 {
                    margin: 0;
                    font-size: 14px;
                    font-weight: normal;
                    color: @text-normal-color;
                    .register-info-circle{
                        margin-left:6px;
                        position: relative;;
                        top:1px;
                    }
                    i {
                        cursor: pointer;
                    }
                }

                input {
                    margin-right: 50px;
                }

                .btns {
                    float: right;
                    position: absolute;
                    top: 0;
                    right: 20px;

                    button {
                        line-height: normal;
                        margin-left:10px;
                        padding-left:12px;
                        padding-right:12px;
                        font-size:@normal-font-size;
                        &.ant-btn > .anticon + span{
                            margin-left: 5px;
                        }
                    }
                }
            }

            .register-box-body {
                & {
                    height: ~"calc(100% - 40px)";
                    background: #f4f5f6;
                    overflow-x: hidden;
                    overflow-y: auto;
                }

                &.white-bg {
                    background: #fff;
                }

                .none-data {
                    & {
                        position: relative;
                        text-align: center;
                        margin-top: 30px;
                        font-size:@normal-font-size;
                    }

                    i {
                        font-size: 56px;
                        color: #888;
                    }

                    p {
                        font-size: 16px;
                    }
                }
            }
        }
    }

    // 左侧菜单树样式
    .system-menu-list {
        & {
            position: relative;
            list-style: none;
            padding-left: 0;
            margin-bottom: 0;
        }
        .system-menu-item {
            & {
                position: relative;
                min-height: 40px;
                line-height: 40px;
                cursor: pointer;
                background: #fff;
                font-size:@normal-font-size;
                color:@text-normal-color;
                span {
                    i {
                        margin-right: 6px;
                        vertical-align: bottom;
                        font-size: 18px;
                        line-height: initial;
                    }
                }
            }

            .system-menu-title>i.anticon{
                margin-right:4px;
            }
            .oper-name{
                padding-left:18px;
            }

            &.active {
                color: #222;
                background: @hover-background;
                span {
                    color: #222;

                    i {
                        font-weight: normal;
                    }
                }
            }

            &:hover {
                >.oper-list {
                    opacity: 1;
                }
            }

            &.selected {
                & {
                    background: #f5f5f5;
                }

                >.system-menu-title {
                    color: #222;
                    span {
                        color: inherit;
                    }
                    .oper-list {
                        opacity: 1;
                    }
                }
            }

            .system-menu-title {
                padding: 0 20px;
                position: relative;
                .system-menu-title-span{
                    display: flex;
                    align-items: center;
                }
            }
            .oper-list {
                & {
                    position: absolute;
                    right: 20px;
                    top: 0;
                    opacity: .8;
                }
                i {
                    margin-left: 6px;
                    color:@text-normal-color;
                    opacity: .8;
                }
            }
        }

        .oper-list-opera-desc{
            display: none;
        }
    }

    // tree
    .system-menu-list.tree {
        &{
            position: relative;
        }
        &.top1{
            padding-top: 10px;
        }
        .hide{
            display: none;
        }
        .empty-holder{
            width:20px;
            display: inline-block;
        }
        // 循环菜单树的样式
        .tree-level-left(@n) when (@n > 0) {
            .system-menu-title.level@{n}{
                    padding-left:(8px+@n*30px-30px);
            }
            .system-menu-title.level@{n}+.system-menu-list::before{
                content:'';
                position: absolute;
                left:(26px+@n*30px-30px);
                height:100%;
                width:1px;
                background: #E1E6EE;
                top:0;
                z-index:99;
            }
            //循环调用自身
            .tree-level-left((@n - 1));
        }
        .tree-level-left(4);


        li.system-menu-item ul span{
                opacity: .8;
        }
    }

    // register-origin-tabs
    .register-origin-tabs.ant-tabs {
        & {
            height: calc(100%);
            overflow-y: scroll;
        }

        .ant-tabs-bar {
            margin-bottom: 0;
        }

        .ant-tabs-nav {
            height: 48px;
        }

        .ant-tabs-content {
            height: ~"calc(100% - 40px)";
        }
        .ant-tabs-tab {
            margin-right: 0;
        }
    }
}
