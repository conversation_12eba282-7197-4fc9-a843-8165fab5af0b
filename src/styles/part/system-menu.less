@import "../base/variables";

:global {
	.ant-dropdown {
		.ant-dropdown-menu.system-menu-list {
			& {
				padding: 28px;
				width: 320px;
			}
			.ant-dropdown-menu-item {
				& {
					display: inline-block;
					vertical-align: top;
					color: black;
					z-index: 999;
					height: 98px;
					width: 88px;
					text-align: center;
					padding: 0;
				}
				a {
					& {
						position: relative;
						top: 5px;
						color: rgba(0, 0, 0, 0.87);
						display: inline-block;
						font-size: 13px;
						text-align: center;
						outline: none;
						padding: 0;
						margin: 0;
						height: 88px;
						width: 88px;
					}
					span.bg {
						background-image: url("../../sources/images/common/demo-system-icon.png");
						background-size: 64px 2479px;
						width: 64px;
						height: 64px;
						display: inline-block;
						&.user {
							background-position: 0 -345px;
						}
						&.credit {
							background-position: 0 -897px;
						}
						&.model {
							background-position: 0 -138px;
						}
					}
					span.name {
						position: relative;
						top: -16px;
						display: block;
						line-height: 20px;
						overflow: hidden;
						white-space: nowrap;
						width: 100%;
						text-overflow: ellipsis;
					}
				}
			}
		}
	}
}
