@import "../base/variables";

.field-setting-wrap {
    & {
        display: flex;
        height: ~"calc(100vh - 60px)";
        font-size: 12px;
        color: rgba(10, 18, 32, .64);
    }

    .field-setting-section {
        & {
            padding: 8px 16px;
            border-bottom: 1px solid #e6e6e6;
        }

        &:last-child {
            border-bottom: none;
        }

        &.no-border {
            border: none;
        }

        .section-header {
            & {
                position: relative;
                width: 100%;
                height: 40px;
                line-height: 40px;
                font-size: 14px;
                cursor: pointer;
            }

            .left {
                float: left;

                span:hover {
                    color: #333;
                }

                em {
                    color: #fff;
                    font-style: normal;
                    margin-right: 4px;
                    background: #1aaeff;
                    border-radius: 3px;
                    display: inline-block;
                    height: 20px;
                    line-height: 20px;
                    padding: 0 5px;
                }
            }

            .right {
                float: right;

                span {
                    font-size: 12px;
                }

                .ant-checkbox-inner {
                    width: 14px;
                    height: 14px;
                }
            }

            i {
                width: 16px;
                height: 16px;
            }
        }

        .section-body {
            & {
                line-height: 32px;
                font-size: 12px;
                word-wrap: break-word;
                word-break: break-all;
                clear: both;
            }

            a {
                color: inherit;
            }

            a:hover {
                color: @blue;
            }

            .sub-block {
                .block-header {}

                .block-body {
                    & {
                        line-height: 32px;
                        padding-left: 20px;
                        margin-bottom: 8px;
                        word-wrap: break-word;
                        word-break: break-all;
                        margin-bottom: 8px;
                    }

                    &.no-padding-left {
                        padding-left: 0;
                    }

                    pre {
                        line-height: 1.5;
                        max-height: 400px;
                        overflow: auto;
                        font-size: 14px;
                        border: 1px dashed #e6e6e6;
                        padding: 10px;
                    }

                    .block-body-inline {
                        & {
                            display: inline-block;
                            margin-right: 6px;
                        }

                        &:last-child {
                            margin-right: 0;
                        }

                        &.title {
                            position: relative;
                            // top: 6px;
                            width: 66px;
                        }

                        &.w50 {
                            width: 50px;
                        }

                        &.w60 {
                            width: 60px;
                        }

                        &.w66 {
                            width: 66px;
                        }

                        &.w120 {
                            width: 120px;
                        }

                        &.w175 {
                            width: 175px;
                        }

                        &.w200 {
                            width: 200px;
                        }

                        &.fr {
                            float: right;
                        }

                        .ant-input-number {
                            width: 100%;
                        }

                        .color-choose-trigger {
                            & {
                                position: relative;
                                top: 8px;
                                border: 1px solid #cdd0d4;
                                width: 24px;
                                height: 24px;
                                padding: 2px;
                                box-sizing: border-box;
                                cursor: pointer;
                            }

                            .color-choose-trigger-content {
                                width: 18px;
                                height: 18px;
                                background: #9db6e8;
                            }

                            i {
                                position: absolute;
                                bottom: 2px;
                                right: 2px;
                                color: #000;
                                font-size: 12px;
                                transform: scale(0.65);
                            }
                        }
                    }
                }
            }
        }
    }

    .field-setting-left {
        & {
            width: 220px;
            background: #e8ebed;
            height: 100%;
            padding: 5px 0;
            box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, .06);
        }
    }

    .field-setting-content {
        & {
            flex: 1;
            background: #f3f5f6;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .1), 0 10px 24px 0 rgba(0, 0, 0, .06);
            z-index: 1;
        }
    }

    .field-setting-right {
        & {
            width: 320px;
            padding: 5px 0;
            background: #e8ebed;
            overflow-y: auto;
        }
    }
}
