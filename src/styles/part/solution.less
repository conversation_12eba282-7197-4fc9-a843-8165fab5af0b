// @border-color: #e6e6e6;
@import "../base/variables";

:global {
	.solution-register-main {
		& {
            padding: 20px;
            font-size:@normal-font-size;
		}
		.solution-register-group {
			& {
				border: 1px solid @border-color;
				border-radius: 4px;
			}
			dt {
				& {
					height: 40px;
					line-height: 40px;
					border-bottom: 1px solid @border-color;
					background: @hover-background;
                    padding-left: 12px;
                    &.no-btm-border{
                        border-bottom:none;
                    }
				}
				i {
                    margin-right: 5px;
                    opacity: .8;
                    font-size:14px;
                    &.icon-caret{
                        margin-right:15px;
                        opacity: .6;
                        font-size:@normal-font-size;
                    }
                }
                > span{
                    font-weight: normal;
                }
			}
			dd {
				& {
					height: 40px;
					line-height: 40px;
					border-bottom: 1px solid rgba(0,0,0,0.10);
					margin-bottom: 0;
					padding-left: 40px;
					cursor: move;
				}
				&:hover {
                    // background: #e6e6e6;
                    box-shadow: 0 0 8px 0 rgba(0,0,0,0.10);
                    transition: box-shadow .2s linear;
				}
				i {
					margin-left: 12px;
					margin-right: 10px;
				}
				span.menu-title {
					float: left;
				}
				span.has-in-group {
					float: right;
					margin-right: 20px;
					color: #999;
				}
			}
			dd:last-child {
				border-bottom: none;
			}
		}
	}

	//solution-custom-wrap
	.solution-custom-wrap {
		& {
			padding: 20px;
			background: #fff;
			overflow-y: scroll;
		}
		.solution-custom-group {
			& {
				margin-bottom: 0;
				box-sizing: content-box;
				margin-bottom: 20px;
                border: 1px solid @border-color;
                &.border-btm-none{
                    border-bottom: none;
                }
			}
			&:last-child {
				margin-bottom: 0;
			}
			&.canDrop {
				border: 1px dotted #4cc246;
			}
			dt, dd {
				height: 40px;
				line-height: 40px;
				margin: 0;
				padding: 0 20px;
                background: #fff;
                font-size:@normal-font-size;
			}
			dt {
				& {
					position: relative;
					border-bottom: 1px solid @border-color;
					background: @hover-background;
                    padding: 0 12px;
                    cursor: pointer;
                    color:#17233D;
                }
                > span{
                    font-weight: normal;
                }
				i {
                    margin-right: 10px;
                    &.iconfont-title{
                        opacity: .8;
                        margin-right:5px;
                        font-size: 14px;
                    }
                    &.icon-caret{
                        opacity: .6;
                        margin-right:16px;
                    }
				}
				.oper-list {
					& {
						position: absolute;
						right: 10px;
						top: 0;
                        opacity: .8;
					}
					i {
						// margin-left: 10px;
                        cursor: pointer;
                        color:#17233D;
                        &:hover{
                            color:@primary-color;
                        }
                    }
				}
			}
			dd {
				& {
					position: relative;
					border-bottom: 1px solid @border-color;
				}
				&:hover {
					background: #FAFAFA;
					.oper-list {
						opacity: 1;
					}
				}
				> i {
					margin-right: 10px;
				}
				span {
					margin-left: 19px;
				}
				&:last-child {
					border-bottom: none;
				}
				.oper-list {
					& {
						position: absolute;
						right: 20px;
						top: 0;
						opacity: .8;
					}
					i {
                        margin-left: 10px;
                        color:#17233D;
                        cursor: pointer;
                        &:hover{
                            color:@primary-color;
                        }
					}
				}
			}
			.none-data {
				text-align: center;
				padding: 20px 0;
			}
		}
	}
}
.group-data-wrap,.solution-group-wrap{
    overflow: hidden;
    transition: height .3s;
}
