@import "../base/variables";

:global {
	.card-section-header {
		& {
			height: auto;
			overflow: hidden;
			margin-bottom: 10px;
		}
		.left-info {
			& {
				float: left;
			}
			h2 {
				display: inline-block;
				font-size: 20px;
				font-weight: 500;
				// color: rgba(0, 0, 0, .85);
				margin-right: 12px;
				float: left;
				margin-bottom: 0;
			}
			.sub-info {
				float: left;
				position: relative;
				top: 6px;
			}
		}
		.right-info {
			& {
				float: right;
			}
			.right-info-item {
				& {
					float: left;
					margin-left: 12px;
				}
				.right-info-tree{
					width: 210px;
				}
			}
		}
	}
	.card-section-body {
		& {
			clear: both;
		}
	}
}
