@border-color: #e6e6e6;

:global {
    .fix-card {
        height: ~"calc(100vh - 104px)";
    }

    .tnt-vertical-box {
        & {
            position: relative;
        }

        &.white-bg {
            .box-item .box-item-body {
                background: #fff;
            }
        }

        .tnt-vertical-box-header {
            & {
                height: 40px;
                line-height: 40px;
                margin-bottom: 10px;
                clear: both;
            }

            h2 {
                margin: 0;
                float: left;
            }
        }

        .tnt-vertical-box-body {
            & {
                height: ~"calc(100vh - 160px)";
                border: 1px solid @border-color;
            }

            &.white-bg {
                .register-box .register-box-body {
                    background: #fff;
                }
            }
        }

        .box-item {
            & {
                float: left;
                height: 100%;
                transition: all 0.3s;
                background: #fff;
            }

            &.border-right {
                border-right: 1px solid @border-color;
            }

            &.box-item-10 {
                width: 10%;
            }

            &.box-item-20 {
                width: 20%;
            }

            &.box-item-25 {
                width: 25%;
            }

            &.box-item-30 {
                width: 30%;
            }

            &.box-item-35 {
                width: 35%;
            }

            &.box-item-40 {
                width: 40%;
            }

            &.box-item-50 {
                width: 50%;
            }

            &.box-item-60 {
                width: 60%;
            }

            &.box-item-70 {
                width: 70%;
            }

            &.box-item-75 {
                width: 75%;
            }

            &.box-item-80 {
                width: 80%;
            }

            &:last-child:hover {
                border-right: none;
            }

            .box-item-header {
                & {
                    position: relative;
                    border-bottom: 1px solid @border-color;
                    height: 48px;
                    line-height: 48px;
                    padding: 0 20px;
                    background: #717b88;
                }

                .box-item-header-select {
                    width: 210px;
                }

                h3 {
                    margin: 0;
                    font-size: 14px;
                    font-weight: normal;
                    color: #fff;

                    i {
                        cursor: pointer;
                    }
                }

                input {
                    margin-right: 50px;
                }

                .btns {
                    float: right;
                    position: absolute;
                    top: 0;
                    right: 20px;

                    button {
                        line-height: normal;
                    }
                }
            }

            .box-item-body {
                & {
                    height: ~"calc(100% - 48px)";
                    background: #f4f5f6;
                    overflow-x: hidden;
                    overflow-y: auto;
                }

                .none-data {
                    & {
                        position: relative;
                        text-align: center;
                        margin-top: 30px;
                    }

                    i {
                        font-size: 56px;
                        color: #888;
                    }

                    p {
                        font-size: 16px;
                    }
                }
            }
        }
    }

    .system-menu-list {
        & {
            position: relative;
            list-style: none;
            padding-left: 0;
            margin-bottom: 0;
            overflow-x: hidden;
        }

        .system-menu-item {
            & {
                position: relative;
                min-height: 48px;
                line-height: 48px;
                cursor: pointer;
                background: #fff;

                span {

                    //color: #999;
                    i {
                        margin-right: 6px;
                        vertical-align: bottom;
                        font-size: 18px;
                        line-height: initial;
                    }
                }
            }

            &:last-child .system-menu-title {
                //border-bottom: none;
            }

            &.active {
                color: #222;
                background: #f0f0f0;

                //font-weight: bold;
                span {
                    color: #222;

                    i {
                        font-weight: normal;
                    }
                }
            }

            &:hover {
                >.oper-list {
                    opacity: 1;
                }
            }

            &.selected {
                & {
                    background: #f5f5f5;
                }

                >.system-menu-title {
                    color: #222;

                    //font-weight: bold;
                    span {
                        color: inherit;
                    }

                    .oper-list {
                        opacity: 1;
                    }
                }
            }

            .system-menu-title {
                & {
                    padding: 0 20px;
                    border-bottom: 1px solid @border-color;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                &.level2 {
                    padding: 0 40px;
                }

                &.level3 {
                    padding: 0 60px;
                }
            }

            .oper-list {
                & {
                    position: absolute;
                    right: 20px;
                    top: 0;
                    opacity: .35;
                }

                i {
                    margin-left: 10px;
                }
            }
        }
    }

    // tree
    .system-menu-list.tree {
        margin-top:10px;
        &.top1:before {
            position: absolute;
            left: 20px;
            height: 100%;
            border-left: 1px dotted #999;
            content: "";
            z-index: 99;
        }

        .system-menu-title:before {
            position: absolute;
            top: -29px;
            height: 56px;
            border-left: 1px dotted #999;
            content: "";
            display: none;
        }

        .system-menu-title {
            &.level1:before {}

            &.level2:before {
                width: 30px;
                left: 40px;
                display: inline-block;
            }

            &.level3:before {
                width: 50px;
                left: 60px;
                display: inline-block;
            }
        }

        .system-menu-title {
            &.level1 {
                padding-left: 48px;
            }

            &.level2 {
                padding-left: 68px;
            }

            &.level3 {
                padding-left: 88px;
            }
        }

        .system-menu-title {
            .dot {
                position: absolute;
                top: 23px;
                width: 7px;
                height: 7px;
                background: #777;
                z-index: 200;
            }

            &.level1 .dot {
                left: 17px;
            }

            &.level2 .dot {
                left: 37px;
            }

            &.level3 .dot {
                left: 57px;
            }
        }

        .system-menu-title {
            .line {
                position: absolute;
                top: 26px;
                width: 22px;
                height: 5px;
                border-top: 1px dotted #999;
                z-index: 101;
            }

            &.level1 .line {
                left: 20px;
            }

            &.level2 .line {
                left: 40px;
            }

            &.level3 .line {
                left: 60px;
            }
        }
    }

    // register-origin-tabs
    .register-origin-tabs.ant-tabs {
        & {
            height: calc(100%);
            overflow-y: scroll;
        }

        .ant-tabs-bar {
            margin-bottom: 0;
        }

        .ant-tabs-nav {
            height: 48px;
        }

        .ant-tabs-content {
            height: ~"calc(100% - 40px)";
        }

        .ant-tabs-tab {
            margin-right: 0;
        }
    }
}
