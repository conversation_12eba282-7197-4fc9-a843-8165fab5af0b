.tnt-ant-select.ant-select {
    & {
        min-width: 80px;
        font-size: 12px;
    }

    .ant-select-selection {
        height: 24px;
        line-height: 24px;
        background: none;
        border: none;
        border-radius: 0;
        box-shadow: none;
        border-bottom: 1px solid #9db6e8;
    }

    .ant-select-selection__rendered {
        margin-left: 0;
        margin-right: 0;
        height: 24px;
        line-height: 24px;
    }

    .ant-select-arrow {
        right: 0;
    }
}

// dropdown menu
body {
    .ant-select-dropdown {
        & {
            font-size: 12px;
            border-radius: 0;
            outline: none;
            -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .ant-select-dropdown-menu-item {
            padding: 0px 12px;
            line-height: 24px;
            white-space: nowrap;
            text-overflow: ellipsis;
            cursor: pointer;
            -webkit-transition: background 0.3s ease;
            transition: background 0.3s ease;
        }
    }
}
