@modal-header-height: 44px;

body {
    .ant-modal-mask {
        background-color: rgba(60, 80, 100, .5);
    }
}

.ant-modal.tnt-ant-modal {
    &.body-no-padding {
        .ant-modal-body {
            padding: 0;
        }
    }

    &.footer-has-border {
        .ant-modal-footer {
            border-top: 1px solid #e6e6e6;
        }
    }

    .ant-modal-content {
        border-radius: 0;
        -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .ant-modal-header {
        & {
            padding: 0 24px;
            height: @modal-header-height;
            line-height: @modal-header-height;
            border-radius: 0;
            background-color: #F2F4F7;
            color: #647185;
        }

        .ant-modal-title {
            line-height: @modal-header-height;
        }
    }

    .ant-modal-body {
        padding: 24px 24px 0;
    }

    .ant-modal-close-x {
        width: @modal-header-height;
        height: @modal-header-height;
        font-size: 16px;
        line-height: @modal-header-height;
    }

    .ant-modal-footer {
        & {
            padding: 0 16px;
            height: 48px;
            line-height: 48px;
            border-radius: 0;
            border-top: none;
        }

        .ant-btn {
            height: 28px;
            border-radius: 0;
        }
    }
}
