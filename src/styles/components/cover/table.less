.tnt-ant-table {
    .ant-table-thead>tr>th {
        color: rgba(0, 0, 0, 0.85);
        font-weight: normal;
        background: rgba(54, 133, 242, .1);
        border-bottom: 1px solid #e8e8e8;
    }

    .ant-table {
        & {
            font-size: 12px;
        }

        .ant-table-thead>tr>th,
        .ant-table-tbody>tr>td {
            padding: 8px 16px;
        }

        table {
            border-radius: 0;
        }

        .ant-table-thead>tr:first-child>th:first-child {
            border-top-left-radius: 0;
        }

        .ant-table-thead>tr:first-child>th:last-child {
            border-top-right-radius: 0;
        }

        .table-action {
            a {
                margin-right: 8px;

                &:hover i {
                    color: #2e81f7;
                }

                i {
                    font-size: 16px;
                    color: #999;
                }
            }
        }
    }

    .table-head {
        & {
            font-size: 12px;
        }

        i {
            color: #5182e4;
            font-size: 12px;
            margin-right: 6px;
        }

        span {
            color: #666;
        }
    }
}
