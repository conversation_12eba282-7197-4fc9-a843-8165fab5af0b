@import "../base/variables";

:global {
	.ant-switch.ant-switch-s1 {
		& {
			font-family: "Monospaced Number", "Chinese Quote", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
			font-size: 14px;
			line-height: 1.5;
			color: rgba(0, 0, 0, 0.65);
			margin: 0;
			padding: 0;
			list-style: none;
			position: relative;
			display: inline-block;
			-webkit-box-sizing: border-box;
			box-sizing: border-box;
			height: 14px;
			min-width: 44px;
			line-height: 14px;
			vertical-align: middle;
			border-radius: 100px;
			border: 1px solid transparent;
			background-color: #dcdcdc;
			cursor: pointer;
			-webkit-transition: all 0.36s;
			-o-transition: all 0.36s;
			transition: all 0.36s;
			-webkit-user-select: none;
			-moz-user-select: none;
			-ms-user-select: none;
			user-select: none;
		}
		&.ant-switch-checked {
			background-color: #1890ff;
		}
		&.ant-switch:before, &.ant-switch:after {
			position: absolute;
			width: 20px;
			height: 20px;
			border-radius: 18px;
			top: -4px;
			background-color: #fff;
			content: " ";
			cursor: pointer;
			-webkit-transition: all 0.36s cubic-bezier(0.78, 0.14, 0.15, 0.86);
			-o-transition: all 0.36s cubic-bezier(0.78, 0.14, 0.15, 0.86);
			transition: all 0.36s cubic-bezier(0.78, 0.14, 0.15, 0.86);
		}
	}
}

