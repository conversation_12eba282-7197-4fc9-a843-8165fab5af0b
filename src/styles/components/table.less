@import "../base/variables";

:global {

    // 覆盖antd table原有的middle padding
    // .ant-table-middle>.ant-table-content>.ant-table-header>table>.ant-table-thead>tr>th,
    // .ant-table-middle>.ant-table-content>.ant-table-body>table>.ant-table-thead>tr>th,
    // .ant-table-middle>.ant-table-content>.ant-table-scroll>.ant-table-header>table>.ant-table-thead>tr>th,
    // .ant-table-middle>.ant-table-content>.ant-table-scroll>.ant-table-body>table>.ant-table-thead>tr>th,
    // .ant-table-middle>.ant-table-content>.ant-table-fixed-left>.ant-table-header>table>.ant-table-thead>tr>th,
    // .ant-table-middle>.ant-table-content>.ant-table-fixed-right>.ant-table-header>table>.ant-table-thead>tr>th,
    // .ant-table-middle>.ant-table-content>.ant-table-fixed-left>.ant-table-body-outer>.ant-table-body-inner>table>.ant-table-thead>tr>th,
    // .ant-table-middle>.ant-table-content>.ant-table-fixed-right>.ant-table-body-outer>.ant-table-body-inner>table>.ant-table-thead>tr>th,
    // .ant-table-middle>.ant-table-content>.ant-table-header>table>.ant-table-tbody>tr>td,
    // .ant-table-middle>.ant-table-content>.ant-table-body>table>.ant-table-tbody>tr>td,
    // .ant-table-middle>.ant-table-content>.ant-table-scroll>.ant-table-header>table>.ant-table-tbody>tr>td,
    // .ant-table-middle>.ant-table-content>.ant-table-scroll>.ant-table-body>table>.ant-table-tbody>tr>td,
    // .ant-table-middle>.ant-table-content>.ant-table-fixed-left>.ant-table-header>table>.ant-table-tbody>tr>td,
    // .ant-table-middle>.ant-table-content>.ant-table-fixed-right>.ant-table-header>table>.ant-table-tbody>tr>td,
    // .ant-table-middle>.ant-table-content>.ant-table-fixed-left>.ant-table-body-outer>.ant-table-body-inner>table>.ant-table-tbody>tr>td,
    // .ant-table-middle>.ant-table-content>.ant-table-fixed-right>.ant-table-body-outer>.ant-table-body-inner>table>.ant-table-tbody>tr>td {
    //     padding: 8px 16px !important;
    // }

    .table-striped table {
        tbody tr:nth-child(even) {
            /* 匹配偶数行 */
            background-color: #F7FBFF;
        }
    }
}
