@import "../base/variables";

:global {
	.switch-app {
		& {
			position: absolute;
			left: @header-height+20;
			top: 0;
		}
		.switch-app-menu {
			& {
				display: block;
				color: #fff;
			}
			span {
				font-size: 16px;
			}
			i {
				margin-left: 12px;
				vertical-align: text-top;
			}
		}
	}
	.app-menu-list.ant-dropdown-menu {
		& {
			border-radius: 0;
			min-width: 250px;
			max-height: 350px;
			overflow-y: scroll;
			border: 1px solid #ccc;
			-webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, .2);
			box-shadow: 0 2px 10px rgba(0, 0, 0, .2);
		}
		.ant-dropdown-menu-item {
			& {
				padding: 0 20px;
				height: 44px;
				line-height: 44px;
			}
			&:hover {
				background: rgba(0, 0, 0, 0.1);
			}
			&.app-search-warp {
				& {
					padding: 0;
				}
				&:hover {
					background: none;
				}
				.ant-input-affix-wrapper {
					height: 100%;
				}
				.ant-input {
					border: none;
					border-bottom: 1px dashed #dcdcdc;
					box-shadow: none;
					padding: 0 20px;
				}
			}
			i {
				margin-right: 0 !important;
				margin-left: 6px !important;
			}
		}
	}
}
