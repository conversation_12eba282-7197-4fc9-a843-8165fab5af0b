import ReactDOM from "react-dom";
import dva from "dva";
import "moment/locale/zh-cn";
import browserHistory from "history/createBrowserHistory";
import { message } from "antd";
import router from "./router";
import "./styles/style.less";
import { isAppListVisible } from "@/utils/utils";

const app = dva({
	history: browserHistory(),
	onError(e) {
		message.error(e.message, 3);
	}
});

// 配置 hooks 或者注册插件
const registerModels = app => {
	// common
	app.model(require("./models/global").default);
	app.model(require("./models/login").default);

	// solution
	app.model(require("./models/solution/register").default);
	app.model(require("./models/solution/solution").default);
	app.model(require("./models/solution/startup").default);

	// permission
	app.model(require("./models/permission/user").default);
	app.model(require("./models/permission/permission").default);

	// public
	app.model(require("./models/public/systemConfig").default);
	app.model(require("./models/public/application").default);
	app.model(require("./models/public/dictionary").default);

	// user
	app.model(require("./models/userCenter").default);
	app.model(require("./models/log").default);
};

// isInLightBox 标志应用是否在运行在轻盒（微前端spa）, 若为假则按照当前模式运行
if (!window.isInLightBox) {
	registerModels(app);
	app.router(router);
	app.start("#root");
}

export async function bootstrap() {
	console.log("react app bootstraped");
};

let onPopstate = actions => actions.setAppListVisible(isAppListVisible());
export const mount = async ({actions}) => {

	actions.setAppListVisible(isAppListVisible());
	onPopstate = onPopstate.bind(null, actions);
	window.addEventListener("popstate", onPopstate);

	registerModels(app);
	app.router(
		({ app }) => router({ history: browserHistory(), app, actions })
	);
	app.start("#root");
};

export const unmount = async () => {
	window.removeEventListener("popstate", onPopstate);
	ReactDOM.unmountComponentAtNode(document.getElementById("root"));
	app._models.forEach(model => {
	  app.unmodel(model.namespace);
	});
	console.log("bifrost unmount ...");
};

export async function update(props) {
	console.log("update props", props);
};

export const getAppStore = () => app._store;

export const getLanguage = () => {
	const globalStore = getAppStore().getState().global;
	const { personalMode } = globalStore;

	return personalMode.lang === "cn" ? "cn" : "en";
};

export default app._store;
