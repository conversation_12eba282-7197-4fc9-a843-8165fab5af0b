// import { message } from "antd";
// import { systemConfigAPI } from "@/services";
import { compare, multipleToNull } from "@/utils/modelOperator";

export default {
	namespace: "systemConfig",
	state: {
		// configList: [],
		// total: 0,
		// searchParams: {
		// 	curPage: 1,
		// 	pageSize: 10,
		// 	status: "",
		// 	configKey: "",
		// 	configValue: "",
		// 	type: "",
		// 	description: ""
		// },
		dialogShow: {
			addConfig: false,
			modifyConfig: false,
			valueDetail: false
		},
		dialogData: {
			configData: {
				uuid: null,
				configKey: null,
				configValue: null,
				description: null,
				type: null,
				status: null
			},
			valueDetailData: null
		}
	},
	effects: {
		// *getConfigList({ }, { call, put, select }) {
		// 	const { searchParams } = yield select(state => state.systemConfig);
		// 	let response = yield call(systemConfigAPI.getConfigList, searchParams);
		// 	if (!response) {
		// 		return;
		// 	}
		// 	if (!response.success) {
		// 		message.error(response.message);
		// 		return;
		// 	}
		// 	yield put({
		// 		type: "setAttrValue",
		// 		payload: {
		// 			configList: response.data.configList || [],
		// 			total: response.data.total || 0
		// 		}
		// 	});
		// }
	},
	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 用于在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = multipleToNull(stateChange[key]);
						}
					}
				}
				return stateChange;
			})(Object.assign({}, state), payload);
		}
	}
};
