import { message } from "antd";
import { applicationAPI } from "@/services";
import { compare, multipleToNull } from "@/utils/modelOperator";

export default {
	namespace: "application",
	state: {
		// applicationList: [],
		// total: 0,
		// searchParams: {
		// 	curPage: 1,
		// 	pageSize: 10,
		// 	name: "",
		// 	displayName: ""
		// },
		dialogShow: {
			addApplication: false,
			modifyApplication: false
		},
		dialogData: {
			applicationData: {
				uuid: null,
				name: null,
				displayName: null,
				description: null
			}
		}
	},
	effects: {
		// *getApplicationList({ }, { call, put, select }) {
		// 	const { searchParams } = yield select(state => state.application);
		// 	const response = yield call(applicationAPI.getApplicationList, searchParams);

		// 	if (!response) {
		// 		return;
		// 	}
		// 	if (!response.success) {
		// 		message.error(response.message);
		// 		return;
		// 	}

		// 	const data = response.data || {};
		// 	yield put({
		// 		type: "setAttrValue",
		// 		payload: {
		// 			applicationList: data.contents || [],
		// 			total: data.total || 0,
		// 			searchParams: {
		// 				curPage: data.curPage,
		// 				pageSize: data.pageSize
		// 			}
		// 		}
		// 	});
		// }
	},
	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 用于在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = multipleToNull(stateChange[key]);
						}
					}
				}
				return stateChange;
			})(Object.assign({}, state), payload);
		}
	}
};
