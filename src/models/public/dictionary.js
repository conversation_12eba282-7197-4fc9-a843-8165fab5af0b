// import { message } from "antd";
// import { dictionaryAPI } from "@/services";
import { compare, multipleToNull } from "@/utils/modelOperator";
import { initObj } from "@/constants/dicitionary";

export default {
	namespace: "dictionary",
	state: {
		operation: null, // 添加add 或者 修改modify
		// dictionaryList: [],
		// total: 0,
		// searchParams: {
		// 	curPage: 1,
		// 	pageSize: 10,
		// 	group: "", // 分组
		// 	myKey: "", // 字典标识
		// 	myValue: "", // 字典详情
		// 	description: "" // 字典描述
		// },
		dialogShow: {
			addDict: false,
			modifyDict: false,
			myValueDialog: false
		},
		dialogData: {
			dictData: {
				uuid: null,
				myKey: null,
				myValue: null,
				normalMyValue: [initObj],
				groups: [],
				description: null
			},
			myValueData: {
				myValue: null
			}
		}
	},
	effects: {
		// *getDictionaryList({ }, { call, put, select }) {
		// 	const { searchParams } = yield select(state => state.dictionary);
		// 	let response = yield call(dictionaryAPI.getDictionaryList, searchParams);
		// 	if (!response) {
		// 		return;
		// 	}
		// 	if (!response.success) {
		// 		message.error(response.message);
		// 		return;
		// 	}
		// 	const data = response.data || {};
		// 	yield put({
		// 		type: "setAttrValue",
		// 		payload: {
		// 			dictionaryList: data.dictionaryList || [],
		// 			total: data.total || 0
		// 		}
		// 	});
		// }
	},
	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 用于在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = multipleToNull(stateChange[key]);
						}
					}
				}
				return stateChange;
			})(Object.assign({}, state), payload);
		}
	}
};
