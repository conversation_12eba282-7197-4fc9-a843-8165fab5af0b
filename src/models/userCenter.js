import { message } from "antd";
import { userAPI } from "@/services";

export default {
	namespace: "userCenter",
	state: {
		userInfo: {
			uuid: null,
			avatar: null,
			account: null,
			userName: null,
			gender: null,
			telephoneNum: null,
			email: null,
			sign: null,
			orgName: null,
			apps: []
		},
		loading: false,
		dialogShow: {
			modifyLicense: false
		},
		dialogData: {
			modifyLicenseData: {
				license: null,
				publicKey: null
			}
		},
		currentLicenseInfo: {},
		loginHistoryData: {
			pageSize: null,
			curPage: null,
			loginHistory: [],
			total: null
		}
	},
	effects: {
		*getUserInfo({ payload }, { call, put }) {
			const response = yield call(userAPI.getUserInfo, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "setAttrValue",
				payload: {
					userInfo: response.data
				}
			});
		},
		*getLoginHistory({ payload }, { call, put }) {
			const response = yield call(userAPI.getLoginHistory, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initloginHistoryData",
				payload: response.data
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		},
		initloginHistoryData(state, action) {
			let { loginHistoryData } = state;
			let { payload } = action;
			for (let key in loginHistoryData) {
				if (payload[key] !== undefined) {
					loginHistoryData[key] = payload[key];
				}
			}

			return {
				...state,
				loginHistoryData: loginHistoryData
			};
		}
	}
};
