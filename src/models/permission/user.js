import { userAP<PERSON> } from "@/services";
import { message } from "antd";

export default {
	namespace: "user",
	state: {
		currentUser: {},
		// userListData: {
		// 	userList: [],
		// 	searchType: "account",
		// 	total: null,
		// 	searchParams: {
		// 		curPage: 1,
		// 		pageSize: 10,
		// 		orgUuid: null,
		// 		roleUuid: null,
		// 		account: null,
		// 		userName: null,
		// 		selectOrgList: []
		// 	}
		// },
		dialogShow: {
			operateUser: false
		},
		dialogData: {
			userData: {
				avatar: "male1",
				account: null,
				userName: null,
				orgUuid: null,
				roleUuids: null,
				expiration: null,
				gender: 0,
				appName: null,
				email: null,
				phone: null,
				status: null,
				otherRoles: null
			},
			selectOrgList: [],
			isThisOrgList: false,
			roleList: [],
			appList: [],
			operationType: "add"
		}
	},

	effects: {
		*fetchCurrent({ payload }, { call, put }) {
			const response = yield call(userAPI.getUserInfo, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "saveCurrentUser",
				payload: response.data
			});
		}
		// *getUserList({ payload }, { call, put }) {
		// 	let response = yield call(userAPI.getUserList, payload);
		// 	if (!response) {
		// 		return;
		// 	}
		// 	if (!response.success) {
		// 		message.error(response.message);
		// 		return;
		// 	}
		// 	yield put({
		// 		type: "initUserList",
		// 		payload: response
		// 	});
		// }
	},

	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		},
		saveCurrentUser(state, action) {
			return {
				...state,
				currentUser: action.payload
			};
		}
		// setUserListData(state, action) {
		// 	let { userListData } = state;
		// 	let { payload } = action;
		// 	for (let key in userListData) {
		// 		if (payload[key] !== undefined) {
		// 			userListData[key] = payload[key];
		// 		}
		// 	}

		// 	return {
		// 		...state,
		// 		userListData: userListData
		// 	};
		// },
		// initUserList(state, action) {
		// 	let { userListData } = state;
		// 	let searchParams = userListData.searchParams;
		// 	let data = action.payload.data || {};
		// 	if (data.curPage) {
		// 		userListData["userList"] = data.userList;
		// 		searchParams["pageSize"] = data.pageSize;
		// 		searchParams["curPage"] = data.curPage;
		// 		userListData["total"] = data.total;
		// 	}
		// 	return {
		// 		...state,
		// 		userListData: userListData
		// 	};
		// }
	}
};
