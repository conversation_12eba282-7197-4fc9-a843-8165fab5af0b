import { message } from "antd";
import { orgAPI, roleAPI } from "@/services";

export default {
	namespace: "permission",
	state: {
		orgList: [],
		selectOrg: {},
		selectRole: {},
		roleSelectOrg: {},
		permissionConfig: [],
		permissionConfigTemp: [],
		hasChangeConfig: false,
		expandedRowKeys: [],
		rolePageData: {
			selectOrgList: [],
			roleList: []
		},
		dialogShow: {
			addOrg: false,
			modifyOrg: false,
			addRole: false,
			modifyRole: false
		},
		dialogData: {
			addOrgData: {
				name: null,
				code: null,
				level: null,
				parentUuid: null
			},
			modifyOrgData: {
				name: null,
				code: null,
				level: null,
				uuid: null,
				parentUuid: null
			},
			addRoleData: {
				orgUuid: null,
				code: null,
				name: null,
				type: "default"
			},
			modifyRoleData: {
				orgUuid: null,
				code: null,
				name: null,
				type: "default"
			}
		}
	},
	effects: {
		*getOrgList({ payload }, { call, put }) {
			let response = yield call(orgAPI.getOrgList, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initOrgList",
				payload: response
			});
		},
		*getOrgPermission({ payload }, { call, put }) {
			let response = yield call(orgAPI.getOrgPermission, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initPermission",
				payload: response
			});
		},
		*getRolePermission({ payload }, { call, put }) {
			let response = yield call(roleAPI.getRolePermission, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initPermission",
				payload: response
			});
		},
		*getRoleByOrg({ payload }, { call, put }) {
			let response = yield call(roleAPI.getRoleByOrg, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initRoleByOrg",
				payload: response
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		},
		setRolePageData(state, action) {
			let { rolePageData } = state;
			let { payload } = action;
			for (let key in rolePageData) {
				if (payload[key] !== undefined || payload[key] === null) {
					rolePageData[key] = payload[key];
				}
			}

			return {
				...state,
				rolePageData: rolePageData
			};
		},
		initOrgList(state, action) {
			let { orgList } = state;
			let reqOrgList = action.payload.data || [];
			orgList = reqOrgList;
			return {
				...state,
				orgList: orgList
			};
		},
		initPermission(state, action) {
			let { permissionConfig, permissionConfigTemp, expandedRowKeys } = state;
			let data = action.payload.data || [];
			expandedRowKeys = [];
			data && data.map((item, index) => {
				console.log(item);
				expandedRowKeys.push(item.groupUuid);
			});
			return {
				...state,
				permissionConfig: data || [],
				permissionConfigTemp: JSON.parse(JSON.stringify(data)) || [],
				expandedRowKeys: expandedRowKeys
			};
		},
		initRoleByOrg(state, action) {
			let { rolePageData } = state;
			let data = action.payload.data || [];

			rolePageData["roleList"] = data || [];

			return {
				...state,
				rolePageData: rolePageData
			};
		}
	}
};
