import { startupAPI } from "@/services";
import { message } from "antd";
import cloneDeep from "lodash.clonedeep";
import { compare, multipleToNull } from "@/utils/modelOperator";

export default {
	namespace: "startup",
	state: {
		activeMenuIndex: 0,
		licensePage: {
			hasLicense: false,
			licenseReady: false,
			modifyLicence: false,
			modifyLicenceSuccess: false,
			modifyLicenceData: {
				license: null,
				publicKey: null
			},
			licenseInfo: {
				license: null,
				leftDay: null,
				appNum: null,
				expiration: null
			}
		},
		solutionPage: {
			hasSolution: false,
			solutionReady: false,
			menuTreeInfo: {
				name: null,
				enName: null,
				logo: null,
				code: null,
				path: null,
				menuTree: []
			}
		},
		dialogShow: {
			viewSolution: false,
			importSolution: false
		},
		dialogData: {}
	},
	effects: {
		*getLicense({ payload }, { call, put }) {
			let response = yield call(startupAPI.getLicenseInfo, payload);
			if (response && response.code && response.code === 100404) {
				yield put({
					type: "setAttrValue",
					payload: {
						licensePage: {
							hasLicense: false,
							licenseReady: true
						}
					}
				});
				return;
			}
			if (!(response && response.data)) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			let data = response.data;
			yield put({
				type: "setAttrValue",
				payload: {
					licensePage: {
						hasLicense: true,
						licenseReady: true,
						licenseInfo: {
							license: data.license,
							leftDay: data.leftDay,
							appNum: data.appNum,
							expiration: data.expiration
						}
					}
				}
			});
		},

		*getSolution({ payload }, { call, put }) {
			let response = yield call(startupAPI.getSolution, payload);
			if (response && response.code && response.code === 110404) {
				yield put({
					type: "setAttrValue",
					payload: {
						solutionPage: {
							hasSolution: false,
							solutionReady: true
						}
					}
				});
				return;
			}
			if (!(response && response.data)) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			let data = response.data;
			yield put({
				type: "setAttrValue",
				payload: {
					solutionPage: {
						hasSolution: true,
						solutionReady: true,
						menuTreeInfo: {
							name: data[0].name,
							enName: data[0].enName,
							code: data[0].code,
							path: data[0].path,
							logo: data[0].logo,
							menuTree: data[0].children || []
						}
					}
				}
			});
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 用于在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = multipleToNull(stateChange[key]);
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		}
	}
};

