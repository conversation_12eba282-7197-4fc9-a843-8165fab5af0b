import { message } from "antd";
import { solutionAPI } from "@/services";

export default {
	namespace: "solution",
	state: {
		registerList: [],
		customTree: [],
		currentSolutionIndex: 0,
		dialogShow: {
			addSolution: false,
			modifySolution: false,
			addSolutionGroup: false,
			modifySolutionGroup: false
		},
		dialogData: {
			addGroupData: {
				name: null,
				enName: null,
				code: null,
				icon: null
			},
			modifyGroupData: {
				name: null,
				enName: null,
				code: null,
				level: null,
				uuid: null,
				icon: null
			},
			modifySolutionData: {
				name: null,
				enName: null,
				icon: null,
				path: null,
				uuid: null,
				code: null
			}
		}
	},
	effects: {
		*getSolution({ payload }, { call, put }) {
			let response = yield call(solutionAPI.getSolution, payload);
			console.log(response);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initCustomTree",
				payload: response
			});
		}
	},
	reducers: {
		initCustomTree(state, action) {
			let { customTree } = state;
			let list = action.payload.data || [];
			customTree = list;
			return {
				...state,
				customTree: customTree
			};
		},
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		}
	}
};
