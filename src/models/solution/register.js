import { message } from "antd";
import { systemAPI } from "@/services";

export default {
	namespace: "register",
	state: {
		systemRegister: {
			registerList: [],
			activeSystemIndex: 0,
			activeGroupIndex: null,
			activeMenuIndex: null,
			activeFuncIndex: null
		},
		whiteApiList: {
			activeSystemIndex: 0
		},
		dialogShow: {
			addSystem: false,
			modifySystem: false,
			addGroup: false,
			modifyGroup: false,
			addMenu: false,
			modifyMenu: false,
			addFunc: false,
			modifyFunc: false
		},
		dialogData: {
			systemData: {
				name: null,
				enName: null,
				code: null
			},
			groupData: {
				name: null,
				enName: null,
				icon: null,
				code: null
			},
			menuData: {
				name: null,
				enName: null,
				code: null,
				icon: "document",
				path: null,
				target: "_self"
			},
			funcData: {
				name: null,
				enName: null,
				code: null,
				path: null,
				method: null
			}
		}
	},
	effects: {
		*getSystemRegister({ payload }, { call, put }) {
			let response = yield call(systemAPI.getSystemRegister, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initRegisterList",
				payload: response
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		initRegisterList(state, action) {
			let { systemRegister } = state;
			let registerList = action.payload.data || [];
			systemRegister["registerList"] = registerList;
			return {
				...state,
				systemRegister: systemRegister
			};
		},
		setSystemRegister(state, action) {
			let { systemRegister } = state;
			let { payload } = action;
			for (let key in systemRegister) {
				if (payload[key] !== undefined || payload[key] === null) {
					systemRegister[key] = payload[key];
				}
			}

			return {
				...state,
				systemRegister: systemRegister
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		}
	}
};
