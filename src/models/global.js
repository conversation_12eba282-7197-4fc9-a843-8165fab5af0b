import { userAPI, dictionaryAP<PERSON> } from "@/services";
import { message } from "antd";
import cloneDeep from "lodash.clonedeep";
import { compare, multipleToNull } from "@/utils/modelOperator";
import { isAppListVisible } from "@/utils/utils";
import { isJSON } from "@/utils/isJSON";

export default {
	// model 的命名空间，同时也是他在全局 state 上的属性，只能用字符串，不支持通过 . 的方式创建多层命名空间
	namespace: "global",
	state: {
		// 初始值，优先级低于传给 dva() 的 opts.initialState。
		messageStatus: false,
		appList: [],
		currentApp: {},
		showApp: false,
		collapsed: false,
		sideMenu: {
			collapsed: false,
			activeGroupCode: false,
			openKeys: [],
			beforeOpenKeys: []
		},
		customTree: {},
		personalMode: {
			showModal: false,
			lang: "cn",
			theme: "day",
			layout: "default",
			simplified: true
		},
		userInfoMode: {
			avatar: null,
			userName: null,
			account: null
		},
		menuTreeReady: false,
		userReady: false,
		multiUserModal: false,
		systemDictionaryMap: {}
	},
	effects: {
		*getUserMenuTree({ payload, actions }, { call, put }) {
			let response;
			if (actions) {
				response = {
				  	data: actions.getMenuTreeInfo()
				};
				actions.on(
					actions.EVENTS_ENUM.MENU_TREE_INFO_CHANGE,
					menuTreeInfo => {
						put({
							type: "initUserMenuTree",
							payload: {
								data: menuTreeInfo
							}
						});
					}
				);
			} else {
				response = yield call(userAPI.getUserMenuTree, payload);
				if (!(response && response.data)) {
					return;
				}
				if (!response.success) {
					message.error(response.message);
					return;
				}
			}
			yield put({
				type: "initUserMenuTree",
				payload: response
			});
		},
		*getUserInfo({ payload, actions }, { call, put, select }) {
			const { userInfoMode } = yield select(state=>state.global);
			const allObj = { key: "all", name: "全部应用" };
			const formatAppList = appList => [
				{...allObj},
				...appList?.map(({ name, displayName }) => ({
					key: name,
					name: displayName
				}))
			];

			let currentApp = {...allObj};
			if (localStorage.hasOwnProperty("currentApp")) {
				// 缓存中是否存在currentApp
				let currentAppObjStr = localStorage.getItem("currentApp");
				if (currentAppObjStr && isJSON(currentAppObjStr)) {
					// 存在的currentApp是否是标准JSON
					let currentAppJson = JSON.parse(currentAppObjStr);
					if (currentAppJson.name && currentAppJson.dName) {
						// 判断currentApp是否是标准格式
						currentApp = {
							key: currentAppJson.name,
							name: currentAppJson.dName
						};
					} if (currentAppJson.name && currentAppJson.key) {
						// 判断currentApp是否是标准格式
						currentApp = currentAppJson;
					}
				}
			}

			let response;

			if (actions) {
				response = { data: actions.getUserInfo() };
				actions.on(
				    actions.EVENTS_ENUM.USER_INFO_CHANGE,
				    userInfo => {
						const { avatar, userName, account, token, apps } = userInfo || {};
						put({
							type: "setAttrValue",
							payload: {
								userInfoMode: { avatar, userName, account, token },
								appList: formatAppList(apps || [])
							}
						});
				    }
				);
			} else { // 这个代码保留是为了保证不运行在lightbox中，也能工作正常
				response = yield call(userAPI.getUserInfo, payload);
				if (!(response && response.data)) {
					return;
				}
				if (!response.success) {
					message.error(response.message);
					return;
				}
			}
			const { avatar, userName, account, token, apps } = response?.data || {};

			Object.assign(userInfoMode, {
				avatar,
				userName,
				account,
				token
			});

			yield put({
				type: "setAttrValue",
				payload: {
					userInfoMode,
					appList: formatAppList(apps || []),
					currentApp,
					userReady: true
				}
			});
		},
		*getDictionaryType({ payload }, { call, put }) {
			let response = yield call(dictionaryAPI.getDictionaryType, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			const data = response.data;

			let systemDictionaryMap = {};
			systemDictionaryMap[payload] = data || [];
			yield put({
				type: "setAttrValue",
				payload: {
					systemDictionaryMap: systemDictionaryMap
				}
			});
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 用于在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = multipleToNull(stateChange[key]);
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		},

		switchApp(state, action) {
			let name = action.payload.name;
			let { currentApp, appList } = state;
			currentApp = appList.filter(item => item.name === name)[0];
			return {
				...state,
				currentApp: currentApp
			};
		},

		initUserMenuTree(state, { payload }) {
			let { customTree } = state;
			customTree = payload.data || {};
			window.localStorage.setItem("customTree", JSON.stringify(customTree));
			return {
				...state,
				customTree: customTree,
				menuTreeReady: true
			};
		}
	},

	subscriptions: {
		setup({ dispatch, history }) {
			return history.listen(({ pathname, search }) => {
				if (typeof window.ga !== "undefined") {
					window.ga("send", "pageview", pathname + search);
				}
				// let list = [];
				// let needApp = list.find(item => location.pathname.includes(item) === true);
				dispatch({
					type: "setAttrValue",
					payload: {
						showApp: !!isAppListVisible(location.pathname) // needApp
					}
				});
			});
		}
	}
};
