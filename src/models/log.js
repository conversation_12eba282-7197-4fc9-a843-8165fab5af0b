// import { message } from "antd";
// import { logAPI } from "@/services";
import { compare, multipleToNull } from "@/utils/modelOperator";

export default {
	namespace: "log",
	state: {
		// logList: [],
		// total: 0,
		menuMap: []
		// searchParams: {
		// 	curPage: 1,
		// 	pageSize: 10,
		// 	startTime: null,
		// 	endTime: null,
		// 	account: null, // 用户账号（操作人）
		// 	menuUuid: [], // 菜单
		// 	orderType: "desc",
		// 	orderBy: "operationTime",
		// 	description: null // 业务描述
		// }
	},
	effects: {
		// *getLogList({ payload }, { call, put }) {
		// 	let response = yield call(logAPI.getLogList, payload);
		// 	if (!response) {
		// 		return;
		// 	}
		// 	if (!response.success) {
		// 		message.error(response.message);
		// 		return;
		// 	}
		// 	yield put({
		// 		type: "initLogList",
		// 		payload: response
		// 	});
		// }
	},
	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 用于在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = multipleToNull(stateChange[key]);
						}
					}
				}
				return stateChange;
			})(Object.assign({}, state), payload);
		},
		initLogList(state, { payload }) {
			state["logList"] = payload.data.userOperationList || [];
			state["total"] = payload.data.total || 0;
			return {
				...state
			};
		}
	}
};
