#!/bin/bash
echo "1. 编译项目"
# sudo npm run build:default

echo "2. 线上备份"
ssh -tt tdops@************ << remoteScript
cp -r /data01/mingmo/model-react/bifrost-resource/ /data01/mingmo/model-react/bifrost-resource-20210601
rm -rf /data01/mingmo/model-react/bifrost-resource/*
exit
remoteScript

echo "3. 替换文件"
scp -r ../dist/bifrost-resource/* tdops@************:/data01/mingmo/model-react/bifrost-resource/

